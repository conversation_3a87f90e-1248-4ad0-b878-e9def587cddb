name: CI/CD Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18.x'
  CACHE_KEY_PREFIX: 'faafo-v1'

jobs:
  # Job 1: Code Quality & Linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Run ESLint
        working-directory: ./faafo-career-platform
        run: npm run lint || true
        
      - name: Run TypeScript check
        working-directory: ./faafo-career-platform
        run: npx tsc --noEmit || true

  # Job 2: Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        test-group: [unit, architecture, performance]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Setup test environment
        working-directory: ./faafo-career-platform
        run: |
          cp .env.test .env.local
          echo "NODE_ENV=test" >> .env.local
          
      - name: Run unit tests
        working-directory: ./faafo-career-platform
        run: npm run test:${{ matrix.test-group }}
        env:
          NODE_ENV: test
          CI: true
          COVERAGE_PROFILE: ci
          COVERAGE_VALIDATION: true
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.test-group }}
          path: |
            faafo-career-platform/test-results/
            faafo-career-platform/coverage/
          retention-days: 7

  # Job 3: Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: faafo_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Setup test database
        working-directory: ./faafo-career-platform
        run: |
          cp .env.test .env.local
          echo "DATABASE_URL=postgresql://test_user:test_password@localhost:5432/faafo_test" >> .env.local
          echo "REDIS_URL=redis://localhost:6379/1" >> .env.local
          echo "NODE_ENV=test" >> .env.local
          
      - name: Run database migrations
        working-directory: ./faafo-career-platform
        run: npx prisma migrate deploy || npx prisma db push
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/faafo_test
          
      - name: Generate Prisma client
        working-directory: ./faafo-career-platform
        run: npx prisma generate
        
      - name: Run integration tests
        working-directory: ./faafo-career-platform
        run: npm run test:integration
        env:
          NODE_ENV: test
          CI: true
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/faafo_test
          REDIS_URL: redis://localhost:6379/1
          
      - name: Upload integration test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: integration-test-results
          path: |
            faafo-career-platform/test-results/
            faafo-career-platform/coverage/
          retention-days: 7

  # Job 4: Build Test
  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Setup build environment
        working-directory: ./faafo-career-platform
        run: |
          cp .env.test .env.local
          echo "NODE_ENV=production" >> .env.local
          
      - name: Generate Prisma client
        working-directory: ./faafo-career-platform
        run: npx prisma generate
        
      - name: Build application
        working-directory: ./faafo-career-platform
        run: npm run build
        env:
          NODE_ENV: production
          CI: true
          
      - name: Upload build artifacts
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            faafo-career-platform/.next/
            faafo-career-platform/dist/
          retention-days: 3

  # Job 5: Coverage Report & Quality Gates
  coverage-report:
    name: Coverage Report & Quality Gates
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: always()
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download test artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: '*test-results*'
          merge-multiple: true
          path: ./test-results
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Run quality gates and coverage validation
        working-directory: ./faafo-career-platform
        run: npm run test:quality-gates
        env:
          NODE_ENV: test
          CI: true
          COVERAGE_PROFILE: ci
          COVERAGE_VALIDATION: true

      - name: Generate production coverage report
        working-directory: ./faafo-career-platform
        run: npm run test:coverage:production || true
        env:
          NODE_ENV: production
          CI: true
          COVERAGE_PROFILE: production

      - name: Upload coverage to Codecov
        if: success()
        uses: codecov/codecov-action@v4
        with:
          file: ./faafo-career-platform/coverage/ci/lcov.info
          flags: unittests
          name: faafo-coverage
          fail_ci_if_error: true

      - name: Upload coverage artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: |
            faafo-career-platform/coverage/
            faafo-career-platform/test-results/
          retention-days: 30

  # Job 6: Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Run security audit
        working-directory: ./faafo-career-platform
        run: npm audit --audit-level=moderate || true
        
      - name: Run dependency check
        working-directory: ./faafo-career-platform
        run: npx audit-ci --moderate || true
