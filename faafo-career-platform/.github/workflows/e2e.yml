name: End-to-End Tests

on:
  schedule:
    # Run E2E tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to test'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      browser:
        description: 'Browser to test'
        required: true
        default: 'chromium'
        type: choice
        options:
          - chromium
          - firefox
          - webkit

env:
  NODE_VERSION: '18.x'

jobs:
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: faafo_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Install Playwright browsers
        working-directory: ./faafo-career-platform
        run: npx playwright install --with-deps ${{ github.event.inputs.browser || 'chromium' }}
        
      - name: Setup test environment
        working-directory: ./faafo-career-platform
        run: |
          cp .env.test .env.local
          echo "DATABASE_URL=postgresql://test_user:test_password@localhost:5432/faafo_test" >> .env.local
          echo "REDIS_URL=redis://localhost:6379/1" >> .env.local
          echo "NODE_ENV=test" >> .env.local
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env.local
          
      - name: Setup test database
        working-directory: ./faafo-career-platform
        run: |
          npx prisma migrate deploy || npx prisma db push
          npx prisma generate
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/faafo_test
          
      - name: Build application
        working-directory: ./faafo-career-platform
        run: npm run build
        env:
          NODE_ENV: production
          CI: true
          
      - name: Start application
        working-directory: ./faafo-career-platform
        run: |
          npm run start &
          sleep 30
          curl -f http://localhost:3000 || exit 1
        env:
          NODE_ENV: production
          PORT: 3000
          
      - name: Run E2E tests
        working-directory: ./faafo-career-platform
        run: npm run test:e2e
        env:
          NODE_ENV: test
          CI: true
          PLAYWRIGHT_BROWSER: ${{ github.event.inputs.browser || 'chromium' }}
          
      - name: Upload E2E test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-results-${{ github.event.inputs.browser || 'chromium' }}
          path: |
            faafo-career-platform/test-results/
            faafo-career-platform/playwright-report/
          retention-days: 7
          
      - name: Upload screenshots on failure
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-screenshots-${{ github.event.inputs.browser || 'chromium' }}
          path: faafo-career-platform/test-results/
          retention-days: 7

  # Performance testing job
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: github.event.inputs.environment == 'staging' || github.event_name == 'schedule'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Run performance tests
        working-directory: ./faafo-career-platform
        run: npm run test:performance || true
        env:
          NODE_ENV: test
          CI: true
          
      - name: Upload performance results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: performance-test-results
          path: |
            faafo-career-platform/test-results/
            faafo-career-platform/performance-reports/
          retention-days: 14

  # Accessibility testing job
  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Install Playwright browsers
        working-directory: ./faafo-career-platform
        run: npx playwright install --with-deps chromium
        
      - name: Build application
        working-directory: ./faafo-career-platform
        run: npm run build
        env:
          NODE_ENV: production
          CI: true
          
      - name: Start application
        working-directory: ./faafo-career-platform
        run: |
          npm run start &
          sleep 30
          curl -f http://localhost:3000 || exit 1
        env:
          NODE_ENV: production
          PORT: 3000
          
      - name: Run accessibility tests
        working-directory: ./faafo-career-platform
        run: npm run test:accessibility || true
        env:
          NODE_ENV: test
          CI: true
          
      - name: Upload accessibility results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: accessibility-test-results
          path: |
            faafo-career-platform/test-results/
            faafo-career-platform/accessibility-reports/
          retention-days: 7
