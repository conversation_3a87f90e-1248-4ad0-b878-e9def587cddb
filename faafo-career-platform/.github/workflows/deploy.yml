name: Deploy

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip tests before deployment'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Pre-deployment tests
  pre-deploy-tests:
    name: Pre-deployment Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: ${{ !github.event.inputs.skip_tests }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'faafo-career-platform/package-lock.json'
          
      - name: Install dependencies
        working-directory: ./faafo-career-platform
        run: npm ci --prefer-offline --no-audit
        
      - name: Run critical tests
        working-directory: ./faafo-career-platform
        run: |
          npm run test:unit
          npm run build
        env:
          NODE_ENV: test
          CI: true

  # Build and push Docker image
  build-image:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [pre-deploy-tests]
    if: always() && (needs.pre-deploy-tests.result == 'success' || needs.pre-deploy-tests.result == 'skipped')
    timeout-minutes: 15
    
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
            
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./faafo-career-platform
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: Output image
        id: image
        run: echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-image]
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    timeout-minutes: 10
    environment: staging
    
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying ${{ needs.build-image.outputs.image }} to staging"
          # Add your staging deployment commands here
          # Example: kubectl set image deployment/faafo-app faafo-app=${{ needs.build-image.outputs.image }}
          
      - name: Run smoke tests
        run: |
          echo "Running smoke tests against staging"
          # Add smoke test commands here
          # Example: curl -f https://staging.faafo.com/health

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-image, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    timeout-minutes: 15
    environment: production
    
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying ${{ needs.build-image.outputs.image }} to production"
          # Add your production deployment commands here
          # Example: kubectl set image deployment/faafo-app faafo-app=${{ needs.build-image.outputs.image }}
          
      - name: Run production smoke tests
        run: |
          echo "Running smoke tests against production"
          # Add production smoke test commands here
          # Example: curl -f https://faafo.com/health
          
      - name: Create GitHub release
        if: startsWith(github.ref, 'refs/tags/v')
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false

  # Rollback job (manual trigger only)
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && github.event_name == 'workflow_dispatch'
    timeout-minutes: 10
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Rollback deployment
        run: |
          echo "Rolling back deployment in ${{ github.event.inputs.environment }}"
          # Add rollback commands here
          # Example: kubectl rollout undo deployment/faafo-app
          
      - name: Verify rollback
        run: |
          echo "Verifying rollback in ${{ github.event.inputs.environment }}"
          # Add verification commands here

  # Notification job
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    timeout-minutes: 5
    
    steps:
      - name: Notify on success
        if: needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success'
        run: |
          echo "Deployment successful!"
          # Add success notification commands here
          # Example: Send Slack notification, email, etc.
          
      - name: Notify on failure
        if: needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure'
        run: |
          echo "Deployment failed!"
          # Add failure notification commands here
          # Example: Send alert to monitoring system
