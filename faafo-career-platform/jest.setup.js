require('@testing-library/jest-dom');

// Import memory leak fixes
const { setupJestMemoryFixes } = require('./test-utils/memory-leak-fixes');

// Setup memory leak prevention
setupJestMemoryFixes();

// Load test environment variables from .env.test
const path = require('path');
const dotenv = require('dotenv');

// Load test environment
dotenv.config({ path: path.resolve(__dirname, '.env.test') });

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: function(query) {
    return {
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    };
  },
});

// Lightweight NextAuth mocks
jest.mock('next-auth/providers/credentials', () => jest.fn(() => ({
  id: 'credentials',
  name: 'Credentials',
  type: 'credentials',
  authorize: jest.fn(),
})));

jest.mock('next-auth/providers/email', () => jest.fn(() => ({
  id: 'email',
  name: 'Email',
  type: 'email',
})));

jest.mock('next-auth', () => ({
  default: jest.fn(() => ({
    handlers: { GET: jest.fn(), POST: jest.fn() },
    auth: jest.fn(),
  })),
  getServerSession: jest.fn(),
}));

// Create a mock useSession that can be controlled in tests
const mockUseSession = jest.fn(() => ({
  data: null,
  status: 'unauthenticated',
  update: jest.fn(),
}));

jest.mock('next-auth/react', () => ({
  useSession: mockUseSession,
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
  SessionProvider: ({ children, session }) => {
    // Update the mock based on the session prop
    if (session) {
      mockUseSession.mockReturnValue({
        data: session,
        status: 'authenticated',
        update: jest.fn(),
      });
    }
    return children;
  },
}));

// Make mockUseSession available globally for tests
global.mockUseSession = mockUseSession;

// Lightweight Prisma mock
const createMockModel = () => ({
  findUnique: jest.fn(),
  findMany: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
});

const mockPrisma = new Proxy({}, {
  get: (target, prop) => {
    if (prop === '$transaction') return jest.fn();
    if (prop === '$connect') return jest.fn();
    if (prop === '$disconnect') return jest.fn();
    return createMockModel();
  }
});

jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: mockPrisma,
}));

// Mock useAuthState hook to prevent React Act warnings - removed for now to isolate issues

// Mock navigation hooks to prevent errors
jest.mock('@/hooks/useNavigationState', () => ({
  useNavigationState: jest.fn(() => ({
    isMobileMenuOpen: false,
    isToolsDropdownOpen: false,
    toggleMobileMenu: jest.fn(),
    closeMobileMenu: jest.fn(),
    toggleToolsDropdown: jest.fn(),
    closeToolsDropdown: jest.fn(),
  })),
  useNavigationShortcuts: jest.fn(),
}));

// Mock theme hook
jest.mock('next-themes', () => ({
  useTheme: jest.fn(() => ({
    theme: 'dark',
    setTheme: jest.fn(),
    resolvedTheme: 'dark',
    themes: ['light', 'dark'],
    systemTheme: 'dark',
  })),
  ThemeProvider: ({ children }) => children,
}));

// Essential mocks only
jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Essential global mocks
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Duplicate window.matchMedia mock removed - already defined above

global.fetch = jest.fn(() => Promise.resolve({
  ok: true,
  json: () => Promise.resolve({}),
}));
// Environment setup
process.env.NODE_ENV = 'test';
process.env.NEXTAUTH_URL = 'http://localhost:3000';
process.env.NEXTAUTH_SECRET = 'test-secret';

// Test cleanup
beforeEach(() => {
  jest.clearAllMocks();
});



