/**
 * Mock for @radix-ui/react-switch
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Switch Root component
const Root = React.forwardRef(({ 
  checked, 
  defaultChecked, 
  onCheckedChange, 
  disabled, 
  className, 
  children,
  ...props 
}, ref) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);
  
  const handleChange = () => {
    if (disabled) return;
    
    const newChecked = !internalChecked;
    setInternalChecked(newChecked);
    
    if (onCheckedChange) {
      onCheckedChange(newChecked);
    }
  };

  return (
    <button
      ref={ref}
      type="button"
      role="switch"
      aria-checked={internalChecked}
      disabled={disabled}
      className={className}
      data-testid="switch-root"
      data-state={internalChecked ? 'checked' : 'unchecked'}
      onClick={handleChange}
      {...props}
    >
      {children}
    </button>
  );
});

// Mock Switch Thumb component
const Thumb = React.forwardRef(({ className, ...props }, ref) => (
  <span
    ref={ref}
    className={className}
    data-testid="switch-thumb"
    {...props}
  />
));

// Set display names
Root.displayName = 'Switch';
Thumb.displayName = 'SwitchThumb';

module.exports = {
  Root,
  Thumb,
};
