/**
 * Mock for @radix-ui/react-separator
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Separator Root component
const Root = React.forwardRef(({ 
  orientation = 'horizontal',
  decorative = true,
  className, 
  ...props 
}, ref) => (
  <div
    ref={ref}
    role={decorative ? 'none' : 'separator'}
    aria-orientation={orientation}
    className={className}
    data-testid="separator-root"
    data-orientation={orientation}
    {...props}
  />
));

// Set display name
Root.displayName = 'Separator';

module.exports = {
  Root,
};
