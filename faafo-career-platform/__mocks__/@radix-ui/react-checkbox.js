/**
 * Mock for @radix-ui/react-checkbox
 * Provides simplified implementations for testing
 */

const React = require('react');

// Mock Checkbox Root component
const Root = React.forwardRef(({ 
  checked, 
  defaultChecked, 
  onCheckedChange, 
  disabled, 
  className, 
  children,
  ...props 
}, ref) => {
  const [internalChecked, setInternalChecked] = React.useState(defaultChecked || checked || false);
  
  const handleChange = () => {
    if (disabled) return;
    
    const newChecked = !internalChecked;
    setInternalChecked(newChecked);
    
    if (onCheckedChange) {
      onCheckedChange(newChecked);
    }
  };

  return (
    <button
      ref={ref}
      type="button"
      role="checkbox"
      aria-checked={internalChecked}
      disabled={disabled}
      className={className}
      data-testid="checkbox-root"
      data-state={internalChecked ? 'checked' : 'unchecked'}
      onClick={handleChange}
      {...props}
    >
      {children}
    </button>
  );
});

// Mock Checkbox Indicator component
const Indicator = React.forwardRef(({ className, children, ...props }, ref) => (
  <span
    ref={ref}
    className={className}
    data-testid="checkbox-indicator"
    {...props}
  >
    {children}
  </span>
));

// Set display names
Root.displayName = 'Checkbox';
Indicator.displayName = 'CheckboxIndicator';

module.exports = {
  Root,
  Indicator,
};
