import React from 'react';

export const FeedbackManager = ({ children, messages, onDismiss, className, ...props }: any) => (
  <div className={className} data-testid="feedback-manager">
    {messages && messages.map((message: any, index: number) => (
      <div key={index} data-testid={`feedback-message-${index}`}>
        {message.message || message}
      </div>
    ))}
    {children}
  </div>
);

export const FormErrorDisplay = ({ errors, className, ...props }: any) => (
  <div className={className} data-testid="form-error-display">
    {errors && typeof errors === 'object' && Object.entries(errors).map(([field, error]: [string, any]) => (
      <div key={field} data-testid={`error-${field}`}>
        {error}
      </div>
    ))}
  </div>
);
