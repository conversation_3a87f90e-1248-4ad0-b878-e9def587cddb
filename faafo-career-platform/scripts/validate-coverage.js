#!/usr/bin/env node

/**
 * Coverage Validation Script
 * Validates coverage results against quality gates and thresholds
 */

const fs = require('fs');
const path = require('path');
const coverageConfig = require('../jest.coverage.config.js');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(colors.green, `✅ ${message}`);
}

function logError(message) {
  log(colors.red, `❌ ${message}`);
}

function logWarning(message) {
  log(colors.yellow, `⚠️  ${message}`);
}

function logInfo(message) {
  log(colors.blue, `ℹ️  ${message}`);
}

/**
 * Main validation function
 */
async function validateCoverage() {
  try {
    log(colors.bold, '\n🔍 COVERAGE VALIDATION STARTING');
    log(colors.bold, '=====================================');
    
    const profile = coverageConfig.getCurrentProfile();
    logInfo(`Coverage Profile: ${profile}`);
    logInfo(`Environment: ${process.env.NODE_ENV || 'development'}`);
    logInfo(`CI Mode: ${process.env.CI === 'true' ? 'Yes' : 'No'}`);
    
    // Find coverage files
    const coverageFiles = findCoverageFiles();
    
    if (coverageFiles.length === 0) {
      logError('No coverage files found. Run tests with coverage first.');
      process.exit(1);
    }
    
    logInfo(`Found ${coverageFiles.length} coverage file(s)`);
    
    let allPassed = true;
    const results = [];
    
    // Validate each coverage file
    for (const coverageFile of coverageFiles) {
      const result = await validateCoverageFile(coverageFile);
      results.push(result);
      
      if (!result.passed) {
        allPassed = false;
      }
    }
    
    // Generate summary report
    generateSummaryReport(results, allPassed);
    
    if (!allPassed) {
      logError('\nCoverage validation FAILED. Quality gates not met.');
      process.exit(1);
    } else {
      logSuccess('\nCoverage validation PASSED. All quality gates met.');
      process.exit(0);
    }
    
  } catch (error) {
    logError(`Validation failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

/**
 * Find all coverage summary files
 */
function findCoverageFiles() {
  const coverageFiles = [];
  const coverageDirs = ['coverage', 'coverage/ci', 'coverage/unit'];
  
  for (const dir of coverageDirs) {
    const summaryPath = path.join(process.cwd(), dir, 'coverage-summary.json');
    if (fs.existsSync(summaryPath)) {
      coverageFiles.push({
        path: summaryPath,
        type: dir.includes('ci') ? 'ci' : dir.includes('unit') ? 'unit' : 'default'
      });
    }
  }
  
  return coverageFiles;
}

/**
 * Validate a single coverage file
 */
async function validateCoverageFile(coverageFile) {
  try {
    logInfo(`\nValidating: ${coverageFile.path}`);
    
    const coverageData = JSON.parse(fs.readFileSync(coverageFile.path, 'utf8'));
    const validation = coverageConfig.validateCoverage(coverageData);
    
    const result = {
      file: coverageFile.path,
      type: coverageFile.type,
      passed: validation.passed,
      errors: validation.errors,
      warnings: validation.warnings,
      coverage: coverageData.total
    };
    
    if (validation.passed) {
      logSuccess(`${coverageFile.type} coverage validation passed`);
    } else {
      logError(`${coverageFile.type} coverage validation failed`);
      validation.errors.forEach(error => logError(`  • ${error}`));
    }
    
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => logWarning(`  • ${warning}`));
    }
    
    // Log coverage metrics
    if (coverageData.total) {
      logInfo(`  Coverage metrics:`);
      Object.entries(coverageData.total).forEach(([metric, data]) => {
        if (data && typeof data.pct === 'number') {
          const color = data.pct >= 90 ? colors.green : data.pct >= 75 ? colors.yellow : colors.red;
          log(color, `    ${metric}: ${data.pct}%`);
        }
      });
    }
    
    return result;
    
  } catch (error) {
    logError(`Failed to validate ${coverageFile.path}: ${error.message}`);
    return {
      file: coverageFile.path,
      type: coverageFile.type,
      passed: false,
      errors: [error.message],
      warnings: [],
      coverage: null
    };
  }
}

/**
 * Generate summary report
 */
function generateSummaryReport(results, allPassed) {
  log(colors.bold, '\n📊 COVERAGE VALIDATION SUMMARY');
  log(colors.bold, '================================');
  
  const totalFiles = results.length;
  const passedFiles = results.filter(r => r.passed).length;
  const failedFiles = totalFiles - passedFiles;
  
  logInfo(`Total files validated: ${totalFiles}`);
  logSuccess(`Passed: ${passedFiles}`);
  
  if (failedFiles > 0) {
    logError(`Failed: ${failedFiles}`);
  }
  
  // Show overall status
  if (allPassed) {
    log(colors.green + colors.bold, '\n🎉 ALL QUALITY GATES PASSED!');
  } else {
    log(colors.red + colors.bold, '\n💥 QUALITY GATES FAILED!');
  }
  
  // Show profile information
  const profile = coverageConfig.getCurrentProfile();
  const thresholds = coverageConfig.getCoverageThreshold();
  
  logInfo(`\nProfile: ${profile}`);
  logInfo(`Global thresholds: ${JSON.stringify(thresholds.global, null, 2)}`);
  
  // Generate detailed report file
  const reportPath = path.join(process.cwd(), 'coverage-validation-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    profile,
    passed: allPassed,
    summary: {
      total: totalFiles,
      passed: passedFiles,
      failed: failedFiles
    },
    results,
    thresholds: thresholds.global
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);
}

// Run validation if called directly
if (require.main === module) {
  validateCoverage();
}

module.exports = {
  validateCoverage,
  validateCoverageFile,
  findCoverageFiles
};
