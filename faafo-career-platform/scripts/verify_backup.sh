#!/bin/bash

# Load environment variables (e.g., from .env file if available)
if [ -f .env ]; then
  set -a
  source .env
  set +a
fi

# Database credentials
DB_USER="${POSTGRES_USER}"
DB_PASSWORD="${POSTGRES_PASSWORD}"
DB_HOST="${POSTGRES_HOST:-localhost}"
DB_PORT="${POSTGRES_PORT:-5432}"
MAIN_DB_NAME="${POSTGRES_DB}"
TEMP_DB_NAME="${MAIN_DB_NAME}_test_restore"

# Backup directory
BACKUP_DIR="./backups"

# Find the latest backup file
LATEST_BACKUP=$(ls -t "${BACKUP_DIR}"/faafo_db_backup_*.sql | head -n 1)

if [ -z "${LATEST_BACKUP}" ]; then
  echo "Error: No backup files found in ${BACKUP_DIR}"
  exit 1
fi

echo "Found latest backup: ${LATEST_BACKUP}"

# Restore the backup to the temporary database
echo "Restoring backup '${LATEST_BACKUP}' to temporary database '${TEMP_DB_NAME}'..."
PGPASSWORD="${DB_PASSWORD}" createdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" "${TEMP_DB_NAME}"
if [ $? -ne 0 ]; then
  echo "Error: Failed to create temporary database '${TEMP_DB_NAME}'."
  exit 1
fi

PGPASSWORD="${DB_PASSWORD}" pg_restore -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${TEMP_DB_NAME}" "${LATEST_BACKUP}"

# Check if the restoration was successful
if [ $? -eq 0 ]; then
  echo "Backup verification successful: '${LATEST_BACKUP}' was restored to '${TEMP_DB_NAME}'."
else
  echo "Error: Backup verification failed. Could not restore '${LATEST_BACKUP}' to '${TEMP_DB_NAME}'."
fi

# Clean up: Drop the temporary database
echo "Cleaning up: Dropping temporary database '${TEMP_DB_NAME}'..."
PGPASSWORD="${DB_PASSWORD}" dropdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" "${TEMP_DB_NAME}"
if [ $? -eq 0 ]; then
  echo "Temporary database '${TEMP_DB_NAME}' dropped successfully."
else
  echo "Error: Failed to drop temporary database '${TEMP_DB_NAME}'."
fi

exit 0 