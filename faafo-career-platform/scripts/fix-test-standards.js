#!/usr/bin/env node

/**
 * Test Standards Fix Script
 * 
 * Automatically fixes common test naming and documentation issues
 * identified by the analyze-test-standards.js script.
 */

const fs = require('fs');
const path = require('path');

class TestStandardsFixer {
  constructor() {
    this.fixes = {
      renamed: [],
      documented: [],
      errors: []
    };
  }

  /**
   * Main fix function
   */
  async fix() {
    console.log('🔧 Fixing Test Standards Issues...\n');
    
    const testDir = path.join(process.cwd(), '__tests__');
    await this.processDirectory(testDir);
    this.generateReport();
  }

  /**
   * Process directory recursively
   */
  async processDirectory(dir) {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await this.processDirectory(filePath);
      } else if (this.isTestFile(file)) {
        await this.processTestFile(filePath);
      }
    }
  }

  /**
   * Check if file is a test file
   */
  isTestFile(filename) {
    return /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(filename);
  }

  /**
   * Process individual test file
   */
  async processTestFile(filePath) {
    try {
      const originalPath = filePath;
      const filename = path.basename(filePath);
      const dir = path.dirname(filePath);

      // Fix naming conventions
      const newFilename = this.fixFilename(filename, filePath);
      if (newFilename !== filename) {
        const newPath = path.join(dir, newFilename);
        fs.renameSync(filePath, newPath);
        this.fixes.renamed.push({
          from: path.relative(process.cwd(), originalPath),
          to: path.relative(process.cwd(), newPath)
        });
        filePath = newPath; // Update path for documentation fixes
      }

      // Fix documentation
      await this.fixDocumentation(filePath);

    } catch (error) {
      this.fixes.errors.push({
        file: path.relative(process.cwd(), filePath),
        error: error.message
      });
    }
  }

  /**
   * Fix filename to follow conventions
   */
  fixFilename(filename, fullPath) {
    let newFilename = filename;

    // Convert PascalCase/camelCase to kebab-case
    const nameWithoutExt = filename.replace(/\.(test|spec)\.(ts|tsx|js|jsx)$/, '');
    const extension = filename.match(/\.(test|spec)\.(ts|tsx|js|jsx)$/)[0];

    // Convert to kebab-case
    let kebabName = nameWithoutExt
      .replace(/([A-Z])/g, '-$1')  // Add dash before capitals
      .toLowerCase()               // Convert to lowercase
      .replace(/^-/, '')           // Remove leading dash
      .replace(/_/g, '-');         // Replace underscores with dashes

    // Fix E2E test extensions
    const isE2E = fullPath.includes('/e2e/') || fullPath.includes('e2e') || 
                  kebabName.includes('e2e') || kebabName.includes('user-flows');
    
    if (isE2E && extension.includes('.test.')) {
      newFilename = kebabName + extension.replace('.test.', '.spec.');
    } else if (!isE2E && extension.includes('.spec.')) {
      newFilename = kebabName + extension.replace('.spec.', '.test.');
    } else {
      newFilename = kebabName + extension;
    }

    return newFilename;
  }

  /**
   * Fix documentation in test file
   */
  async fixDocumentation(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check if file already has JSDoc documentation
      if (content.includes('/**') && content.match(/\/\*\*[\s\S]*?\*\//)) {
        return; // Already documented
      }

      const filename = path.basename(filePath);
      const relativePath = path.relative(process.cwd(), filePath);
      
      // Generate documentation based on file type and name
      const documentation = this.generateDocumentation(filename, relativePath, content);
      
      // Insert documentation at the top of the file
      const newContent = documentation + '\n\n' + content;
      fs.writeFileSync(filePath, newContent, 'utf8');
      
      this.fixes.documented.push(relativePath);

    } catch (error) {
      this.fixes.errors.push({
        file: path.relative(process.cwd(), filePath),
        error: `Documentation fix failed: ${error.message}`
      });
    }
  }

  /**
   * Generate appropriate documentation for test file
   */
  generateDocumentation(filename, relativePath, content) {
    const nameWithoutExt = filename.replace(/\.(test|spec)\.(ts|tsx|js|jsx)$/, '');
    const isComponent = filename.includes('.tsx') || relativePath.includes('/components/');
    const isIntegration = relativePath.includes('/integration/') || filename.includes('integration');
    const isE2E = relativePath.includes('/e2e/') || filename.includes('.spec.');
    const isPerformance = relativePath.includes('/performance/') || filename.includes('performance');
    const isSecurity = relativePath.includes('/security/') || filename.includes('security');
    const isAPI = relativePath.includes('/api/') || filename.includes('api');

    // Determine test category
    let category = 'unit';
    if (isE2E) category = 'e2e';
    else if (isIntegration) category = 'integration';
    else if (isPerformance) category = 'performance';
    else if (isSecurity) category = 'security';

    // Generate title
    const title = nameWithoutExt
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    // Generate description based on type
    let description = '';
    let requirements = '';

    if (isComponent) {
      description = `Tests ${title} component functionality, rendering, user interactions, and edge cases.`;
      requirements = 'React Testing Library, component mocking';
    } else if (isAPI) {
      description = `Tests ${title} API endpoints, request/response handling, validation, and error scenarios.`;
      requirements = 'API mocking, request simulation';
    } else if (isIntegration) {
      description = `Integration tests for ${title} covering end-to-end workflows and system interactions.`;
      requirements = 'Database setup, service mocking';
    } else if (isE2E) {
      description = `End-to-end tests for ${title} covering complete user workflows and browser interactions.`;
      requirements = 'Playwright, browser automation';
    } else if (isPerformance) {
      description = `Performance tests for ${title} measuring response times, throughput, and resource usage.`;
      requirements = 'Performance monitoring, load testing';
    } else if (isSecurity) {
      description = `Security tests for ${title} validating authentication, authorization, and vulnerability protection.`;
      requirements = 'Security testing utilities, mock authentication';
    } else {
      description = `Tests ${title} functionality, business logic, and edge cases.`;
      requirements = 'Unit testing utilities, mocking';
    }

    return `/**
 * ${title} Tests
 * 
 * ${description}
 * 
 * @category ${category}
 * @requires ${requirements}
 */`;
  }

  /**
   * Generate fix report
   */
  generateReport() {
    console.log('📊 Test Standards Fix Report');
    console.log('============================\n');

    console.log(`✅ Files renamed: ${this.fixes.renamed.length}`);
    console.log(`📚 Files documented: ${this.fixes.documented.length}`);
    console.log(`❌ Errors encountered: ${this.fixes.errors.length}\n`);

    if (this.fixes.renamed.length > 0) {
      console.log('📝 Renamed Files:');
      this.fixes.renamed.forEach(fix => {
        console.log(`  ${fix.from} → ${fix.to}`);
      });
      console.log();
    }

    if (this.fixes.documented.length > 0) {
      console.log('📚 Added Documentation:');
      this.fixes.documented.slice(0, 10).forEach(file => {
        console.log(`  ✓ ${file}`);
      });
      if (this.fixes.documented.length > 10) {
        console.log(`  ... and ${this.fixes.documented.length - 10} more files`);
      }
      console.log();
    }

    if (this.fixes.errors.length > 0) {
      console.log('❌ Errors:');
      this.fixes.errors.forEach(error => {
        console.log(`  ${error.file}: ${error.error}`);
      });
      console.log();
    }

    console.log('🎯 Next Steps:');
    console.log('  1. Run tests to ensure all fixes work correctly');
    console.log('  2. Run analyze-test-standards.js to verify improvements');
    console.log('  3. Commit changes with descriptive message');
    console.log('  4. Update any import statements that reference renamed files');
  }
}

// Run fixes if called directly
if (require.main === module) {
  const fixer = new TestStandardsFixer();
  fixer.fix().catch(console.error);
}

module.exports = TestStandardsFixer;
