#!/usr/bin/env node

/**
 * Test Standards Analysis Script
 * 
 * Analyzes all test files for naming convention and documentation compliance
 * according to the test-naming-documentation-standards.md guidelines.
 */

const fs = require('fs');
const path = require('path');

class TestStandardsAnalyzer {
  constructor() {
    this.issues = [];
    this.testFiles = [];
    this.stats = {
      totalFiles: 0,
      namingIssues: 0,
      documentationIssues: 0,
      descriptionIssues: 0,
      compliantFiles: 0
    };
  }

  /**
   * Main analysis function
   */
  analyze() {
    console.log('🔍 Analyzing Test Standards Compliance...\n');
    
    const testDir = path.join(process.cwd(), '__tests__');
    this.findTestFiles(testDir);
    this.analyzeFiles();
    this.generateReport();
  }

  /**
   * Recursively find all test files
   */
  findTestFiles(dir) {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        this.findTestFiles(filePath);
      } else if (this.isTestFile(file)) {
        this.testFiles.push(filePath);
      }
    });
  }

  /**
   * Check if file is a test file
   */
  isTestFile(filename) {
    return /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(filename);
  }

  /**
   * Analyze all found test files
   */
  analyzeFiles() {
    this.stats.totalFiles = this.testFiles.length;

    this.testFiles.forEach(filePath => {
      const issues = this.analyzeFile(filePath);
      if (issues.length === 0) {
        this.stats.compliantFiles++;
      }
    });
  }

  /**
   * Analyze individual test file
   */
  analyzeFile(filePath) {
    const fileIssues = [];
    const filename = path.basename(filePath);
    const relativePath = path.relative(process.cwd(), filePath);

    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Check naming conventions
      const namingIssues = this.checkNamingConventions(filename, relativePath);
      fileIssues.push(...namingIssues);

      // Check documentation
      const docIssues = this.checkDocumentation(content, relativePath);
      fileIssues.push(...docIssues);

      // Check test descriptions
      const descIssues = this.checkTestDescriptions(content, relativePath);
      fileIssues.push(...descIssues);

      // Add to global issues
      this.issues.push(...fileIssues);

      return fileIssues;
    } catch (error) {
      const issue = {
        file: relativePath,
        type: 'error',
        severity: 'high',
        message: `Cannot read file: ${error.message}`
      };
      this.issues.push(issue);
      return [issue];
    }
  }

  /**
   * Check file naming conventions
   */
  checkNamingConventions(filename, relativePath) {
    const issues = [];

    // Check for proper extension
    if (!filename.match(/\.(test|spec)\.(ts|tsx|js|jsx)$/)) {
      issues.push({
        file: relativePath,
        type: 'naming',
        severity: 'high',
        message: 'File does not follow proper test extension pattern (.test.* or .spec.*)'
      });
    }

    // Check for kebab-case (allow dots and extensions)
    const nameWithoutExt = filename.replace(/\.(test|spec)\.(ts|tsx|js|jsx)$/, '');
    if (nameWithoutExt.includes('_') || /[A-Z]/.test(nameWithoutExt)) {
      issues.push({
        file: relativePath,
        type: 'naming',
        severity: 'medium',
        message: 'File name should use kebab-case (lowercase with hyphens)'
      });
    }

    // Check for descriptive naming (too short)
    if (nameWithoutExt.length < 3) {
      issues.push({
        file: relativePath,
        type: 'naming',
        severity: 'medium',
        message: 'File name is too short and not descriptive'
      });
    }

    // Check for mixed extensions (.test. vs .spec.)
    const isE2E = relativePath.includes('/e2e/') || relativePath.includes('e2e');
    if (isE2E && filename.includes('.test.')) {
      issues.push({
        file: relativePath,
        type: 'naming',
        severity: 'low',
        message: 'E2E tests should use .spec.ts extension'
      });
    } else if (!isE2E && filename.includes('.spec.')) {
      issues.push({
        file: relativePath,
        type: 'naming',
        severity: 'low',
        message: 'Non-E2E tests should use .test.ts extension'
      });
    }

    if (issues.length > 0) {
      this.stats.namingIssues++;
    }

    return issues;
  }

  /**
   * Check file-level documentation
   */
  checkDocumentation(content, relativePath) {
    const issues = [];

    // Check for file-level JSDoc comment
    const hasFileComment = content.includes('/**') && 
                          content.match(/\/\*\*[\s\S]*?\*\//);
    
    if (!hasFileComment) {
      issues.push({
        file: relativePath,
        type: 'documentation',
        severity: 'medium',
        message: 'Missing file-level JSDoc documentation'
      });
    }

    // Check for describe blocks
    const hasDescribeBlocks = content.includes('describe(');
    if (!hasDescribeBlocks) {
      issues.push({
        file: relativePath,
        type: 'documentation',
        severity: 'low',
        message: 'No describe blocks found (consider organizing tests)'
      });
    }

    if (issues.length > 0) {
      this.stats.documentationIssues++;
    }

    return issues;
  }

  /**
   * Check test descriptions quality
   */
  checkTestDescriptions(content, relativePath) {
    const issues = [];

    // Find all test descriptions
    const testMatches = content.match(/it\s*\(\s*['"`]([^'"`]+)['"`]/g) || [];
    
    testMatches.forEach(match => {
      const description = match.match(/['"`]([^'"`]+)['"`]/)[1];
      
      // Check for vague descriptions
      const vaguePatterns = [
        /^(works?|test|should work)$/i,
        /^(it works?|basic test)$/i,
        /^(test \w+)$/i
      ];

      if (vaguePatterns.some(pattern => pattern.test(description))) {
        issues.push({
          file: relativePath,
          type: 'description',
          severity: 'medium',
          message: `Vague test description: "${description}"`
        });
      }

      // Check for missing "should" in description
      if (!description.toLowerCase().includes('should') && description.length > 10) {
        issues.push({
          file: relativePath,
          type: 'description',
          severity: 'low',
          message: `Test description should use "should" statement: "${description}"`
        });
      }

      // Check for too short descriptions
      if (description.length < 10) {
        issues.push({
          file: relativePath,
          type: 'description',
          severity: 'low',
          message: `Test description too short: "${description}"`
        });
      }
    });

    if (issues.length > 0) {
      this.stats.descriptionIssues++;
    }

    return issues;
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    console.log('📊 Test Standards Analysis Report');
    console.log('================================\n');

    // Summary statistics
    console.log('📈 Summary Statistics:');
    console.log(`Total test files analyzed: ${this.stats.totalFiles}`);
    console.log(`Fully compliant files: ${this.stats.compliantFiles}`);
    console.log(`Files with naming issues: ${this.stats.namingIssues}`);
    console.log(`Files with documentation issues: ${this.stats.documentationIssues}`);
    console.log(`Files with description issues: ${this.stats.descriptionIssues}`);
    console.log(`Compliance rate: ${Math.round((this.stats.compliantFiles / this.stats.totalFiles) * 100)}%\n`);

    // Group issues by severity
    const issuesBySeverity = {
      high: this.issues.filter(i => i.severity === 'high'),
      medium: this.issues.filter(i => i.severity === 'medium'),
      low: this.issues.filter(i => i.severity === 'low')
    };

    // Report issues by severity
    ['high', 'medium', 'low'].forEach(severity => {
      const severityIssues = issuesBySeverity[severity];
      if (severityIssues.length > 0) {
        console.log(`🚨 ${severity.toUpperCase()} Priority Issues (${severityIssues.length}):`);
        severityIssues.forEach(issue => {
          console.log(`  📁 ${issue.file}`);
          console.log(`     ${this.getIssueIcon(issue.type)} ${issue.message}\n`);
        });
      }
    });

    // Recommendations
    this.generateRecommendations();
  }

  /**
   * Get icon for issue type
   */
  getIssueIcon(type) {
    const icons = {
      naming: '📝',
      documentation: '📚',
      description: '💬',
      error: '❌'
    };
    return icons[type] || '⚠️';
  }

  /**
   * Generate actionable recommendations
   */
  generateRecommendations() {
    console.log('💡 Recommendations:');
    console.log('==================\n');

    if (this.stats.namingIssues > 0) {
      console.log('📝 Naming Convention Fixes:');
      console.log('  - Rename files to use kebab-case');
      console.log('  - Use .test.ts for unit/integration tests');
      console.log('  - Use .spec.ts only for E2E tests');
      console.log('  - Make file names more descriptive\n');
    }

    if (this.stats.documentationIssues > 0) {
      console.log('📚 Documentation Improvements:');
      console.log('  - Add JSDoc comments to all test files');
      console.log('  - Include test purpose and requirements');
      console.log('  - Use describe blocks to organize tests');
      console.log('  - Document complex test setup\n');
    }

    if (this.stats.descriptionIssues > 0) {
      console.log('💬 Test Description Enhancements:');
      console.log('  - Use "should" statements for test names');
      console.log('  - Be specific about expected behavior');
      console.log('  - Avoid vague descriptions like "works" or "test"');
      console.log('  - Include both action and expected outcome\n');
    }

    console.log('🎯 Next Steps:');
    console.log('  1. Review high priority issues first');
    console.log('  2. Update test-naming-documentation-standards.md if needed');
    console.log('  3. Implement fixes systematically');
    console.log('  4. Run this analysis again to verify improvements');
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new TestStandardsAnalyzer();
  analyzer.analyze();
}

module.exports = TestStandardsAnalyzer;
