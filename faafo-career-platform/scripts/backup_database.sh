#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
  set -a
  source .env
  set +a
fi

# Database credentials (ensure these are set in your environment or .env file)
DB_USER="${POSTGRES_USER}"
DB_PASSWORD="${POSTGRES_PASSWORD}"
DB_NAME="${POSTGRES_DB}"
DB_HOST="${POSTGRES_HOST:-localhost}"
DB_PORT="${POSTGRES_PORT:-5432}"

# Backup directory
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/faafo_db_backup_${TIMESTAMP}.sql"

# Retention policy (in days)
RETENTION_DAYS=30

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Perform the backup
echo "Starting PostgreSQL backup for database '${DB_NAME}'..."
PGPASSWORD="${DB_PASSWORD}" pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -F c -b -v -f "${BACKUP_FILE}"

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "Database backup successful: ${BACKUP_FILE}"
else
  echo "Error: Database backup failed."
  exit 1
fi

# Clean up old backups
echo "Cleaning up backups older than ${RETENTION_DAYS} days..."
find "${BACKUP_DIR}" -type f -name "faafo_db_backup_*.sql" -mtime +"${RETENTION_DAYS}" -exec rm {} \;

echo "Old backups cleaned up." 