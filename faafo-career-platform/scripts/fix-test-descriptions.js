#!/usr/bin/env node

/**
 * Test Description Fix Script
 * 
 * Fixes test descriptions to use proper "should" statements
 * and improve clarity according to testing standards.
 */

const fs = require('fs');
const path = require('path');

class TestDescriptionFixer {
  constructor() {
    this.fixes = {
      updated: [],
      errors: []
    };
  }

  /**
   * Main fix function
   */
  async fix() {
    console.log('🔧 Fixing Test Descriptions...\n');
    
    const testDir = path.join(process.cwd(), '__tests__');
    await this.processDirectory(testDir);
    this.generateReport();
  }

  /**
   * Process directory recursively
   */
  async processDirectory(dir) {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await this.processDirectory(filePath);
      } else if (this.isTestFile(file)) {
        await this.processTestFile(filePath);
      }
    }
  }

  /**
   * Check if file is a test file
   */
  isTestFile(filename) {
    return /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(filename);
  }

  /**
   * Process individual test file
   */
  async processTestFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const updatedContent = this.fixTestDescriptions(content, filePath);
      
      if (updatedContent !== content) {
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        this.fixes.updated.push(path.relative(process.cwd(), filePath));
      }

    } catch (error) {
      this.fixes.errors.push({
        file: path.relative(process.cwd(), filePath),
        error: error.message
      });
    }
  }

  /**
   * Fix test descriptions in content
   */
  fixTestDescriptions(content, filePath) {
    let updatedContent = content;

    // Find all test descriptions and fix them
    const testPattern = /it\s*\(\s*['"`]([^'"`]+)['"`]/g;
    let match;
    const replacements = [];

    while ((match = testPattern.exec(content)) !== null) {
      const originalDesc = match[1];
      const fixedDesc = this.fixDescription(originalDesc);
      
      if (fixedDesc !== originalDesc) {
        replacements.push({
          original: match[0],
          fixed: match[0].replace(originalDesc, fixedDesc)
        });
      }
    }

    // Apply replacements
    replacements.forEach(replacement => {
      updatedContent = updatedContent.replace(replacement.original, replacement.fixed);
    });

    return updatedContent;
  }

  /**
   * Fix individual test description
   */
  fixDescription(description) {
    // Skip if already has "should"
    if (description.toLowerCase().includes('should')) {
      return description;
    }

    // Skip very short descriptions (likely placeholders)
    if (description.trim().length < 3) {
      return description;
    }

    // Skip descriptions that are just punctuation or whitespace
    if (/^[\s\n,.-]*$/.test(description)) {
      return description;
    }

    // Skip descriptions that start with "FIXED:" (security test patterns)
    if (description.startsWith('FIXED:') || description.startsWith('SECURITY')) {
      return description;
    }

    // Convert common patterns to "should" statements
    let fixed = description;

    // Pattern: "renders X" -> "should render X"
    if (/^renders?\s/i.test(fixed)) {
      fixed = fixed.replace(/^renders?\s/i, 'should render ');
    }
    // Pattern: "handles X" -> "should handle X"
    else if (/^handles?\s/i.test(fixed)) {
      fixed = fixed.replace(/^handles?\s/i, 'should handle ');
    }
    // Pattern: "displays X" -> "should display X"
    else if (/^displays?\s/i.test(fixed)) {
      fixed = fixed.replace(/^displays?\s/i, 'should display ');
    }
    // Pattern: "shows X" -> "should show X"
    else if (/^shows?\s/i.test(fixed)) {
      fixed = fixed.replace(/^shows?\s/i, 'should show ');
    }
    // Pattern: "allows X" -> "should allow X"
    else if (/^allows?\s/i.test(fixed)) {
      fixed = fixed.replace(/^allows?\s/i, 'should allow ');
    }
    // Pattern: "includes X" -> "should include X"
    else if (/^includes?\s/i.test(fixed)) {
      fixed = fixed.replace(/^includes?\s/i, 'should include ');
    }
    // Pattern: "formats X" -> "should format X"
    else if (/^formats?\s/i.test(fixed)) {
      fixed = fixed.replace(/^formats?\s/i, 'should format ');
    }
    // Pattern: "uses X" -> "should use X"
    else if (/^uses?\s/i.test(fixed)) {
      fixed = fixed.replace(/^uses?\s/i, 'should use ');
    }
    // Pattern: "fetches X" -> "should fetch X"
    else if (/^fetches?\s/i.test(fixed)) {
      fixed = fixed.replace(/^fetches?\s/i, 'should fetch ');
    }
    // Pattern: "switches X" -> "should switch X"
    else if (/^switches?\s/i.test(fixed)) {
      fixed = fixed.replace(/^switches?\s/i, 'should switch ');
    }
    // Pattern: "clears X" -> "should clear X"
    else if (/^clears?\s/i.test(fixed)) {
      fixed = fixed.replace(/^clears?\s/i, 'should clear ');
    }
    // Pattern: "requires X" -> "should require X"
    else if (/^requires?\s/i.test(fixed)) {
      fixed = fixed.replace(/^requires?\s/i, 'should require ');
    }
    // Pattern: "has X" -> "should have X"
    else if (/^has\s/i.test(fixed)) {
      fixed = fixed.replace(/^has\s/i, 'should have ');
    }
    // Generic pattern: add "should" at the beginning if it's a verb phrase
    else if (/^[a-z]/i.test(fixed) && !fixed.includes('should')) {
      // Only add "should" if it looks like a verb phrase
      const verbPatterns = [
        /^(render|display|show|handle|allow|include|format|use|fetch|switch|clear|require|have|contain|validate|check|verify|test|ensure|prevent|enable|disable|toggle|update|create|delete|save|load|submit|cancel|reset|refresh|retry|navigate|redirect|filter|sort|search|select|click|hover|focus|blur|scroll|drag|drop|type|press|release|open|close|expand|collapse|hide|reveal|animate|transition)/i
      ];
      
      if (verbPatterns.some(pattern => pattern.test(fixed))) {
        fixed = 'should ' + fixed.charAt(0).toLowerCase() + fixed.slice(1);
      }
    }

    return fixed;
  }

  /**
   * Generate fix report
   */
  generateReport() {
    console.log('📊 Test Description Fix Report');
    console.log('==============================\n');

    console.log(`✅ Files updated: ${this.fixes.updated.length}`);
    console.log(`❌ Errors encountered: ${this.fixes.errors.length}\n`);

    if (this.fixes.updated.length > 0) {
      console.log('📝 Updated Files:');
      this.fixes.updated.forEach(file => {
        console.log(`  ✓ ${file}`);
      });
      console.log();
    }

    if (this.fixes.errors.length > 0) {
      console.log('❌ Errors:');
      this.fixes.errors.forEach(error => {
        console.log(`  ${error.file}: ${error.error}`);
      });
      console.log();
    }

    console.log('🎯 Next Steps:');
    console.log('  1. Run tests to ensure all fixes work correctly');
    console.log('  2. Run analyze-test-standards.js to verify final improvements');
    console.log('  3. Review any remaining manual fixes needed');
    console.log('  4. Commit all standardization changes');
  }
}

// Run fixes if called directly
if (require.main === module) {
  const fixer = new TestDescriptionFixer();
  fixer.fix().catch(console.error);
}

module.exports = TestDescriptionFixer;
