#!/usr/bin/env node

/**
 * Audit System Component Testing
 * Tests the audit system components and functionality in isolation
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Audit System Component Testing');
console.log('=' .repeat(60));

let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testsPassed++;
  } else {
    testsFailed++;
  }
}

// Test 1: File Structure Validation
console.log('\n📁 Testing File Structure...');

const requiredFiles = [
  // Pages
  'src/app/audit/page.tsx',
  'src/app/audit/runs/page.tsx', 
  'src/app/audit/issues/page.tsx',
  'src/app/audit/issues/[id]/page.tsx',
  
  // Components
  'src/components/audit/AuditDashboard.tsx',
  'src/components/audit/AuditMetrics.tsx',
  'src/components/audit/RecentIssues.tsx',
  'src/components/audit/AuditRunsList.tsx',
  'src/components/audit/AuditIssuesPage.tsx',
  'src/components/audit/AuditIssueDetailPage.tsx',
  'src/components/audit/AuditRunsPage.tsx',
  
  // API Routes
  'src/app/api/audit/runs/route.ts',
  'src/app/api/audit/runs/[id]/route.ts',
  'src/app/api/audit/issues/route.ts',
  'src/app/api/audit/issues/[id]/route.ts',
  'src/app/api/audit/issues/[id]/comments/route.ts',
  
  // Core Engine
  'src/lib/audit/core-audit-engine.ts',
  'src/lib/audit/audit-service.ts',
  'src/lib/audit/types.ts',
  'src/lib/audit/index.ts',
  
  // Storage
  'src/lib/audit/storage/audit-storage.ts',
  
  // Analyzers
  'src/lib/audit/analyzers/typescript-analyzer.ts',
  'src/lib/audit/analyzers/eslint-analyzer.ts',
  'src/lib/audit/analyzers/security-analyzer.ts',
  'src/lib/audit/analyzers/performance-analyzer.ts',
  'src/lib/audit/analyzers/architecture-analyzer.ts',
  'src/lib/audit/analyzers/prisma-analyzer.ts',
  'src/lib/audit/analyzers/test-coverage-analyzer.ts',
  'src/lib/audit/analyzers/api-analyzer.ts',
  'src/lib/audit/analyzers/component-analyzer.ts',
  'src/lib/audit/analyzers/dependency-analyzer.ts'
];

let filesExist = 0;
for (const file of requiredFiles) {
  const exists = fs.existsSync(path.join(process.cwd(), file));
  if (exists) filesExist++;
}

logTest('All required files exist', filesExist === requiredFiles.length, 
  `${filesExist}/${requiredFiles.length} files found`);

// Test 2: Database Schema Validation
console.log('\n🗄️  Testing Database Schema...');

try {
  const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma');
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  const hasAuditRun = schemaContent.includes('model AuditRun');
  const hasAuditIssue = schemaContent.includes('model AuditIssue');
  const hasIssueComment = schemaContent.includes('model IssueComment');
  const hasEnums = schemaContent.includes('enum IssueSeverity') && 
                   schemaContent.includes('enum IssueCategory') && 
                   schemaContent.includes('enum IssueStatus');
  
  logTest('AuditRun model exists', hasAuditRun);
  logTest('AuditIssue model exists', hasAuditIssue);
  logTest('IssueComment model exists', hasIssueComment);
  logTest('Required enums exist', hasEnums);
  
  // Check relationships
  const hasRunIssueRelation = schemaContent.includes('issues        AuditIssue[]');
  const hasIssueRunRelation = schemaContent.includes('auditRun       AuditRun');
  const hasCommentRelation = schemaContent.includes('comments       IssueComment[]');
  
  logTest('AuditRun-AuditIssue relationship', hasRunIssueRelation);
  logTest('AuditIssue-AuditRun relationship', hasIssueRunRelation);
  logTest('AuditIssue-IssueComment relationship', hasCommentRelation);
  
} catch (error) {
  logTest('Database schema validation', false, error.message);
}

// Test 3: API Route Structure
console.log('\n🌐 Testing API Route Structure...');

const apiTests = [
  {
    file: 'src/app/api/audit/runs/route.ts',
    name: 'Audit Runs API',
    checks: ['export const GET', 'export const POST', 'getServerSession', 'withUnifiedErrorHandling']
  },
  {
    file: 'src/app/api/audit/issues/route.ts', 
    name: 'Audit Issues API',
    checks: ['export const GET', 'getServerSession', 'withUnifiedErrorHandling']
  },
  {
    file: 'src/app/api/audit/issues/[id]/route.ts',
    name: 'Individual Issue API',
    checks: ['export const GET', 'export const PATCH', 'getServerSession']
  }
];

for (const test of apiTests) {
  try {
    const content = fs.readFileSync(path.join(process.cwd(), test.file), 'utf8');
    const passedChecks = test.checks.filter(check => content.includes(check)).length;
    logTest(`${test.name} structure`, passedChecks === test.checks.length, 
      `${passedChecks}/${test.checks.length} checks passed`);
  } catch (error) {
    logTest(`${test.name} structure`, false, 'File not found');
  }
}

// Test 4: Component Structure
console.log('\n🎨 Testing Component Structure...');

const componentTests = [
  {
    file: 'src/components/audit/AuditDashboard.tsx',
    name: 'AuditDashboard',
    checks: ['useState', 'useEffect', 'fetch(', 'AuditMetrics', 'RecentIssues']
  },
  {
    file: 'src/components/audit/AuditMetrics.tsx',
    name: 'AuditMetrics', 
    checks: ['interface', 'Card', 'Badge', 'MetricCard']
  },
  {
    file: 'src/components/audit/RecentIssues.tsx',
    name: 'RecentIssues',
    checks: ['getSeverityIcon', 'getCategoryIcon', 'formatTimeAgo', 'IssueCard']
  }
];

for (const test of componentTests) {
  try {
    const content = fs.readFileSync(path.join(process.cwd(), test.file), 'utf8');
    const passedChecks = test.checks.filter(check => content.includes(check)).length;
    logTest(`${test.name} component`, passedChecks === test.checks.length,
      `${passedChecks}/${test.checks.length} features found`);
  } catch (error) {
    logTest(`${test.name} component`, false, 'File not found');
  }
}

// Test 5: Core Engine Structure
console.log('\n🔧 Testing Core Engine Structure...');

try {
  const engineContent = fs.readFileSync(
    path.join(process.cwd(), 'src/lib/audit/core-audit-engine.ts'), 'utf8'
  );
  
  const hasClass = engineContent.includes('class CoreAuditEngine');
  const hasInterface = engineContent.includes('implements AuditEngine');
  const hasAnalyzers = engineContent.includes('analyzeTypeScript') &&
                      engineContent.includes('analyzeSecurity') &&
                      engineContent.includes('analyzePerformance');
  const hasRunAudit = engineContent.includes('runAudit');
  
  logTest('CoreAuditEngine class exists', hasClass);
  logTest('Implements AuditEngine interface', hasInterface);
  logTest('Has analyzer methods', hasAnalyzers);
  logTest('Has runAudit method', hasRunAudit);
  
} catch (error) {
  logTest('Core engine structure', false, error.message);
}

// Test 6: Navigation Integration
console.log('\n🧭 Testing Navigation Integration...');

try {
  const navContent = fs.readFileSync(
    path.join(process.cwd(), 'src/components/layout/NavigationBar.tsx'), 'utf8'
  );
  
  const hasAuditLink = navContent.includes('/audit');
  const hasBugIcon = navContent.includes('Bug');
  const hasAdminCheck = navContent.includes('isAdmin');
  const hasAuditText = navContent.includes('Audit Dashboard');
  
  logTest('Navigation has audit link', hasAuditLink);
  logTest('Navigation has bug icon', hasBugIcon);
  logTest('Navigation has admin check', hasAdminCheck);
  logTest('Navigation has audit text', hasAuditText);
  
} catch (error) {
  logTest('Navigation integration', false, error.message);
}

// Test 7: Security Configuration
console.log('\n🔒 Testing Security Configuration...');

try {
  const middlewareContent = fs.readFileSync(
    path.join(process.cwd(), 'middleware.ts'), 'utf8'
  );
  
  const hasAuditRoute = middlewareContent.includes('/audit');
  const hasAdminRoutes = middlewareContent.includes('adminRoutes');
  const hasAuditAPI = middlewareContent.includes('/api/audit');
  
  logTest('Middleware protects audit routes', hasAuditRoute);
  logTest('Middleware has admin routes', hasAdminRoutes);
  logTest('Middleware protects audit API', hasAuditAPI);
  
} catch (error) {
  logTest('Security configuration', false, error.message);
}

// Test 8: TypeScript Types
console.log('\n📝 Testing TypeScript Types...');

try {
  const typesContent = fs.readFileSync(
    path.join(process.cwd(), 'src/lib/audit/types.ts'), 'utf8'
  );
  
  const hasEnums = typesContent.includes('enum IssueSeverity') &&
                   typesContent.includes('enum IssueCategory') &&
                   typesContent.includes('enum IssueStatus');
  const hasInterfaces = typesContent.includes('interface AuditEngine') &&
                        typesContent.includes('interface AuditRunResult');
  const hasTypes = typesContent.includes('type') || typesContent.includes('interface');
  
  logTest('Has required enums', hasEnums);
  logTest('Has core interfaces', hasInterfaces);
  logTest('Has type definitions', hasTypes);
  
} catch (error) {
  logTest('TypeScript types', false, error.message);
}

// Summary
console.log('\n' + '=' .repeat(60));
console.log('📊 Test Results Summary:');
console.log(`✅ Passed: ${testsPassed}`);
console.log(`❌ Failed: ${testsFailed}`);
console.log(`📈 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`);

if (testsFailed === 0) {
  console.log('\n🎉 All tests passed! Audit system is properly structured.');
} else {
  console.log(`\n⚠️  ${testsFailed} tests failed. Review the issues above.`);
}

console.log('\n🔍 Component Testing Complete!');
console.log('=' .repeat(60));
