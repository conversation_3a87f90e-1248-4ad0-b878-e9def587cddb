/**
 * Jest Configuration for Architecture Tests
 * Optimized for architecture validation and analysis tests
 */

const baseConfig = require('./jest.config.js');

module.exports = {
  ...baseConfig,
  
  // Architecture test specific settings
  testTimeout: 45000, // 45 seconds for architecture analysis
  maxWorkers: 1, // Single worker for file system analysis
  
  // Only run architecture tests
  testMatch: [
    '**/src/__tests__/architecture/**/*.(test|spec).(ts|tsx|js|jsx)',
    '**/__tests__/architecture/**/*.(test|spec).(ts|tsx|js|jsx)'
  ],
  
  // Remove path ignores for architecture tests
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
  ],
  
  // Disable coverage for architecture tests
  collectCoverage: false,
  
  // Use node environment for file system access
  testEnvironment: 'node',
  
  // Enhanced logging for architecture analysis
  verbose: true,
  
  // Custom reporters for architecture metrics
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test-results/architecture',
      outputName: 'architecture-junit.xml',
    }]
  ],
  
  // Setup files specific to architecture testing
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.js',
    '<rootDir>/test-utils/architecture-setup.js'
  ],
  
  // Global test configuration
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx',
      },
    },
    ARCHITECTURE_TEST_MODE: true,
    ENABLE_FILE_ANALYSIS: true,
  },
  
  // Memory management for large file analysis
  workerIdleMemoryLimit: '512MB',
  logHeapUsage: true,
};
