/**
 * End-to-End EdgeCaseHandler Integration Tests
 *
 * Tests complete user workflows with EdgeCaseHandler integration:
 * 1. API endpoint integration with EdgeCaseHandler
 * 2. Edge case scenarios with good UX
 * 3. Error recovery and fallback experiences
 * 4. Performance under stress conditions
 */

import { test, expect } from '@playwright/test';

test.describe('EdgeCaseHandler E2E Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Set base URL to correct port
    await page.goto('http://localhost:3001/');
  });

  test('EdgeCaseHandler API Integration - Skills Analysis', async ({ page }) => {
    // Test EdgeCaseHandler integration through API calls
    const response = await page.request.post('http://localhost:3001/api/ai/skills-analysis', {
      data: {
        currentSkills: ['JavaScript', 'React'],
        targetCareerPath: 'Full Stack Developer',
        experienceLevel: 'mid',
        timeframe: '6_months',
        focusAreas: ['backend', 'databases'],
        includeMarketData: true
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Verify response structure includes EdgeCaseHandler metadata
    const responseData = await response.json();

    if (response.ok()) {
      // Success case - verify EdgeCaseHandler metadata
      expect(responseData.success).toBe(true);
      expect(responseData.data).toBeDefined();
      expect(responseData.metadata).toBeDefined();

      // Check for EdgeCaseHandler integration indicators
      if (responseData.metadata.edgeCaseHandlerUsed) {
        expect(responseData.metadata.edgeCaseHandlerData).toBeDefined();
      }
    } else {
      // Error case - verify EdgeCaseHandler error handling
      expect(responseData.fallbackData).toBeDefined();
      expect(responseData.errorType).toBeDefined();

      // Verify helpful error information
      if (responseData.suggestedAlternatives) {
        expect(Array.isArray(responseData.suggestedAlternatives)).toBe(true);
      }
    }
  });

  test('EdgeCaseHandler API - Invalid Input Handling', async ({ page }) => {
    // Test EdgeCaseHandler with invalid input data
    const response = await page.request.post('http://localhost:3001/api/ai/skills-analysis', {
      data: {
        currentSkills: ['NonExistentSkill123', 'InvalidSkill456'],
        targetCareerPath: 'Invalid Career Path',
        experienceLevel: 'invalid_level',
        timeframe: 'invalid_timeframe',
        focusAreas: [],
        includeMarketData: true
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const responseData = await response.json();

    // Verify EdgeCaseHandler provides graceful error handling
    expect(responseData.success).toBe(false);
    expect(responseData.errorType).toBeDefined();

    // Verify fallback data is provided
    expect(responseData.fallbackData).toBeDefined();
    expect(responseData.fallbackData.skillGaps).toBeDefined();
    expect(responseData.fallbackData.learningPlan).toBeDefined();

    // Verify suggested alternatives are provided
    if (responseData.suggestedAlternatives) {
      expect(Array.isArray(responseData.suggestedAlternatives)).toBe(true);
      expect(responseData.suggestedAlternatives.length).toBeGreaterThan(0);
    }
  });

  test('EdgeCaseHandler API - Skill Assessment Integration', async ({ page }) => {
    // Test skill assessment endpoint with EdgeCaseHandler
    const response = await page.request.post('http://localhost:3001/api/skills/assessment', {
      data: {
        skillId: '550e8400-e29b-41d4-a716-446655440000',
        selfRating: 7,
        confidenceLevel: 8,
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: 'career-123'
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const responseData = await response.json();

    if (response.ok()) {
      // Success case
      expect(responseData.success).toBe(true);
      expect(responseData.data).toBeDefined();

      // Check for EdgeCaseHandler integration
      if (responseData.data.edgeCaseHandlerUsed) {
        expect(responseData.data.edgeCaseHandlerData).toBeDefined();
      }
    } else {
      // Error case - verify EdgeCaseHandler provides helpful response
      expect(responseData.error).toBeDefined();
      expect(typeof responseData.error).toBe('string');
    }
  });

  test('EdgeCaseHandler Performance - Multiple Requests', async ({ page }) => {
    const startTime = Date.now();
    const responses = [];

    // Make multiple concurrent requests to test EdgeCaseHandler performance
    for (let i = 0; i < 3; i++) {
      const response = page.request.post('http://localhost:3001/api/ai/skills-analysis', {
        data: {
          currentSkills: [`JavaScript${i}`, `React${i}`],
          targetCareerPath: 'Full Stack Developer',
          experienceLevel: 'mid',
          timeframe: '6_months',
          focusAreas: ['backend'],
          includeMarketData: true
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });
      responses.push(response);
    }

    // Wait for all responses
    const results = await Promise.all(responses);
    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // Verify reasonable performance (should complete 3 requests in under 30 seconds)
    expect(totalTime).toBeLessThan(30000);

    // Verify all responses are valid
    for (const response of results) {
      const responseData = await response.json();

      if (response.ok()) {
        expect(responseData.success).toBe(true);
        expect(responseData.data).toBeDefined();
      } else {
        // Even error responses should have proper EdgeCaseHandler structure
        expect(responseData.errorType).toBeDefined();
        expect(responseData.fallbackData).toBeDefined();
      }
    }
  });

  test('EdgeCaseHandler Health Check', async ({ page }) => {
    // Test EdgeCaseHandler health and status endpoints
    const healthResponse = await page.request.get('http://localhost:3001/api/health');

    if (healthResponse.ok()) {
      const healthData = await healthResponse.json();
      expect(healthData.status).toBeDefined();

      // Check if EdgeCaseHandler health information is included
      if (healthData.edgeCaseHandler) {
        expect(healthData.edgeCaseHandler.status).toBeDefined();
      }
    }
  });
});
