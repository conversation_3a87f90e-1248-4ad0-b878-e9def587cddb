import { NextRequest, NextResponse } from 'next/server';
import securityStorage from './security-storage';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  message?: string;
  keyGenerator?: (request: NextRequest) => string;
}

export function rateLimit(config: RateLimitConfig) {
  const {
    windowMs,
    maxRequests,
    message = 'Too many requests'
  } = config;

  return async (request: NextRequest): Promise<NextResponse | null> => {
    // Check if rate limiting should be enabled in development
    const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Skip rate limiting only if in development AND dev rate limiting is disabled
    if (isDevelopment && !enableDevRateLimit) {
      console.log('🚫 Rate limiting disabled in development mode');
      return null; // Continue to next middleware/handler
    }

    // Apply more lenient limits in development for testing
    const devMultiplier = isDevelopment ? 2 : 1; // Double the limits in dev
    const adjustedMaxRequests = maxRequests * devMultiplier;

    const result = await securityStorage.checkRateLimit(request, windowMs, adjustedMaxRequests);

    if (!result.allowed) {
      const devWarning = isDevelopment ? ' (Development Mode - Limits Relaxed)' : '';
      return NextResponse.json(
        { error: message + devWarning },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': adjustedMaxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': result.resetTime.toString(),
            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
            'X-RateLimit-Dev-Mode': isDevelopment.toString()
          }
        }
      );
    }

    // Add rate limit headers to successful responses
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', adjustedMaxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
    if (isDevelopment) {
      response.headers.set('X-RateLimit-Dev-Mode', 'true');
    }

    return null; // Continue to next middleware/handler
  };
}

function getClientIP(request: NextRequest): string | null {
  // Try various headers for client IP
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to connection remote address (may not be available in all environments)
  return (request as any).ip || null;
}

// Predefined rate limit configurations
export const rateLimitConfigs = {
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many authentication attempts. Please try again later.'
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: 'Too many API requests. Please try again later.'
  },
  contact: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many contact form submissions. Please try again later.'
  },
  signup: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many signup attempts. Please try again later.'
  }
} as const;

// Development-friendly rate limit configurations (more lenient for testing)
export const devRateLimitConfigs = {
  auth: {
    windowMs: 5 * 60 * 1000, // 5 minutes (shorter window)
    maxRequests: 10, // Double the requests
    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'
  },
  api: {
    windowMs: 10 * 60 * 1000, // 10 minutes (shorter window)
    maxRequests: 200, // Double the requests
    message: 'Too many API requests. Please try again later. (Dev Mode)'
  },
  contact: {
    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)
    maxRequests: 6, // Double the requests
    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'
  },
  signup: {
    windowMs: 30 * 60 * 1000, // 30 minutes (shorter window)
    maxRequests: 6, // Double the requests
    message: 'Too many signup attempts. Please try again later. (Dev Mode)'
  }
} as const;

// Helper to get appropriate config based on environment
export function getRateLimitConfig(type: keyof typeof rateLimitConfigs): RateLimitConfig {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';

  if (isDevelopment && enableDevRateLimit) {
    return devRateLimitConfigs[type];
  }

  return rateLimitConfigs[type];
}

// Helper function to apply rate limiting to API routes
export async function withRateLimit(
  request: NextRequest,
  config: RateLimitConfig,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Check if rate limiting should be enabled in development
  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Skip rate limiting only if in development AND dev rate limiting is disabled
  if (isDevelopment && !enableDevRateLimit) {
    console.log('🚫 Rate limiting disabled for API route in development mode');
    return handler();
  }

  const rateLimitResponse = await rateLimit(config)(request);

  if (rateLimitResponse) {
    return rateLimitResponse;
  }

  return handler();
}

// CSRF Protection
export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  // In a real implementation, you'd store CSRF tokens in session/database
  // For now, we'll use a simple validation
  return token === sessionToken;
}

// Input sanitization helpers
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
    .slice(0, 1000); // Limit length
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

// SQL injection prevention (Prisma handles this, but good to have)
export function escapeSQLString(str: string): string {
  return str.replace(/'/g, "''");
}

// XSS prevention helpers
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[@$!%*?&]/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Session security helpers
export function generateSecureSessionId(): string {
  return crypto.randomUUID() + '-' + Date.now().toString(36);
}

export function isValidSessionId(sessionId: string): boolean {
  // Basic validation for session ID format
  return /^[a-f0-9-]+$/i.test(sessionId) && sessionId.length > 20;
}
