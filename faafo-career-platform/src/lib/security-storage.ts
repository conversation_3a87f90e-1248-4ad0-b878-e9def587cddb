import { prisma, withDatabaseRetry } from './prisma';
import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';
import crypto from 'crypto';

// Database-backed security storage for production use
export class SecurityStorage {
  private static instance: SecurityStorage;
  private memoryFallback = new Map<string, any>();

  static getInstance(): SecurityStorage {
    if (!SecurityStorage.instance) {
      SecurityStorage.instance = new SecurityStorage();
    }
    return SecurityStorage.instance;
  }

  private async getClientIdentifier(request: NextRequest): Promise<string> {
    const session = await getServerSession(authOptions);
    if (session?.user?.id) {
      return `user_${session.user.id}`;
    }
    
    // For anonymous users, use IP + User-Agent hash
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const hash = await this.hashString(`${ip}_${userAgent}`);
    return `anon_${hash.substring(0, 16)}`;
  }

  private async hashString(input: string): Promise<string> {
    // Use Node.js crypto for server-side hashing (more reliable in test environment)
    if (typeof window === 'undefined') {
      // Server-side: use Node.js crypto
      const hash = crypto.createHash('sha256');
      hash.update(input);
      return hash.digest('hex');
    } else {
      // Client-side: use Web Crypto API
      const encoder = new TextEncoder();
      const data = encoder.encode(input);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
  }

  // CSRF Token Management
  async storeCSRFToken(request: NextRequest, token: string, expiresAt: Date): Promise<void> {
    const identifier = await this.getClientIdentifier(request);
    
    try {
      // Try database first
      await prisma.securityToken.upsert({
        where: {
          identifier_type: {
            identifier,
            type: 'CSRF'
          }
        },
        update: {
          token,
          expiresAt,
          updatedAt: new Date()
        },
        create: {
          identifier,
          type: 'CSRF',
          token,
          expiresAt,
          metadata: {
            userAgent: request.headers.get('user-agent'),
            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
          }
        }
      });
    } catch (error) {
      console.warn('Database CSRF storage failed, using memory fallback:', error);
      this.memoryFallback.set(`csrf_${identifier}`, { token, expiresAt });
    }
  }

  async getCSRFToken(request: NextRequest): Promise<string | null> {
    const identifier = await this.getClientIdentifier(request);
    
    try {
      // Try database first
      const stored = await prisma.securityToken.findUnique({
        where: {
          identifier_type: {
            identifier,
            type: 'CSRF'
          }
        }
      });

      if (stored && stored.expiresAt > new Date()) {
        return stored.token;
      }
    } catch (error) {
      console.warn('Database CSRF retrieval failed, using memory fallback:', error);
      const fallback = this.memoryFallback.get(`csrf_${identifier}`);
      if (fallback && fallback.expiresAt > new Date()) {
        return fallback.token;
      }
    }

    return null;
  }

  async validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
    const identifier = await this.getClientIdentifier(request);
    
    try {
      // Try database first
      const stored = await prisma.securityToken.findUnique({
        where: {
          identifier_type: {
            identifier,
            type: 'CSRF'
          }
        }
      });

      if (stored && stored.expiresAt > new Date() && stored.token === token) {
        return true;
      }
    } catch (error) {
      console.warn('Database CSRF validation failed, using memory fallback:', error);
      const fallback = this.memoryFallback.get(`csrf_${identifier}`);
      if (fallback && fallback.expiresAt > new Date() && fallback.token === token) {
        return true;
      }
    }

    return false;
  }

  // Rate Limiting
  async checkRateLimit(request: NextRequest, windowMs: number, maxRequests: number): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const identifier = await this.getClientIdentifier(request);
    const now = new Date();
    const windowStart = new Date(now.getTime() - windowMs);

    try {
      // Try database first
      const count = await prisma.rateLimitEntry.count({
        where: {
          identifier,
          createdAt: {
            gte: windowStart
          }
        }
      });

      if (count >= maxRequests) {
        const oldestEntry = await prisma.rateLimitEntry.findFirst({
          where: { identifier },
          orderBy: { createdAt: 'asc' }
        });

        return {
          allowed: false,
          remaining: 0,
          resetTime: oldestEntry ? oldestEntry.createdAt.getTime() + windowMs : now.getTime() + windowMs
        };
      }

      // Record this request
      await prisma.rateLimitEntry.create({
        data: {
          identifier,
          metadata: {
            userAgent: request.headers.get('user-agent'),
            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
            path: request.nextUrl.pathname
          }
        }
      });

      return {
        allowed: true,
        remaining: maxRequests - count - 1,
        resetTime: now.getTime() + windowMs
      };

    } catch (error) {
      console.warn('Database rate limiting failed, using memory fallback:', error);
      
      // Memory fallback
      const key = `rate_${identifier}`;
      const entries = this.memoryFallback.get(key) || [];
      const validEntries = entries.filter((entry: any) => entry.timestamp > now.getTime() - windowMs);
      
      if (validEntries.length >= maxRequests) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: validEntries[0].timestamp + windowMs
        };
      }

      validEntries.push({ timestamp: now.getTime() });
      this.memoryFallback.set(key, validEntries);

      return {
        allowed: true,
        remaining: maxRequests - validEntries.length,
        resetTime: now.getTime() + windowMs
      };
    }
  }

  // Cleanup expired entries with robust error handling
  async cleanup(): Promise<void> {
    try {
      const now = new Date();

      // Clean up expired security tokens with retry logic
      await withDatabaseRetry(async () => {
        await prisma.securityToken.deleteMany({
          where: {
            expiresAt: {
              lt: now
            }
          }
        });
      }, 2, 1000); // 2 retries with 1 second base delay

      // Clean up old rate limit entries (keep last 24 hours)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      await withDatabaseRetry(async () => {
        await prisma.rateLimitEntry.deleteMany({
          where: {
            createdAt: {
              lt: oneDayAgo
            }
          }
        });
      }, 2, 1000); // 2 retries with 1 second base delay

    } catch (error) {
      console.warn('Database cleanup failed after retries:', error);
      // Continue with memory cleanup even if database cleanup fails
    }

    // Clean up memory fallback
    const now = Date.now();
    Array.from(this.memoryFallback.entries()).forEach(([key, value]) => {
      if (key.startsWith('csrf_') && value.expiresAt < now) {
        this.memoryFallback.delete(key);
      } else if (key.startsWith('rate_')) {
        const validEntries = value.filter((entry: any) => entry.timestamp > now - 24 * 60 * 60 * 1000);
        if (validEntries.length === 0) {
          this.memoryFallback.delete(key);
        } else {
          this.memoryFallback.set(key, validEntries);
        }
      }
    });
  }
}

// Initialize cleanup interval
const securityStorage = SecurityStorage.getInstance();
setInterval(() => {
  securityStorage.cleanup();
}, 15 * 60 * 1000); // Clean up every 15 minutes

export default securityStorage;
