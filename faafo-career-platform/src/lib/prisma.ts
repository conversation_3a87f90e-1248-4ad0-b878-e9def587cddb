import { PrismaClient } from "@prisma/client";
import { databaseMonitor } from './database-monitor';
import { setupAdvancedQueryTracking } from './services/advanced-query-performance-monitor';

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Build optimized database URL with connection pool parameters
function buildDatabaseUrl(): string {
  const baseUrl = process.env.POSTGRES_PRISMA_URL || process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error('DATABASE_URL or POSTGRES_PRISMA_URL must be defined');
  }

  console.log('Database URL being used:', baseUrl.replace(/:[^:@]*@/, ':***@')); // Log URL without password

  // If URL already has parameters, don't modify it
  if (baseUrl.includes('?')) {
    return baseUrl;
  }

  // Add connection pool parameters for PostgreSQL with conservative settings for Neon
  const poolParams = new URLSearchParams({
    'connection_limit': process.env.NODE_ENV === 'production' ? '10' : '3', // More conservative for development
    'pool_timeout': '20', // Shorter timeout
    'connect_timeout': '15', // Shorter connection timeout
    'sslmode': 'require' // Ensure SSL connection
  });

  return `${baseUrl}?${poolParams.toString()}`;
}

// Create Prisma client with maximum reliability configuration
const createPrismaClient = () => new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: buildDatabaseUrl(),
    },
  },
  // Optimize transaction settings for maximum reliability
  transactionOptions: {
    maxWait: 30000, // Increased to 30 seconds
    timeout: 120000, // Increased to 2 minutes for complex operations
    isolationLevel: 'ReadCommitted', // Better for concurrent operations
  },
  // Add error handling and retry logic
  errorFormat: 'pretty',
});

const prisma = globalForPrisma.prisma ?? createPrismaClient();

// Initialize database monitoring in development to detect N+1 queries
if (process.env.NODE_ENV === 'development') {
  databaseMonitor.startMonitoring(prisma);
}

// Setup advanced query performance tracking
setupAdvancedQueryTracking(prisma);

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Improved graceful shutdown handler with better error handling
let isShuttingDown = false;
let shutdownTimeout: NodeJS.Timeout | null = null;

const gracefulShutdown = async (signal: string) => {
  if (isShuttingDown) {
    console.log(`Already shutting down, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  console.log(`Received ${signal}, starting graceful shutdown...`);

  // Clear any existing shutdown timeout
  if (shutdownTimeout) {
    clearTimeout(shutdownTimeout);
    shutdownTimeout = null;
  }

  try {
    // Give ongoing operations time to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Disconnect Prisma client
    await prisma.$disconnect();
    console.log('Prisma client disconnected successfully');

    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Only handle beforeExit in production to avoid disconnecting during development hot reloads
if (process.env.NODE_ENV === 'production') {
  process.on('beforeExit', async () => {
    if (!isShuttingDown) {
      console.log('Process exiting, disconnecting Prisma client...');
      try {
        await prisma.$disconnect();
      } catch (error) {
        console.error('Error disconnecting Prisma on beforeExit:', error);
      }
    }
  });
}

// Only handle signals in production to avoid issues during development
if (process.env.NODE_ENV === 'production') {
  // Check if listeners already exist to prevent MaxListenersExceededWarning
  const existingSigintListeners = process.listenerCount('SIGINT');
  const existingSigtermListeners = process.listenerCount('SIGTERM');

  if (existingSigintListeners === 0) {
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  if (existingSigtermListeners === 0) {
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  }
}

// Connection health monitoring
let connectionHealthy = true;
let lastHealthCheck = 0;
const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds

async function checkConnectionHealth(): Promise<boolean> {
  const now = Date.now();
  if (now - lastHealthCheck < HEALTH_CHECK_INTERVAL && connectionHealthy) {
    return connectionHealthy;
  }

  try {
    await prisma.$queryRaw`SELECT 1`;
    connectionHealthy = true;
    lastHealthCheck = now;
    return true;
  } catch (error) {
    console.warn('Database connection health check failed:', error);
    connectionHealthy = false;
    lastHealthCheck = now;
    return false;
  }
}

// Database operation wrapper with retry logic (simplified for development)
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = process.env.NODE_ENV === 'development' ? 2 : 5,
  baseDelay: number = 500
): Promise<T> {
  let lastError: Error;

  // Skip health checks in development to avoid connection issues
  if (process.env.NODE_ENV === 'production') {
    // Check connection health first
    const isHealthy = await checkConnectionHealth();
    if (!isHealthy) {
      console.warn('Database connection unhealthy, attempting to reconnect...');
      try {
        await prisma.$disconnect();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await prisma.$connect();
        console.log('Database reconnection successful');
      } catch (reconnectError) {
        console.error('Database reconnection failed:', reconnectError);
      }
    }
  }

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();

      // Mark connection as healthy on successful operation
      if (!connectionHealthy) {
        connectionHealthy = true;
        console.log('Database connection restored');
      }

      return result;
    } catch (error) {
      lastError = error as Error;

      // Check if it's a connection error that we should retry
      const isRetryableError =
        error instanceof Error && (
          error.message.includes('connection') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ENOTFOUND') ||
          error.message.includes('ETIMEDOUT') ||
          error.message.includes('Closed') ||
          error.message.includes('EPIPE') ||
          error.message.includes('ECONNREFUSED')
        );

      if (!isRetryableError || attempt === maxRetries) {
        connectionHealthy = false;
        throw error;
      }

      // Mark connection as unhealthy
      connectionHealthy = false;

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 500;
      console.warn(`Database operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`, error.message);

      // Try to reconnect on connection errors
      if (attempt > 0) {
        try {
          await prisma.$disconnect();
          await new Promise(resolve => setTimeout(resolve, 500));
          await prisma.$connect();
        } catch (reconnectError) {
          console.warn('Reconnection attempt failed:', reconnectError);
        }
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// Connection pool monitoring
export async function getDatabaseStatus(): Promise<{
  status: 'healthy' | 'unhealthy';
  connectionCount?: number;
  responseTime?: number;
  error?: string;
}> {
  try {
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;

    return {
      status: 'healthy',
      responseTime,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Periodic connection health monitoring
let healthCheckInterval: NodeJS.Timeout | null = null;

if (process.env.NODE_ENV === 'production') {
  healthCheckInterval = setInterval(async () => {
    const status = await getDatabaseStatus();
    if (status.status === 'unhealthy') {
      console.warn('Database health check failed:', status.error);

      // Attempt to reconnect
      try {
        await prisma.$disconnect();
        await new Promise(resolve => setTimeout(resolve, 2000));
        await prisma.$connect();
        console.log('Database reconnection successful');
      } catch (reconnectError) {
        console.error('Database reconnection failed:', reconnectError);
      }
    }
  }, 60000); // Check every minute in production
}

// Cleanup on process exit
process.on('beforeExit', () => {
  if (healthCheckInterval) {
    clearInterval(healthCheckInterval);
  }
});

export { prisma };
export default prisma;