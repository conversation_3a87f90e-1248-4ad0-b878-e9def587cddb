/**
 * Audit System Index
 * 
 * Main entry point for the codebase audit system.
 * Exports all public interfaces and services.
 */

// Core Engine
export { CoreAuditEngine } from './core-audit-engine';

// Main Service
export { AuditService } from './audit-service';

// Import for utility functions
import { CoreAuditEngine } from './core-audit-engine';
import { AuditService } from './audit-service';

// Storage
export { AuditStorage } from './storage/audit-storage';

// CLI
export { AuditCLI } from './cli/audit-cli';

// Analyzers
export { TypeScriptAnalyzer } from './analyzers/typescript-analyzer';
export { ESLintAnalyzer } from './analyzers/eslint-analyzer';
export { SecurityAnalyzer } from './analyzers/security-analyzer';
export { PerformanceAnalyzer } from './analyzers/performance-analyzer';
export { ArchitectureAnalyzer } from './analyzers/architecture-analyzer';
export { PrismaAnalyzer } from './analyzers/prisma-analyzer';
export { TestCoverageAnalyzer } from './analyzers/test-coverage-analyzer';
export { APIAnalyzer } from './analyzers/api-analyzer';
export { ComponentAnalyzer } from './analyzers/component-analyzer';
export { DependencyAnalyzer } from './analyzers/dependency-analyzer';

// Types
export * from './types';

// Utility functions
export const createAuditService = () => new AuditService();

export const createAuditEngine = (context?: any, progressCallback?: any) =>
  new CoreAuditEngine(context, progressCallback);
