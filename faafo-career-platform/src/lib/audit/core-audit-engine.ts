/**
 * Core Audit Engine Implementation
 * 
 * Main implementation of the codebase audit system that orchestrates
 * all analysis modules and provides a unified interface for running audits.
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import {
  AuditEngine,
  AuditRunConfig,
  AuditRunResult,
  AuditStatus,
  IssueSeverity,
  IssueCategory,
  TypeScriptIssue,
  SecurityVulnerability,
  PerformanceIssue,
  ArchitecturalDebt,
  LintIssue,
  DatabaseIssue,
  CoverageGap,
  APIInconsistency,
  ComponentIssue,
  DependencyIssue,
  AnalysisContext,
  AuditProgress,
  AuditProgressCallback,
  BaseIssue
} from './types';
import { TypeScriptAnalyzer } from './analyzers/typescript-analyzer';
import { SecurityAnalyzer } from './analyzers/security-analyzer';
import { PerformanceAnalyzer } from './analyzers/performance-analyzer';
import { ArchitectureAnalyzer } from './analyzers/architecture-analyzer';
import { ESLintAnalyzer } from './analyzers/eslint-analyzer';
import { PrismaAnalyzer } from './analyzers/prisma-analyzer';
import { TestCoverageAnalyzer } from './analyzers/test-coverage-analyzer';
import { APIAnalyzer } from './analyzers/api-analyzer';
import { ComponentAnalyzer } from './analyzers/component-analyzer';
import { DependencyAnalyzer } from './analyzers/dependency-analyzer';
import { AuditStorage } from './storage/audit-storage';
import { logger } from '../logger';

export class CoreAuditEngine implements AuditEngine {
  private context: AnalysisContext;
  private storage: AuditStorage;
  private progressCallback?: AuditProgressCallback;

  // Analyzer instances
  private typeScriptAnalyzer: TypeScriptAnalyzer;
  private securityAnalyzer: SecurityAnalyzer;
  private performanceAnalyzer: PerformanceAnalyzer;
  private architectureAnalyzer: ArchitectureAnalyzer;
  private eslintAnalyzer: ESLintAnalyzer;
  private prismaAnalyzer: PrismaAnalyzer;
  private testCoverageAnalyzer: TestCoverageAnalyzer;
  private apiAnalyzer: APIAnalyzer;
  private componentAnalyzer: ComponentAnalyzer;
  private dependencyAnalyzer: DependencyAnalyzer;

  constructor(context?: Partial<AnalysisContext>, progressCallback?: AuditProgressCallback) {
    this.context = this.initializeContext(context);
    this.storage = new AuditStorage();
    this.progressCallback = progressCallback;

    // Initialize analyzers
    this.typeScriptAnalyzer = new TypeScriptAnalyzer(this.context);
    this.securityAnalyzer = new SecurityAnalyzer(this.context);
    this.performanceAnalyzer = new PerformanceAnalyzer(this.context);
    this.architectureAnalyzer = new ArchitectureAnalyzer(this.context);
    this.eslintAnalyzer = new ESLintAnalyzer(this.context);
    this.prismaAnalyzer = new PrismaAnalyzer(this.context);
    this.testCoverageAnalyzer = new TestCoverageAnalyzer(this.context);
    this.apiAnalyzer = new APIAnalyzer(this.context);
    this.componentAnalyzer = new ComponentAnalyzer(this.context);
    this.dependencyAnalyzer = new DependencyAnalyzer(this.context);
  }

  private initializeContext(context?: Partial<AnalysisContext>): AnalysisContext {
    const projectRoot = context?.projectRoot || process.cwd();
    const sourceDir = context?.sourceDir || path.join(projectRoot, 'src');

    return {
      projectRoot,
      sourceDir,
      configFiles: {
        tsconfig: path.join(projectRoot, 'tsconfig.json'),
        eslint: path.join(projectRoot, '.eslintrc.json'),
        prisma: path.join(projectRoot, 'prisma/schema.prisma'),
        package: path.join(projectRoot, 'package.json'),
        ...context?.configFiles
      },
      excludePatterns: [
        'node_modules/**',
        '.next/**',
        'dist/**',
        'build/**',
        'coverage/**',
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts',
        '**/*.spec.tsx',
        ...(context?.excludePatterns || [])
      ],
      includePatterns: [
        'src/**/*.ts',
        'src/**/*.tsx',
        'prisma/**/*.prisma',
        '*.config.js',
        '*.config.ts',
        ...(context?.includePatterns || [])
      ]
    };
  }

  private updateProgress(phase: string, progress: number, currentFile?: string, issuesFound: number = 0): void {
    if (this.progressCallback) {
      this.progressCallback({
        phase,
        progress,
        currentFile,
        issuesFound
      });
    }
  }

  async analyzeTypeScript(): Promise<TypeScriptIssue[]> {
    this.updateProgress('TypeScript Analysis', 0);
    const issues = await this.typeScriptAnalyzer.analyze();
    this.updateProgress('TypeScript Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeSecurity(): Promise<SecurityVulnerability[]> {
    this.updateProgress('Security Analysis', 0);
    const issues = await this.securityAnalyzer.analyze();
    this.updateProgress('Security Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzePerformance(): Promise<PerformanceIssue[]> {
    this.updateProgress('Performance Analysis', 0);
    const issues = await this.performanceAnalyzer.analyze();
    this.updateProgress('Performance Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeArchitecture(): Promise<ArchitecturalDebt[]> {
    this.updateProgress('Architecture Analysis', 0);
    const issues = await this.architectureAnalyzer.analyze();
    this.updateProgress('Architecture Analysis', 100, undefined, issues.length);
    return issues;
  }

  async integrateESLint(): Promise<LintIssue[]> {
    this.updateProgress('ESLint Integration', 0);
    const issues = await this.eslintAnalyzer.analyze();
    this.updateProgress('ESLint Integration', 100, undefined, issues.length);
    return issues;
  }

  async integratePrismaSchema(): Promise<DatabaseIssue[]> {
    this.updateProgress('Prisma Schema Analysis', 0);
    const issues = await this.prismaAnalyzer.analyze();
    this.updateProgress('Prisma Schema Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeTestCoverage(): Promise<CoverageGap[]> {
    this.updateProgress('Test Coverage Analysis', 0);
    const issues = await this.testCoverageAnalyzer.analyze();
    this.updateProgress('Test Coverage Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeAPIConsistency(): Promise<APIInconsistency[]> {
    this.updateProgress('API Consistency Analysis', 0);
    const issues = await this.apiAnalyzer.analyze();
    this.updateProgress('API Consistency Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeComponentPatterns(): Promise<ComponentIssue[]> {
    this.updateProgress('Component Pattern Analysis', 0);
    const issues = await this.componentAnalyzer.analyze();
    this.updateProgress('Component Pattern Analysis', 100, undefined, issues.length);
    return issues;
  }

  async analyzeDependencies(): Promise<DependencyIssue[]> {
    this.updateProgress('Dependency Analysis', 0);
    const issues = await this.dependencyAnalyzer.analyze();
    this.updateProgress('Dependency Analysis', 100, undefined, issues.length);
    return issues;
  }

  async runAudit(config: AuditRunConfig = {}): Promise<AuditRunResult> {
    const auditId = uuidv4();
    const startedAt = new Date();

    logger.info(`Starting audit run ${auditId}`);

    try {
      // Create audit run record
      await this.storage.createAuditRun({
        id: auditId,
        status: AuditStatus.RUNNING,
        startedAt,
        totalIssues: 0,
        criticalCount: 0,
        highCount: 0,
        mediumCount: 0,
        lowCount: 0,
        issues: [],
        metadata: { config }
      });

      this.updateProgress('Initializing Audit', 5);

      const allIssues: BaseIssue[] = [];
      const analysisSteps = this.getEnabledAnalysisSteps(config);
      const stepProgress = 90 / analysisSteps.length; // Reserve 10% for finalization

      let currentProgress = 10;

      // Run enabled analyses
      for (let index = 0; index < analysisSteps.length; index++) {
        const step = analysisSteps[index];
        try {
          this.updateProgress(`Running ${step.name}`, currentProgress);
          const issues = await step.analyzer();
          allIssues.push(...issues);
          currentProgress += stepProgress;
          this.updateProgress(`Completed ${step.name}`, currentProgress, undefined, issues.length);
        } catch (error) {
          logger.error(`Error in ${step.name}:`, error as Error);
          // Continue with other analyses even if one fails
        }
      }

      // Filter and process issues
      const filteredIssues = this.filterIssues(allIssues, config);
      const processedIssues = this.processIssues(filteredIssues);

      this.updateProgress('Finalizing Results', 95);

      // Calculate counts
      const severityCounts = this.calculateSeverityCounts(processedIssues);
      const completedAt = new Date();

      const result: AuditRunResult = {
        id: auditId,
        status: AuditStatus.COMPLETED,
        startedAt,
        completedAt,
        totalIssues: processedIssues.length,
        criticalCount: severityCounts.CRITICAL,
        highCount: severityCounts.HIGH,
        mediumCount: severityCounts.MEDIUM,
        lowCount: severityCounts.LOW,
        issues: processedIssues,
        metadata: { config, analysisSteps: analysisSteps.map(s => s.name) }
      };

      // Update audit run record
      await this.storage.updateAuditRun(auditId, {
        status: AuditStatus.COMPLETED,
        completedAt,
        totalIssues: result.totalIssues,
        criticalCount: result.criticalCount,
        highCount: result.highCount,
        mediumCount: result.mediumCount,
        lowCount: result.lowCount
      });

      // Store issues
      await this.storage.storeIssues(auditId, processedIssues);

      this.updateProgress('Audit Complete', 100, undefined, processedIssues.length);

      logger.info(`Audit run ${auditId} completed successfully with ${result.totalIssues} issues`);

      return result;

    } catch (error) {
      logger.error(`Audit run ${auditId} failed:`, error as Error);

      await this.storage.updateAuditRun(auditId, {
        status: AuditStatus.FAILED,
        completedAt: new Date(),
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      });

      throw error;
    }
  }

  private getEnabledAnalysisSteps(config: AuditRunConfig) {
    const allSteps = [
      { name: 'TypeScript Analysis', analyzer: () => this.analyzeTypeScript() },
      { name: 'ESLint Integration', analyzer: () => this.integrateESLint() },
      { name: 'Security Analysis', analyzer: () => this.analyzeSecurity() },
      { name: 'Performance Analysis', analyzer: () => this.analyzePerformance() },
      { name: 'Architecture Analysis', analyzer: () => this.analyzeArchitecture() },
      { name: 'Prisma Schema Analysis', analyzer: () => this.integratePrismaSchema() },
      { name: 'Test Coverage Analysis', analyzer: () => this.analyzeTestCoverage() },
      { name: 'API Consistency Analysis', analyzer: () => this.analyzeAPIConsistency() },
      { name: 'Component Pattern Analysis', analyzer: () => this.analyzeComponentPatterns() },
      { name: 'Dependency Analysis', analyzer: () => this.analyzeDependencies() }
    ];

    // Filter based on config
    if (config.includeCategories?.length) {
      return allSteps.filter(step => this.stepMatchesCategories(step.name, config.includeCategories!));
    }

    if (config.excludeCategories?.length) {
      return allSteps.filter(step => !this.stepMatchesCategories(step.name, config.excludeCategories!));
    }

    return allSteps;
  }

  private stepMatchesCategories(stepName: string, categories: IssueCategory[]): boolean {
    const stepCategoryMap: Record<string, IssueCategory[]> = {
      'TypeScript Analysis': [IssueCategory.MAINTAINABILITY, IssueCategory.ARCHITECTURE],
      'ESLint Integration': [IssueCategory.MAINTAINABILITY],
      'Security Analysis': [IssueCategory.SECURITY],
      'Performance Analysis': [IssueCategory.PERFORMANCE],
      'Architecture Analysis': [IssueCategory.ARCHITECTURE],
      'Prisma Schema Analysis': [IssueCategory.PERFORMANCE, IssueCategory.SECURITY],
      'Test Coverage Analysis': [IssueCategory.TESTING],
      'API Consistency Analysis': [IssueCategory.ARCHITECTURE],
      'Component Pattern Analysis': [IssueCategory.MAINTAINABILITY, IssueCategory.ACCESSIBILITY],
      'Dependency Analysis': [IssueCategory.SECURITY, IssueCategory.MAINTAINABILITY]
    };

    const stepCategories = stepCategoryMap[stepName] || [];
    return stepCategories.some(cat => categories.includes(cat));
  }

  private filterIssues(issues: BaseIssue[], config: AuditRunConfig): BaseIssue[] {
    let filtered = [...issues];

    // Filter by severity threshold
    if (config.severityThreshold) {
      const severityOrder = [IssueSeverity.LOW, IssueSeverity.MEDIUM, IssueSeverity.HIGH, IssueSeverity.CRITICAL];
      const thresholdIndex = severityOrder.indexOf(config.severityThreshold);
      filtered = filtered.filter(issue => severityOrder.indexOf(issue.severity) >= thresholdIndex);
    }

    // Filter by categories
    if (config.includeCategories?.length) {
      filtered = filtered.filter(issue => config.includeCategories!.includes(issue.category));
    }

    if (config.excludeCategories?.length) {
      filtered = filtered.filter(issue => !config.excludeCategories!.includes(issue.category));
    }

    // Filter by paths
    if (config.includePaths?.length) {
      filtered = filtered.filter(issue => 
        config.includePaths!.some(pattern => this.matchesPattern(issue.filePath, pattern))
      );
    }

    if (config.excludePaths?.length) {
      filtered = filtered.filter(issue => 
        !config.excludePaths!.some(pattern => this.matchesPattern(issue.filePath, pattern))
      );
    }

    // Limit results
    if (config.maxIssues && filtered.length > config.maxIssues) {
      // Sort by severity and take top issues
      filtered = this.sortIssuesBySeverity(filtered).slice(0, config.maxIssues);
    }

    return filtered;
  }

  private matchesPattern(filePath: string, pattern: string): boolean {
    // Simple glob-like pattern matching
    const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
    return regex.test(filePath);
  }

  private sortIssuesBySeverity(issues: BaseIssue[]): BaseIssue[] {
    const severityOrder = {
      [IssueSeverity.CRITICAL]: 4,
      [IssueSeverity.HIGH]: 3,
      [IssueSeverity.MEDIUM]: 2,
      [IssueSeverity.LOW]: 1
    };

    return issues.sort((a, b) => severityOrder[b.severity] - severityOrder[a.severity]);
  }

  private processIssues(issues: BaseIssue[]): BaseIssue[] {
    // Remove duplicates based on file path, line number, and title
    const seen = new Set<string>();
    return issues.filter(issue => {
      const key = `${issue.filePath}:${issue.lineNumber}:${issue.title}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  private calculateSeverityCounts(issues: BaseIssue[]): Record<IssueSeverity, number> {
    return issues.reduce((counts, issue) => {
      counts[issue.severity] = (counts[issue.severity] || 0) + 1;
      return counts;
    }, {} as Record<IssueSeverity, number>);
  }
}
