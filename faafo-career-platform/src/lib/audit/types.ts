/**
 * Audit System Type Definitions
 * 
 * Comprehensive type definitions for the codebase audit system
 * including issue classification, analysis results, and engine interfaces.
 */

// Core Enums (matching Prisma schema)
export enum AuditStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export enum IssueSeverity {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW'
}

export enum IssueCategory {
  SECURITY = 'SECURITY',
  PERFORMANCE = 'PERFORMANCE',
  MAINTAINABILITY = 'MAINTAINABILITY',
  TESTING = 'TESTING',
  DOCUMENTATION = 'DOCUMENTATION',
  ARCHITECTURE = 'ARCHITECTURE',
  ACCESSIBILITY = 'ACCESSIBILITY'
}

export enum IssueStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  DEFERRED = 'DEFERRED',
  FALSE_POSITIVE = 'FALSE_POSITIVE'
}

// Analysis Result Types
export interface BaseIssue {
  severity: IssueSeverity;
  category: IssueCategory;
  title: string;
  description: string;
  filePath: string;
  lineNumber?: number;
  columnNumber?: number;
  codeSnippet?: string;
  recommendation?: string;
  fixExample?: string;
  metadata?: Record<string, any>;
}

export interface TypeScriptIssue extends BaseIssue {
  category: IssueCategory.MAINTAINABILITY | IssueCategory.ARCHITECTURE;
  tsErrorCode?: number;
  tsErrorMessage?: string;
}

export interface SecurityVulnerability extends BaseIssue {
  category: IssueCategory.SECURITY;
  cweId?: string;
  cvssScore?: number;
  exploitability?: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface PerformanceIssue extends BaseIssue {
  category: IssueCategory.PERFORMANCE;
  impactLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  estimatedImpact?: string;
}

export interface ArchitecturalDebt extends BaseIssue {
  category: IssueCategory.ARCHITECTURE;
  debtType?: 'CIRCULAR_DEPENDENCY' | 'TIGHT_COUPLING' | 'VIOLATION' | 'COMPLEXITY';
  refactoringEffort?: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface LintIssue extends BaseIssue {
  category: IssueCategory.MAINTAINABILITY;
  ruleId: string;
  ruleName: string;
  fixable?: boolean;
}

export interface DatabaseIssue extends BaseIssue {
  category: IssueCategory.PERFORMANCE | IssueCategory.SECURITY;
  queryType?: string;
  optimizationSuggestion?: string;
}

export interface CoverageGap extends BaseIssue {
  category: IssueCategory.TESTING;
  coverageType: 'LINE' | 'BRANCH' | 'FUNCTION' | 'STATEMENT';
  currentCoverage: number;
  targetCoverage: number;
}

export interface APIInconsistency extends BaseIssue {
  category: IssueCategory.ARCHITECTURE;
  inconsistencyType: 'NAMING' | 'RESPONSE_FORMAT' | 'ERROR_HANDLING' | 'VALIDATION';
  expectedPattern?: string;
  actualPattern?: string;
}

export interface ComponentIssue extends BaseIssue {
  category: IssueCategory.MAINTAINABILITY | IssueCategory.ACCESSIBILITY;
  componentName: string;
  issueType: 'PROP_TYPES' | 'ACCESSIBILITY' | 'PERFORMANCE' | 'PATTERNS';
}

export interface DependencyIssue extends BaseIssue {
  category: IssueCategory.SECURITY | IssueCategory.MAINTAINABILITY;
  dependencyName: string;
  currentVersion: string;
  recommendedVersion?: string;
  vulnerabilityId?: string;
}

// Audit Run Types
export interface AuditRunConfig {
  includeCategories?: IssueCategory[];
  excludeCategories?: IssueCategory[];
  includePaths?: string[];
  excludePaths?: string[];
  maxIssues?: number;
  severityThreshold?: IssueSeverity;
  enableAIAnalysis?: boolean;
  enablePerformanceAnalysis?: boolean;
  enableSecurityScan?: boolean;
}

export interface AuditRunResult {
  id: string;
  status: AuditStatus;
  startedAt: Date;
  completedAt?: Date;
  totalIssues: number;
  criticalCount: number;
  highCount: number;
  mediumCount: number;
  lowCount: number;
  issues: BaseIssue[];
  metadata?: Record<string, any>;
}

export interface AuditRunSummary {
  id: string;
  status: AuditStatus;
  startedAt: Date;
  completedAt?: Date;
  totalIssues: number;
  issuesBySeverity: Record<IssueSeverity, number>;
  issuesByCategory: Record<IssueCategory, number>;
  triggeredBy?: string;
}

// Engine Interface
export interface AuditEngine {
  // Static analysis capabilities
  analyzeTypeScript(): Promise<TypeScriptIssue[]>;
  analyzeSecurity(): Promise<SecurityVulnerability[]>;
  analyzePerformance(): Promise<PerformanceIssue[]>;
  analyzeArchitecture(): Promise<ArchitecturalDebt[]>;
  
  // Integration with existing tools
  integrateESLint(): Promise<LintIssue[]>;
  integratePrismaSchema(): Promise<DatabaseIssue[]>;
  analyzeTestCoverage(): Promise<CoverageGap[]>;
  
  // Custom analyzers
  analyzeAPIConsistency(): Promise<APIInconsistency[]>;
  analyzeComponentPatterns(): Promise<ComponentIssue[]>;
  analyzeDependencies(): Promise<DependencyIssue[]>;
  
  // Main execution
  runAudit(config?: AuditRunConfig): Promise<AuditRunResult>;
}

// Analysis Context
export interface AnalysisContext {
  projectRoot: string;
  sourceDir: string;
  configFiles: {
    tsconfig?: string;
    eslint?: string;
    prisma?: string;
    package?: string;
  };
  excludePatterns: string[];
  includePatterns: string[];
}

// Progress Tracking
export interface AuditProgress {
  phase: string;
  progress: number; // 0-100
  currentFile?: string;
  estimatedTimeRemaining?: number;
  issuesFound: number;
}

export type AuditProgressCallback = (progress: AuditProgress) => void;

// Filter and Search Types
export interface IssueFilter {
  severity?: IssueSeverity[];
  category?: IssueCategory[];
  status?: IssueStatus[];
  filePath?: string;
  assignedTo?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface IssueSortOptions {
  field: 'severity' | 'category' | 'filePath' | 'createdAt' | 'updatedAt';
  direction: 'asc' | 'desc';
}

// API Response Types
export interface AuditAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

export interface PaginatedIssues {
  issues: BaseIssue[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}
