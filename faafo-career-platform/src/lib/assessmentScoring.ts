// Assessment scoring and insights generation

export interface AssessmentScores {
  readinessScore: number; // 0-100
  riskTolerance: number; // 1-5
  urgencyLevel: number; // 1-5
  skillsConfidence: number; // 0-100
  supportLevel: number; // 1-5
  financialReadiness: number; // 1-5
}

export interface SkillGap {
  skill: string;
  currentLevel: number; // 0-5
  requiredLevel: number; // 0-5
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedLearningTime: string;
  recommendedResources: string[];
}

export interface CareerPathAnalysis {
  careerPath: string;
  matchPercentage: number;
  matchReason: string;
  salaryRange: {
    min: number;
    max: number;
    currency: string;
  };
  jobGrowthRate: string;
  skillGaps: SkillGap[];
  strengthsAlignment: string[];
  nextSteps: string[];
}

export interface AssessmentInsights {
  scores: AssessmentScores;
  primaryMotivation: string;
  topSkills: string[];
  biggestObstacles: string[];
  recommendedTimeline: string;
  keyRecommendations: string[];
  careerPathSuggestions: string[];
  // Enhanced fields
  careerPathAnalysis: CareerPathAnalysis[];
  overallSkillGaps: SkillGap[];
  learningPriorities: string[];
  estimatedTransitionTime: string;
}

export interface AssessmentResponse {
  [key: string]: string | string[] | number | null;
}

// Helper function to safely get array values
const getArrayValue = (value: string | string[] | number | null): string[] => {
  if (Array.isArray(value)) {
    return value.filter(v => typeof v === 'string') as string[];
  }
  if (typeof value === 'string') {
    return [value];
  }
  return [];
};

// Helper function to safely get string value
const getStringValue = (value: string | string[] | number | null): string => {
  if (typeof value === 'string') {
    return value;
  }
  if (Array.isArray(value) && value.length > 0) {
    return String(value[0]);
  }
  return '';
};

// Helper function to safely get number value
const getNumberValue = (value: string | string[] | number | null): number => {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

export function calculateAssessmentScores(responses: AssessmentResponse): AssessmentScores {
  // Financial comfort (1-5 scale)
  const financialReadiness = getNumberValue(responses.financial_comfort) || 1;
  
  // Risk tolerance (1-5 scale)
  const riskTolerance = getNumberValue(responses.risk_tolerance) || 1;
  
  // Support system (1-5 scale)
  const supportLevel = getNumberValue(responses.support_system) || 1;
  
  // Confidence level (1-5 scale)
  const confidenceLevel = getNumberValue(responses.confidence_level) || 1;
  
  // Calculate urgency based on timeline and employment status
  const timeline = getStringValue(responses.transition_timeline);
  let urgencyLevel = 3; // Default medium urgency
  
  switch (timeline) {
    case 'immediate':
      urgencyLevel = 5;
      break;
    case 'short_term':
      urgencyLevel = 4;
      break;
    case 'medium_term':
      urgencyLevel = 3;
      break;
    case 'long_term':
      urgencyLevel = 2;
      break;
    case 'extended':
    case 'exploring':
      urgencyLevel = 1;
      break;
  }
  
  // Calculate skills confidence based on selected skills
  const topSkills = getArrayValue(responses.top_skills);
  const skillsConfidence = Math.min(100, topSkills.length * 20); // 20 points per skill, max 100
  
  // Calculate overall readiness score (weighted average)
  const readinessScore = Math.round(
    (financialReadiness * 0.25 +
     confidenceLevel * 0.25 +
     supportLevel * 0.2 +
     riskTolerance * 0.15 +
     (skillsConfidence / 100 * 5) * 0.15) * 20 // Convert to 0-100 scale
  );
  
  return {
    readinessScore: Math.max(0, Math.min(100, readinessScore)),
    riskTolerance,
    urgencyLevel,
    skillsConfidence,
    supportLevel,
    financialReadiness,
  };
}

export async function generateAssessmentInsights(responses: AssessmentResponse): Promise<AssessmentInsights> {
  const scores = calculateAssessmentScores(responses);
  
  // Extract key data
  const primaryMotivation = getStringValue(responses.career_change_motivation);
  const topSkills = getArrayValue(responses.top_skills);
  const biggestObstacles = getArrayValue(responses.biggest_obstacles);
  const timeline = getStringValue(responses.transition_timeline);
  
  // Generate timeline recommendation
  let recommendedTimeline = 'Take time to plan carefully';
  if (scores.readinessScore >= 80) {
    recommendedTimeline = 'You appear ready to move forward soon';
  } else if (scores.readinessScore >= 60) {
    recommendedTimeline = 'Consider a 3-6 month preparation period';
  } else if (scores.readinessScore >= 40) {
    recommendedTimeline = 'Plan for 6-12 months of preparation';
  } else {
    recommendedTimeline = 'Focus on building readiness over 12+ months';
  }
  
  // Generate key recommendations
  const keyRecommendations: string[] = [];
  
  if (scores.financialReadiness < 3) {
    keyRecommendations.push('Build your emergency fund before making the transition');
  }
  
  if (scores.supportLevel < 3) {
    keyRecommendations.push('Build a stronger support network of mentors and peers');
  }
  
  if (scores.skillsConfidence < 60) {
    keyRecommendations.push('Focus on developing and validating your key skills');
  }
  
  if (biggestObstacles.includes('fear_of_failure')) {
    keyRecommendations.push('Work on mindset and confidence-building exercises');
  }
  
  if (biggestObstacles.includes('unclear_direction')) {
    keyRecommendations.push('Spend time clarifying your career vision and goals');
  }
  
  // Generate career path suggestions using integrated assessment service
  let careerPathSuggestions: string[] = [];

  try {
    // Use the assessment service integration layer
    const { AssessmentServiceIntegration } = require('./assessmentServiceIntegration');
    const result = await AssessmentServiceIntegration.generateCareerRecommendations(responses, {
      scores,
      primaryMotivation,
      topSkills,
      biggestObstacles,
      recommendedTimeline,
      keyRecommendations,
      careerPathSuggestions: [],
      careerPathAnalysis: [],
      overallSkillGaps: [],
      learningPriorities: topSkills.slice(0, 3),
      estimatedTransitionTime: recommendedTimeline
    });

    careerPathSuggestions = result.careerRecommendations;

  } catch (error) {
    console.warn('Assessment service integration unavailable, using fallback:', error);
    careerPathSuggestions = generateFallbackCareerSuggestions(topSkills, responses);
  }

  // Add financial literacy recommendation for low financial readiness
  if (scores.financialReadiness < 3) {
    keyRecommendations.push('Learn financial planning and budgeting for career transitions');
  }

  // Ensure we have at least some suggestions
  if (careerPathSuggestions.length === 0) {
    careerPathSuggestions = ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'];
  }
  
  return {
    scores,
    primaryMotivation,
    topSkills,
    biggestObstacles,
    recommendedTimeline,
    keyRecommendations,
    careerPathSuggestions: Array.from(new Set(careerPathSuggestions)), // Remove duplicates
    // Enhanced fields - will be populated by EnhancedAssessmentService
    careerPathAnalysis: [],
    overallSkillGaps: [],
    learningPriorities: topSkills.slice(0, 3), // Use top skills as initial learning priorities
    estimatedTransitionTime: recommendedTimeline
  };
}

// Function to get a readiness level description
export function getReadinessLevel(score: number): string {
  if (score >= 80) return 'High Readiness';
  if (score >= 60) return 'Moderate Readiness';
  if (score >= 40) return 'Building Readiness';
  return 'Early Exploration';
}

// Function to get readiness color for UI
export function getReadinessColor(score: number): string {
  if (score >= 80) return 'text-green-600 dark:text-green-400';
  if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
  if (score >= 40) return 'text-orange-600 dark:text-orange-400';
  return 'text-red-600 dark:text-red-400';
}

/**
 * Enhanced fallback career suggestions with better logic
 */
function generateFallbackCareerSuggestions(
  topSkills: string[],
  responses: AssessmentResponse
): string[] {
  const suggestions: string[] = [];
  const skillDevelopmentInterests = getArrayValue(responses.skill_development_interest);
  const careerValues = getArrayValue(responses.career_values);

  // Technical programming paths
  if (topSkills.includes('technical_programming')) {
    suggestions.push('Full-Stack Web Developer', 'Mobile App Developer');

    if (skillDevelopmentInterests.includes('ai_ml') || topSkills.includes('data_analysis')) {
      suggestions.push('AI/ML Engineer', 'Data Scientist');
    }

    if (skillDevelopmentInterests.includes('cybersecurity')) {
      suggestions.push('Cybersecurity Specialist');
    }
  }

  // Creative and content paths
  if (topSkills.includes('writing_content') || topSkills.includes('design_creative')) {
    suggestions.push('UX/UI Designer', 'Content Creator');

    if (skillDevelopmentInterests.includes('digital_marketing')) {
      suggestions.push('Digital Marketing Specialist');
    }
  }

  // Business and leadership paths
  if (topSkills.includes('sales_marketing') || topSkills.includes('leadership')) {
    suggestions.push('Digital Marketing Specialist', 'Product Manager');

    if (skillDevelopmentInterests.includes('entrepreneurship') || careerValues.includes('autonomy')) {
      suggestions.push('Entrepreneur / Startup Founder');
    }
  }

  // Education and coaching paths
  if (topSkills.includes('teaching_training') || topSkills.includes('coaching_mentoring')) {
    suggestions.push('Online Coaching', 'Course Creation', 'Training Specialist');
  }

  // Data and analysis paths
  if (topSkills.includes('data_analysis')) {
    suggestions.push('Data Scientist', 'Business Analyst');

    if (skillDevelopmentInterests.includes('financial_planning')) {
      suggestions.push('Financial Advisor / Planner');
    }
  }

  // Project management paths
  if (topSkills.includes('project_management')) {
    suggestions.push('Product Manager', 'Project Manager', 'Scrum Master');
  }

  // Remove duplicates and return top suggestions
  const uniqueSuggestions = Array.from(new Set(suggestions));

  // If still no matches, provide diverse default options
  if (uniqueSuggestions.length === 0) {
    return [
      'Digital Marketing Specialist',
      'Full-Stack Web Developer',
      'Product Manager',
      'Data Analyst',
      'UX/UI Designer'
    ];
  }

  return uniqueSuggestions.slice(0, 5);
}
