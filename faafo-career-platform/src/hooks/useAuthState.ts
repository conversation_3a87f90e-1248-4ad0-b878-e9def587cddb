/**
 * React hook for consistent authentication state management
 * Provides unified authentication state across all components
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { getAuthStateManager, AuthState } from '@/lib/auth-state-manager';

export interface UseAuthStateOptions {
  requireAuth?: boolean;
  requireAdmin?: boolean;
  redirectTo?: string;
  onAuthChange?: (state: AuthState) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UseAuthStateReturn extends AuthState {
  refreshAuth: () => Promise<void>;
  logout: (reason?: string) => Promise<void>;
  updateActivity: () => void;
  isSessionValid: () => Promise<boolean>;
  hasPermission: (permission: string) => boolean;
  isReady: boolean;
}

/**
 * Main authentication state hook
 */
export function useAuthState(options: UseAuthStateOptions = {}): UseAuthStateReturn {
  const {
    requireAuth = false,
    requireAdmin = false,
    redirectTo = '/login',
    onAuthChange,
    autoRefresh = true,
    refreshInterval = 300000 // 5 minutes
  } = options;

  const router = useRouter();
  const { data: session, status } = useSession();
  const authManager = getAuthStateManager();
  const [authState, setAuthState] = useState<AuthState>(authManager.getState());
  const [isReady, setIsReady] = useState(false);
  const refreshTimer = useRef<NodeJS.Timeout | null>(null);

  // Subscribe to auth state changes
  useEffect(() => {
    const unsubscribe = authManager.subscribe((newState) => {
      setAuthState(newState);
      if (onAuthChange) {
        onAuthChange(newState);
      }
    });

    return unsubscribe;
  }, [authManager, onAuthChange]);

  // Initial state synchronization and real-time updates
  useEffect(() => {
    const syncState = async () => {
      if (status !== 'loading') {
        await authManager.refreshState();
        setIsReady(true);
      }
    };

    // Set up real-time state listener
    const unsubscribe = authManager.addStateChangeListener((newState) => {
      setAuthState(newState);
      if (onAuthChange) {
        onAuthChange(newState);
      }
    });

    // Initial sync
    syncState();

    // Cleanup listener on unmount
    return () => {
      unsubscribe();
    };
  }, [status, authManager, onAuthChange]);

  // Auto-refresh setup
  useEffect(() => {
    if (autoRefresh && authState.isAuthenticated && !authState.isLoading) {
      refreshTimer.current = setInterval(async () => {
        await authManager.validateSession();
      }, refreshInterval);

      return () => {
        if (refreshTimer.current) {
          clearInterval(refreshTimer.current);
        }
      };
    }
  }, [autoRefresh, authState.isAuthenticated, authState.isLoading, refreshInterval, authManager]);

  // Handle authentication requirements
  useEffect(() => {
    if (!isReady || authState.isLoading) {
      return;
    }

    // Check authentication requirement
    if (requireAuth && !authState.isAuthenticated) {
      const currentPath = typeof window !== 'undefined'
        ? window.location.pathname + window.location.search
        : '/';
      const loginUrl = `${redirectTo}?callbackUrl=${encodeURIComponent(currentPath)}`;
      router.push(loginUrl);
      return;
    }

    // Check admin requirement
    if (requireAdmin && authState.isAuthenticated && !authState.isAdmin) {
      router.push('/unauthorized');
      return;
    }

    // Redirect authenticated users away from auth pages
    if (authState.isAuthenticated && typeof window !== 'undefined' &&
        (window.location.pathname === '/login' || window.location.pathname === '/signup')) {
      router.push('/dashboard');
      return;
    }
  }, [isReady, authState.isLoading, authState.isAuthenticated, authState.isAdmin, requireAuth, requireAdmin, redirectTo, router]);

  // Refresh authentication state
  const refreshAuth = useCallback(async () => {
    await authManager.refreshState();
  }, [authManager]);

  // Logout function
  const logout = useCallback(async (reason?: string) => {
    await authManager.forceLogout(reason);
    router.push('/login');
  }, [authManager, router]);

  // Update activity
  const updateActivity = useCallback(() => {
    authManager.updateLastActivity();
  }, [authManager]);

  // Validate session
  const isSessionValid = useCallback(async (): Promise<boolean> => {
    return await authManager.validateSession();
  }, [authManager]);

  // Check permissions
  const hasPermission = useCallback((permission: string): boolean => {
    if (!authState.isAuthenticated) {
      return false;
    }

    // Basic permission checks
    switch (permission) {
      case 'admin':
        return authState.isAdmin;
      case 'user':
        return authState.isAuthenticated;
      case 'interview_practice':
        return authState.isAuthenticated;
      case 'career_paths':
        return authState.isAuthenticated;
      case 'resume_builder':
        return authState.isAuthenticated;
      case 'progress_tracking':
        return authState.isAuthenticated;
      case 'admin_panel':
        return authState.isAdmin;
      case 'user_management':
        return authState.isAdmin;
      default:
        return false;
    }
  }, [authState.isAuthenticated, authState.isAdmin]);

  return {
    ...authState,
    refreshAuth,
    logout,
    updateActivity,
    isSessionValid,
    hasPermission,
    isReady
  };
}

/**
 * Hook for components that require authentication
 */
export function useRequireAuth(redirectTo = '/login') {
  return useAuthState({
    requireAuth: true,
    redirectTo
  });
}

/**
 * Hook for components that require admin access
 */
export function useRequireAdmin(redirectTo = '/unauthorized') {
  return useAuthState({
    requireAuth: true,
    requireAdmin: true,
    redirectTo
  });
}

/**
 * Hook for authentication status only (no redirects)
 */
export function useAuthStatus() {
  return useAuthState({
    requireAuth: false,
    autoRefresh: false
  });
}

/**
 * Hook for session monitoring with activity tracking
 */
export function useSessionMonitor(options: {
  onSessionExpired?: () => void;
  onInactivity?: () => void;
  activityEvents?: string[];
} = {}) {
  const {
    onSessionExpired,
    onInactivity,
    activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
  } = options;

  const authState = useAuthState({
    autoRefresh: true,
    onAuthChange: (state) => {
      if (state.error === 'Session expired' && onSessionExpired) {
        onSessionExpired();
      }
    }
  });

  // Track user activity
  useEffect(() => {
    if (!authState.isAuthenticated) {
      return;
    }

    const handleActivity = () => {
      authState.updateActivity();
    };

    // Add activity listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Check for inactivity
    const inactivityTimer = setInterval(() => {
      if (authState.lastActivity) {
        const timeSinceActivity = Date.now() - authState.lastActivity;
        const inactivityThreshold = 30 * 60 * 1000; // 30 minutes

        if (timeSinceActivity > inactivityThreshold && onInactivity) {
          onInactivity();
        }
      }
    }, 60000); // Check every minute

    return () => {
      // Remove activity listeners
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearInterval(inactivityTimer);
    };
  }, [authState.isAuthenticated, authState.updateActivity, authState.lastActivity, activityEvents, onInactivity]);

  return authState;
}

/**
 * Hook for permission-based rendering
 */
export function usePermissions() {
  const authState = useAuthState();

  const can = useCallback((permission: string): boolean => {
    return authState.hasPermission(permission);
  }, [authState]);

  const canAny = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => authState.hasPermission(permission));
  }, [authState]);

  const canAll = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => authState.hasPermission(permission));
  }, [authState]);

  return {
    can,
    canAny,
    canAll,
    isAuthenticated: authState.isAuthenticated,
    isAdmin: authState.isAdmin,
    user: authState.user
  };
}

/**
 * Hook for cross-tab authentication synchronization
 */
export function useCrossTabAuth() {
  const authState = useAuthState();

  useEffect(() => {
    // Only set up cross-tab sync if window is available (client-side)
    if (typeof window === 'undefined') return;

    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'auth_logout') {
        // Another tab logged out
        authState.logout('Logged out from another tab');
      } else if (event.key === 'auth_login') {
        // Another tab logged in
        authState.refreshAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [authState]);

  return authState;
}
