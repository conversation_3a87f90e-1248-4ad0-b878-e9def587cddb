'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';

export interface SessionState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  sessionId?: string;
  lastActivity?: number;
  isExpired: boolean;
  isOnline: boolean;
}

export interface SessionMonitorOptions {
  redirectOnExpiry?: boolean;
  redirectTo?: string;
  warningBeforeExpiry?: number; // minutes
  onSessionExpired?: () => void;
  onSessionWarning?: (minutesLeft: number) => void;
  onSessionRestored?: () => void;
}

export function useSessionMonitor(options: SessionMonitorOptions = {}) {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  const [sessionState, setSessionState] = useState<SessionState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    isExpired: false,
    isOnline: true // Default to true for SSR, will be updated after mounting
  });

  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [warningShown, setWarningShown] = useState(false);

  // Memoize session values to prevent infinite re-renders
  const sessionValues = useMemo(() => ({
    isAuthenticated: status === 'authenticated' && !!session,
    isLoading: status === 'loading',
    user: session?.user || null,
    sessionId: (session as any)?.sessionId,
    lastActivity: (session as any)?.lastActivity,
  }), [status, session?.user, (session as any)?.sessionId, (session as any)?.lastActivity]);

  // Update session state when session changes
  useEffect(() => {
    setSessionState(prev => ({
      ...prev,
      ...sessionValues,
      isExpired: false
    }));
  }, [sessionValues]);

  // Set mounted state and initialize online status
  useEffect(() => {
    setMounted(true);

    // Only access browser APIs after mounting to prevent hydration mismatch
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      // Set initial online state from navigator
      setSessionState(prev => ({ ...prev, isOnline: navigator.onLine }));
    }
  }, []);

  // Monitor online/offline status - only after mounting
  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    const handleOnline = () => {
      setSessionState(prev => ({ ...prev, isOnline: true }));
      // Refresh session when coming back online
      if (sessionState.isAuthenticated) {
        update();
        options.onSessionRestored?.();
      }
    };

    const handleOffline = () => {
      setSessionState(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [mounted, sessionState.isAuthenticated, update, options]);

  // Track user activity
  const updateActivity = useCallback(() => {
    setLastActivityTime(Date.now());
    setWarningShown(false);
  }, []);

  useEffect(() => {
    if (!mounted || typeof document === 'undefined') return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [mounted, updateActivity]);

  // Session expiry monitoring
  useEffect(() => {
    if (!sessionState.isAuthenticated || !sessionState.lastActivity) {
      return;
    }

    const checkSessionExpiry = () => {
      const now = Math.floor(Date.now() / 1000);
      const sessionAge = now - (sessionState.lastActivity || 0);
      const maxAge = 30 * 24 * 60 * 60; // 30 days in seconds
      const warningTime = (options.warningBeforeExpiry || 5) * 60; // Convert minutes to seconds

      // Check if session is expired
      if (sessionAge >= maxAge) {
        setSessionState(prev => ({ ...prev, isExpired: true }));
        options.onSessionExpired?.();
        
        if (options.redirectOnExpiry) {
          router.push(options.redirectTo || '/login');
        }
        return;
      }

      // Check if we should show warning
      const timeUntilExpiry = maxAge - sessionAge;
      if (timeUntilExpiry <= warningTime && !warningShown) {
        const minutesLeft = Math.ceil(timeUntilExpiry / 60);
        setWarningShown(true);
        options.onSessionWarning?.(minutesLeft);
      }
    };

    const interval = setInterval(checkSessionExpiry, 60000); // Check every minute
    checkSessionExpiry(); // Check immediately

    return () => clearInterval(interval);
  }, [
    sessionState.isAuthenticated,
    sessionState.lastActivity,
    warningShown,
    options,
    router
  ]);

  // Refresh session periodically when online and active
  useEffect(() => {
    if (!sessionState.isAuthenticated || !sessionState.isOnline) {
      return;
    }

    const refreshSession = () => {
      const timeSinceActivity = Date.now() - lastActivityTime;
      const fiveMinutes = 5 * 60 * 1000;

      // Only refresh if user has been active in the last 5 minutes
      if (timeSinceActivity < fiveMinutes) {
        update();
      }
    };

    const interval = setInterval(refreshSession, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(interval);
  }, [sessionState.isAuthenticated, sessionState.isOnline, lastActivityTime, update]);

  const extendSession = useCallback(async () => {
    if (sessionState.isAuthenticated) {
      await update();
      updateActivity();
      setWarningShown(false);
    }
  }, [sessionState.isAuthenticated, update, updateActivity]);

  const forceRefresh = useCallback(async () => {
    await update();
  }, [update]);

  return {
    sessionState,
    extendSession,
    forceRefresh,
    updateActivity
  };
}

// Hook for components that need to ensure authentication
export function useRequireAuth(redirectTo = '/login') {
  const { sessionState } = useSessionMonitor({
    redirectOnExpiry: true,
    redirectTo
  });
  const router = useRouter();

  useEffect(() => {
    if (!sessionState.isLoading && !sessionState.isAuthenticated) {
      router.push(redirectTo);
    }
  }, [sessionState.isLoading, sessionState.isAuthenticated, router, redirectTo]);

  return sessionState;
}

// Hook for session timeout warnings
export function useSessionTimeout(options: {
  warningMinutes?: number;
  onWarning?: (minutesLeft: number) => void;
  onExpired?: () => void;
} = {}) {
  const [timeoutWarning, setTimeoutWarning] = useState<{
    show: boolean;
    minutesLeft: number;
  }>({ show: false, minutesLeft: 0 });

  const { sessionState, extendSession } = useSessionMonitor({
    warningBeforeExpiry: options.warningMinutes || 5,
    onSessionWarning: (minutesLeft) => {
      setTimeoutWarning({ show: true, minutesLeft });
      options.onWarning?.(minutesLeft);
    },
    onSessionExpired: () => {
      setTimeoutWarning({ show: false, minutesLeft: 0 });
      options.onExpired?.();
    }
  });

  const dismissWarning = useCallback(() => {
    setTimeoutWarning({ show: false, minutesLeft: 0 });
  }, []);

  const extendSessionAndDismiss = useCallback(async () => {
    await extendSession();
    dismissWarning();
  }, [extendSession, dismissWarning]);

  return {
    sessionState,
    timeoutWarning,
    dismissWarning,
    extendSession: extendSessionAndDismiss
  };
}
