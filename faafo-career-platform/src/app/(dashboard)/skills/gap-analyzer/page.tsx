'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import Head from 'next/head';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Target, TrendingUp, BookOpen, Info } from 'lucide-react';
import { toast } from 'sonner';
import { generateStructuredData, generateBreadcrumbData, generateFAQData } from '@/lib/seo/skill-gap-seo';
import { useCSRFToken } from '@/hooks/useCSRFToken';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import PageLayout from '@/components/layout/PageLayout';
import useMemoryManagement from '@/hooks/useMemoryManagement';

// Dynamic imports for heavy components
const SkillAssessmentForm = dynamic(() => import('@/components/skills/SkillAssessmentForm'), {
  loading: () => <div className="animate-pulse h-64 bg-gray-200 rounded-lg"></div>,
  ssr: false
});

const SkillGapAnalysis = dynamic(() => import('@/components/skills/SkillGapAnalysis'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
  ssr: false
});

interface Skill {
  id: string;
  name: string;
  category: string;
  description?: string;
}

interface SkillAssessment {
  skillId: string;
  skillName: string;
  selfRating: number;
  confidenceLevel: number;
  assessmentType: 'SELF_ASSESSMENT' | 'PEER_VALIDATION' | 'CERTIFICATION' | 'PERFORMANCE_BASED' | 'AI_EVALUATED';
  notes?: string;
  yearsOfExperience?: number;
  lastUsed?: string;
}

interface ComprehensiveAnalysisRequest {
  currentSkills: Array<{
    skillName: string;
    selfRating: number;
    confidenceLevel: number;
    yearsOfExperience?: number;
  }>;
  targetCareerPath: {
    careerPathName: string;
    targetLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  };
  preferences: {
    timeframe: 'THREE_MONTHS' | 'SIX_MONTHS' | 'ONE_YEAR' | 'TWO_YEARS';
    hoursPerWeek: number;
    learningStyle: string[];
    budget: 'FREE' | 'FREEMIUM' | 'PAID';
    focusAreas: string[];
  };
  includeMarketData: boolean;
  includePersonalizedPaths: boolean;
}

interface AnalysisResult {
  analysisId: string;
  skillGaps: any[];
  learningPlan: any;
  careerReadiness: any;
  marketInsights?: any;
  generatedAt: string;
  cached?: boolean;
  metadata?: {
    edgeCaseHandlerData?: any;
    performanceMetrics?: any;
    [key: string]: any;
  };
}

function SkillGapAnalyzerPageContent() {
  const [activeTab, setActiveTab] = useState('assess');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [userAssessments, setUserAssessments] = useState<SkillAssessment[]>([]);
  const [hasCompletedCareerAssessment, setHasCompletedCareerAssessment] = useState(false);
  const [targetCareerPath, setTargetCareerPath] = useState('');
  const [targetLevel, setTargetLevel] = useState<'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'>('INTERMEDIATE');
  const [preferences, setPreferences] = useState({
    timeframe: 'ONE_YEAR' as const,
    hoursPerWeek: 10,
    learningStyle: ['VISUAL'],
    budget: 'FREEMIUM' as const,
    focusAreas: ['Technical Skills'],
  });

  // Form state for persistent skill assessments across tab switches
  const [currentFormAssessments, setCurrentFormAssessments] = useState<SkillAssessment[]>([
    {
      skillId: '',
      skillName: '',
      selfRating: 5,
      confidenceLevel: 5,
      assessmentType: 'SELF_ASSESSMENT' as const,
      yearsOfExperience: 0,
      lastUsed: '',
      notes: '',
    }
  ]);

  // SEO structured data
  const structuredData = generateStructuredData({
    type: activeTab === 'assess' ? 'assessment' :
          activeTab === 'analyze' ? 'analysis' :
          activeTab === 'results' ? 'results' : 'assessment',
    data: {
      careerPath: targetCareerPath,
      skillCount: userAssessments.length,
      level: targetLevel,
    }
  });

  const breadcrumbData = generateBreadcrumbData(activeTab);
  const faqData = generateFAQData();

  // Memory management hook for cleanup
  const memoryManager = useMemoryManagement();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Memoized functions to prevent unnecessary re-renders
  const loadUserAssessments = useCallback(async () => {
    try {
      // Create abort controller for this request
      const { controller } = memoryManager.createAbortController();
      abortControllerRef.current = controller;

      const response = await fetch('/api/skills/assessment', {
        signal: controller.signal
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.assessments) {
          // Convert API response to SkillAssessment format
          const assessments: SkillAssessment[] = data.data.assessments.map((assessment: any) => ({
            skillId: assessment.skillId,
            skillName: assessment.skillName,
            selfRating: assessment.currentRating,
            confidenceLevel: assessment.confidenceLevel,
            assessmentType: 'SELF_ASSESSMENT' as const,
            lastUsed: assessment.lastAssessed,
          }));
          setUserAssessments(assessments);
        }
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error loading user assessments:', error);
      }
    }
  }, [memoryManager]);

  const checkCareerAssessmentStatus = useCallback(async () => {
    try {
      // Create abort controller for this request
      const { controller } = memoryManager.createAbortController();

      const response = await fetch('/api/assessment?status=true', {
        signal: controller.signal
      });

      if (response.ok) {
        const data = await response.json();
        // Check if user has a completed assessment
        const hasCompleted = data.status === 'COMPLETED';
        setHasCompletedCareerAssessment(hasCompleted);
      } else if (response.status === 404) {
        // No assessment found - user hasn't started one yet
        setHasCompletedCareerAssessment(false);
      } else {
        console.error('Assessment status check failed:', response.status);
        setHasCompletedCareerAssessment(false);
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error checking career assessment status:', error);
        setHasCompletedCareerAssessment(false);
      }
    }
  }, [memoryManager]);

  // Load existing assessments on component mount with proper cleanup
  useEffect(() => {
    loadUserAssessments();
    checkCareerAssessmentStatus();

    // Cleanup function to abort any pending requests
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      memoryManager.forceCleanup();
    };
  }, [loadUserAssessments, checkCareerAssessmentStatus, memoryManager]);

  const searchSkills = useCallback(async (query: string): Promise<Skill[]> => {
    try {
      // Create abort controller for this request
      const { controller } = memoryManager.createAbortController();

      const response = await fetch(`/api/skills/search?q=${encodeURIComponent(query)}`, {
        signal: controller.signal
      });

      if (response.ok) {
        const data = await response.json();
        return data.skills || [];
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error searching skills:', error);
      }
    }
    return [];
  }, [memoryManager]);

  const handleSkillAssessment = async (assessments: SkillAssessment[]) => {
    try {
      const response = await fetch('/api/skills/assessment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          assessments.length === 1
            ? assessments[0]
            : { assessments }
        ),
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please log in to save your skill assessments', {
            action: {
              label: 'Log In',
              onClick: () => window.location.href = '/login?callbackUrl=' + encodeURIComponent(window.location.pathname),
            },
          });
          return;
        }
        throw new Error(`HTTP ${response.status}: Failed to submit assessments`);
      }

      const data = await response.json();
      if (data.success) {
        toast.success('Skill assessments submitted successfully!');
        await loadUserAssessments(); // Reload assessments

        // Handle EdgeCaseHandler response data
        if (data.data?.edgeCaseHandlerData) {
          const edgeData = data.data.edgeCaseHandlerData;
          if (edgeData.isNewUser && edgeData.onboardingRecommendations) {
            toast.info('Welcome! Here are some recommendations to get started.', {
              description: edgeData.onboardingRecommendations.join(', '),
            });
          }
        }

        // If user has assessments, suggest running analysis
        if (assessments.length > 0) {
          toast.info('Ready to analyze your skill gaps!', {
            action: {
              label: 'Analyze Now',
              onClick: () => setActiveTab('analyze'),
            },
          });
        }
      } else {
        // Handle EdgeCaseHandler error responses
        if (data.fallbackData) {
          toast.warning('Using fallback data due to service issues');
        }
        if (data.suggestedAlternatives) {
          toast.info(`Consider these alternatives: ${data.suggestedAlternatives.join(', ')}`);
        }
        throw new Error(data.error || 'Failed to submit assessments');
      }
    } catch (error) {
      console.error('Error submitting assessments:', error);
      if (error instanceof Error && error.message.includes('401')) {
        toast.error('Authentication required to save assessments');
      } else {
        toast.error('Failed to submit skill assessments. Please try again.');
      }
      throw error;
    }
  };

  const handleComprehensiveAnalysis = async () => {
    if (userAssessments.length === 0 && !hasCompletedCareerAssessment) {
      toast.error('Please complete either skill assessments or the career assessment first');
      setActiveTab('assess');
      return;
    }

    if (!targetCareerPath.trim()) {
      toast.error('Please specify your target career path');
      return;
    }

    setIsAnalyzing(true);
    try {
      // If user has no skill assessments but has completed career assessment,
      // send empty currentSkills array - the API will extract skills from career assessment
      const currentSkills = userAssessments.length > 0
        ? userAssessments.map(assessment => ({
            skillName: assessment.skillName,
            selfRating: assessment.selfRating,
            confidenceLevel: assessment.confidenceLevel,
            yearsOfExperience: assessment.yearsOfExperience,
          }))
        : []; // Empty array - API will use career assessment data

      const analysisRequest: ComprehensiveAnalysisRequest = {
        currentSkills,
        targetCareerPath: {
          careerPathName: targetCareerPath,
          targetLevel,
        },
        preferences,
        includeMarketData: true,
        includePersonalizedPaths: true,
      };

      const response = await fetch('/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisRequest),
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please log in to run skill gap analysis', {
            action: {
              label: 'Log In',
              onClick: () => window.location.href = '/login?callbackUrl=' + encodeURIComponent(window.location.pathname),
            },
          });
          return;
        }
        throw new Error(`HTTP ${response.status}: Failed to analyze skill gaps`);
      }

      const data = await response.json();
      if (data.success) {
        setAnalysisResult(data.data);
        setActiveTab('results');
        toast.success('Skill gap analysis completed!');

        // Handle cached results
        if (data.cached) {
          toast.info('Analysis retrieved from cache for faster results');
        }
      } else {
        // Handle EdgeCaseHandler error responses
        if (data.fallbackData) {
          toast.warning('Using fallback analysis due to service issues');
          setAnalysisResult(data.fallbackData);
          setActiveTab('results');
        } else {
          throw new Error(data.error || 'Failed to analyze skill gaps');
        }
      }
    } catch (error) {
      console.error('Error analyzing skill gaps:', error);
      if (error instanceof Error && error.message.includes('401')) {
        toast.error('Authentication required for skill gap analysis');
      } else {
        toast.error('Failed to analyze skill gaps. Please try again.');
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <>
      <Head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(faqData) }}
        />
      </Head>

      <div className="container mx-auto py-8 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Skill Gap Analyzer</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Assess your current skills, identify gaps, and get personalized learning recommendations
            to advance your career goals.
          </p>
        </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assess" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Assess Skills
          </TabsTrigger>
          <TabsTrigger value="analyze" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analyze Gaps
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            View Results
          </TabsTrigger>
        </TabsList>

        {/* Skill Assessment Tab */}
        <TabsContent value="assess" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Step 1: Assess Your Current Skills</CardTitle>
              <CardDescription>
                Rate your proficiency and confidence in various skills to establish your baseline.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {(userAssessments.length > 0 || hasCompletedCareerAssessment) && (
                <Alert className="mb-6">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    {userAssessments.length > 0 && hasCompletedCareerAssessment ? (
                      <>You have {userAssessments.length} skill assessments and a completed career assessment. You can add more skills or proceed to gap analysis.</>
                    ) : userAssessments.length > 0 ? (
                      <>You have {userAssessments.length} existing skill assessments. You can add more or proceed to gap analysis.</>
                    ) : (
                      <>You have completed the career assessment. You can add skill assessments for more detailed analysis or proceed to gap analysis.</>
                    )}
                  </AlertDescription>
                </Alert>
              )}
              
              <SkillAssessmentForm
                onSubmit={handleSkillAssessment}
                onSkillSearch={searchSkills}
                mode="bulk"
                maxAssessments={20}
                preserveStateOnSubmit={true}
                initialAssessments={currentFormAssessments}
                onAssessmentsChange={setCurrentFormAssessments}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Gap Analysis Tab */}
        <TabsContent value="analyze" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Step 2: Analyze Your Skill Gaps</CardTitle>
              <CardDescription>
                Define your career goals and preferences to get personalized gap analysis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {userAssessments.length === 0 && !hasCompletedCareerAssessment ? (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      Please complete either skill assessments or the career assessment before running gap analysis.
                      <div className="flex gap-2 mt-2">
                        <Button
                          variant="link"
                          className="p-0 h-auto"
                          onClick={() => setActiveTab('assess')}
                        >
                          Add skill assessments
                        </Button>
                        <span className="text-gray-400">or</span>
                        <Button
                          variant="link"
                          className="p-0 h-auto"
                          onClick={() => window.location.href = '/assessment'}
                        >
                          Take career assessment
                        </Button>
                      </div>
                    </AlertDescription>
                  </Alert>
                ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Career Path Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Target Career Path</label>
                      <input
                        type="text"
                        placeholder="e.g., Full Stack Developer, Data Scientist"
                        value={targetCareerPath}
                        onChange={(e) => setTargetCareerPath(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    {/* Target Level */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Target Level</label>
                      <select
                        value={targetLevel}
                        onChange={(e) => setTargetLevel(e.target.value as any)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="BEGINNER">Beginner</option>
                        <option value="INTERMEDIATE">Intermediate</option>
                        <option value="ADVANCED">Advanced</option>
                        <option value="EXPERT">Expert</option>
                      </select>
                    </div>

                    {/* Timeframe */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Learning Timeframe</label>
                      <select
                        value={preferences.timeframe}
                        onChange={(e) => setPreferences(prev => ({ ...prev, timeframe: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="THREE_MONTHS">3 Months</option>
                        <option value="SIX_MONTHS">6 Months</option>
                        <option value="ONE_YEAR">1 Year</option>
                        <option value="TWO_YEARS">2 Years</option>
                      </select>
                    </div>

                    {/* Hours per Week */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Hours per Week</label>
                      <input
                        type="number"
                        min="1"
                        max="40"
                        value={preferences.hoursPerWeek}
                        onChange={(e) => setPreferences(prev => ({ ...prev, hoursPerWeek: parseInt(e.target.value) || 10 }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      onClick={handleComprehensiveAnalysis}
                      disabled={isAnalyzing || !targetCareerPath.trim()}
                      size="lg"
                      className="min-w-48"
                    >
                      {isAnalyzing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        'Analyze Skill Gaps'
                      )}
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {analysisResult ? (
            <SkillGapAnalysis
              analysisId={analysisResult.analysisId}
              skillGaps={analysisResult.skillGaps}
              learningPlan={analysisResult.learningPlan}
              careerReadiness={analysisResult.careerReadiness}
              marketInsights={analysisResult.marketInsights}
              generatedAt={analysisResult.generatedAt}
              cached={analysisResult.cached}
              edgeCaseHandlerData={analysisResult.metadata?.edgeCaseHandlerData}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto" />
                  <div>
                    <h3 className="text-lg font-medium">No Analysis Results</h3>
                    <p className="text-gray-500">
                      Complete your assessments and run gap analysis to see results here.
                    </p>
                  </div>
                  <Button 
                    onClick={() => setActiveTab('assess')}
                    variant="outline"
                  >
                    Start Assessment
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
    </>
  );
}

// Wrap the skills gap analyzer page with error boundary
export default function SkillGapAnalyzerPage() {
  return (
    <ErrorBoundary
      fallback={
        <PageLayout>
          <div className="max-w-4xl mx-auto p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Skills Gap Analyzer Error
              </h2>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an issue loading the skills gap analyzer. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </PageLayout>
      }
      onError={(error, errorInfo) => {
        console.error('Skills Gap Analyzer Page Error:', { error, errorInfo });
      }}
    >
      <SkillGapAnalyzerPageContent />
    </ErrorBoundary>
  );
}
