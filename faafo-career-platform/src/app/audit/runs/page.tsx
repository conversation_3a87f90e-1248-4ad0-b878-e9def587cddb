/**
 * Audit Runs Page - List all audit runs with filtering and pagination
 * 
 * Provides comprehensive view of all audit runs with status tracking,
 * filtering capabilities, and detailed run information.
 */

import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { requireAdmin } from '@/lib/auth-utils';
import { AuditRunsPage } from '@/components/audit/AuditRunsPage';

export const metadata: Metadata = {
  title: 'Audit Runs | FAAFO Career Platform',
  description: 'View and manage all codebase audit runs with detailed status tracking.',
  robots: 'noindex, nofollow', // Admin pages should not be indexed
};

export default async function AuditRunsListPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/audit/runs');
  }

  try {
    // Check if user has admin privileges
    await requireAdmin();
  } catch (error) {
    // Redirect to personal dashboard if not admin
    redirect('/dashboard?error=admin-required');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <AuditRunsPage />
      </div>
    </div>
  );
}
