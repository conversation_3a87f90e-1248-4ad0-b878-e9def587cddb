/**
 * Audit Dashboard - Main Overview Page
 * 
 * Provides comprehensive overview of codebase audit system with metrics,
 * recent issues, and quick actions for administrators.
 */

import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { requireAdmin } from '@/lib/auth-utils';
import { AuditDashboard } from '@/components/audit/AuditDashboard';

export const metadata: Metadata = {
  title: 'Audit Dashboard | FAAFO Career Platform',
  description: 'Comprehensive codebase audit system with quality analysis and issue tracking.',
  robots: 'noindex, nofollow', // Admin pages should not be indexed
};

export default async function AuditPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/audit');
  }

  try {
    // Check if user has admin privileges
    await requireAdmin();
  } catch (error) {
    // Redirect to personal dashboard if not admin
    redirect('/dashboard?error=admin-required');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <AuditDashboard />
      </div>
    </div>
  );
}
