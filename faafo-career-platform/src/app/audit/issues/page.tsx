/**
 * Audit Issues Page - List all issues with filtering and management
 * 
 * Provides comprehensive view of all audit issues with filtering,
 * sorting, assignment, and resolution capabilities.
 */

import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth';
import { requireAdmin } from '@/lib/auth-utils';
import { AuditIssuesPage } from '@/components/audit/AuditIssuesPage';

export const metadata: Metadata = {
  title: 'Audit Issues | FAAFO Career Platform',
  description: 'View and manage all codebase audit issues with filtering and assignment capabilities.',
  robots: 'noindex, nofollow', // Admin pages should not be indexed
};

export default async function AuditIssuesListPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/audit/issues');
  }

  try {
    // Check if user has admin privileges
    await requireAdmin();
  } catch (error) {
    // Redirect to personal dashboard if not admin
    redirect('/dashboard?error=admin-required');
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <AuditIssuesPage />
      </div>
    </div>
  );
}
