import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com';

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/career-paths',
          '/career-paths/',
          '/assessment',
          '/tools',
          '/tools/',
          '/resources',
          '/resources/',
          '/forum',
          '/forum/',
          '/freedom-fund',
          '/skills/gap-analyzer',
          '/contact',
          '/help',
          '/faq',
          '/privacy-policy',
          '/terms-of-service',
          '/login',
          '/signup',
        ],
        disallow: [
          '/api/',
          '/dashboard',
          '/dashboard/',
          '/progress',
          '/progress/',
          '/my-analytics',
          '/my-analytics/',
          '/interview-practice',
          '/interview-practice/',
          '/resume-builder',
          '/resume-builder/',
          '/admin/',
          '/auth/',
          '/_next/',
          '/private/',
          '*.json',
          '/skills/gap-analyzer?tab=results*', // User-specific results
          '/skills/gap-analyzer?analysis=*',   // Specific analysis results
          '/assessment/results/',              // User-specific assessment results
          '/profile',                          // User profiles
          '/profile/',
        ],
      },
      {
        userAgent: 'GPTBot',
        disallow: ['/'],
      },
      {
        userAgent: 'ChatGPT-User',
        disallow: ['/'],
      },
      {
        userAgent: 'CCBot',
        disallow: ['/'],
      },
      {
        userAgent: 'anthropic-ai',
        disallow: ['/'],
      },
      {
        userAgent: 'Claude-Web',
        disallow: ['/'],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
