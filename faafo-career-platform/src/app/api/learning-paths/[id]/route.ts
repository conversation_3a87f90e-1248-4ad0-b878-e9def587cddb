// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { z } from 'zod';
import { withCSRFProtection } from '@/lib/csrf';
import { isUserAdmin } from '@/lib/auth-utils';

const updateLearningPathSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).max(2000).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  estimatedHours: z.number().min(1).max(1000).optional(),
  category: z.enum(['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP', 'UX_UI_DESIGN', 'PRODUCT_MANAGEMENT', 'DEVOPS']).optional(),
  prerequisites: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  imageUrl: z.string().url().optional(),
  isActive: z.boolean().optional(),
  skillIds: z.array(z.string().uuid()).optional(),
  careerPathIds: z.array(z.string().uuid()).optional(),
});

// GET - Retrieve specific learning path
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 200 }, // 200 requests per 15 minutes
    async () => {
      const { id } = await params;
      const session = await getServerSession(authOptions);
      const userId = session?.user?.id;

        // Build cache key
        const cacheKey = `learning_path:${id}:${userId || 'anonymous'}`;
        
        // Check cache first
        const cached = await consolidatedCache.get<any>(cacheKey);
        if (cached) {
          return NextResponse.json({
            success: true,
            data: cached,
            cached: true
          });
        }

        // Find learning path by ID or slug
        const where = id.length === 36 ? { id } : { slug: id };

        const learningPath = await prisma.learningPath.findUnique({
          where,
          include: {
            skills: true,
            careerPaths: {
              select: {
                id: true,
                name: true,
                slug: true,
                overview: true,
              }
            },
            steps: {
              orderBy: { stepOrder: 'asc' },
              include: {
                resource: {
                  select: {
                    id: true,
                    title: true,
                    description: true,
                    type: true,
                    url: true,
                    author: true,
                    duration: true,
                    skillLevel: true,
                  }
                },
                userProgress: userId ? {
                  where: { userId },
                  select: {
                    id: true,
                    status: true,
                    startedAt: true,
                    completedAt: true,
                    timeSpent: true,
                    score: true,
                    notes: true,
                  }
                } : false,
              }
            },
            userPaths: userId ? {
              where: { userId },
              select: {
                id: true,
                status: true,
                startedAt: true,
                completedAt: true,
                lastAccessedAt: true,
                currentStepId: true,
                completedSteps: true,
                totalSteps: true,
                progressPercent: true,
                totalTimeSpent: true,
                notes: true,
                rating: true,
                review: true,
              }
            } : false,
            _count: {
              select: {
                steps: true,
                userPaths: true,
              }
            }
          }
        });

        if (!learningPath) {
          return NextResponse.json(
            { success: false, error: 'Learning path not found' },
            { status: 404 }
          );
        }

        if (!learningPath.isActive) {
          return NextResponse.json(
            { success: false, error: 'Learning path is not available' },
            { status: 404 }
          );
        }

        // Transform data
        const transformedPath = {
          ...learningPath,
          stepCount: learningPath._count.steps,
          enrollmentCount: learningPath._count.userPaths,
          userProgress: learningPath.userPaths?.[0] || null,
          steps: learningPath.steps.map(step => ({
            ...step,
            userProgress: step.userProgress?.[0] || null,
          })),
          _count: undefined,
          userPaths: undefined,
        };

      // Cache for 5 minutes
      await consolidatedCache.set(cacheKey, transformedPath, { ttl: 5 * 60 * 1000, tags: ['learning_paths', `learning_path:${id}`] });

      return NextResponse.json({
        success: true,
        data: transformedPath
      });
    }
  );
});

// PUT - Update learning path (admin only)
export const PUT = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 updates per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check admin access using proper role-based authorization
      const isAdmin = await isUserAdmin(session.user.id);
      if (!isAdmin) {
        return NextResponse.json(
          { success: false, error: 'Admin access required' },
          { status: 403 }
        );
      }

      const { id } = await params;
      const body = await request.json();
        const validation = updateLearningPathSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const {
          title,
          description,
          difficulty,
          estimatedHours,
          category,
          prerequisites,
          tags,
          imageUrl,
          isActive,
          skillIds,
          careerPathIds,
        } = validation.data;

        // Check if learning path exists
        const existingPath = await prisma.learningPath.findUnique({
          where: { id }
        });

        if (!existingPath) {
          return NextResponse.json(
            { success: false, error: 'Learning path not found' },
            { status: 404 }
          );
        }

        // Generate new slug if title changed
        let slug = existingPath.slug;
        if (title && title !== existingPath.title) {
          slug = title.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();

          // Check if new slug already exists
          const slugExists = await prisma.learningPath.findFirst({
            where: { 
              slug,
              id: { not: id }
            }
          });

          if (slugExists) {
            return NextResponse.json(
              { success: false, error: 'A learning path with this title already exists' },
              { status: 409 }
            );
          }
        }

        // Update learning path
        const updatedPath = await prisma.learningPath.update({
          where: { id },
          data: {
            ...(title && { title, slug }),
            ...(description && { description }),
            ...(difficulty && { difficulty }),
            ...(estimatedHours && { estimatedHours }),
            ...(category && { category }),
            ...(prerequisites !== undefined && { prerequisites }),
            ...(tags !== undefined && { tags }),
            ...(imageUrl !== undefined && { imageUrl }),
            ...(isActive !== undefined && { isActive }),
            ...(skillIds && {
              skills: {
                set: [], // Clear existing
                connect: skillIds.map(id => ({ id }))
              }
            }),
            ...(careerPathIds && {
              careerPaths: {
                set: [], // Clear existing
                connect: careerPathIds.map(id => ({ id }))
              }
            }),
          },
          include: {
            skills: true,
            careerPaths: {
              select: {
                id: true,
                name: true,
                slug: true,
              }
            },
            _count: {
              select: {
                steps: true,
                userPaths: true,
              }
            }
          }
        });

        // Clear cache
        await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${id}`]);

      return NextResponse.json({
        success: true,
        data: {
          ...updatedPath,
          stepCount: updatedPath._count.steps,
          enrollmentCount: updatedPath._count.userPaths,
          _count: undefined,
        },
        message: 'Learning path updated successfully'
      });
    }
  );
});

// DELETE - Delete learning path (admin only)
export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check admin access using proper role-based authorization
      const isAdmin = await isUserAdmin(session.user.id);
      if (!isAdmin) {
        return NextResponse.json(
          { success: false, error: 'Admin access required' },
          { status: 403 }
        );
      }
        const { id } = await params;

        // Check if learning path exists and has enrollments
        const learningPath = await prisma.learningPath.findUnique({
          where: { id },
          include: {
            _count: {
              select: {
                userPaths: true,
              }
            }
          }
        });

        if (!learningPath) {
          return NextResponse.json(
            { success: false, error: 'Learning path not found' },
            { status: 404 }
          );
        }

        // If there are enrollments, deactivate instead of delete
        if (learningPath._count.userPaths > 0) {
          await prisma.learningPath.update({
            where: { id },
            data: { isActive: false }
          });

          return NextResponse.json({
            success: true,
            message: 'Learning path deactivated due to existing enrollments'
          });
        }

        // Delete learning path (this will cascade to steps)
        await prisma.learningPath.delete({
          where: { id }
        });

      // Clear cache
      await consolidatedCache.invalidateByTags(['learning_paths', `learning_path:${id}`]);

      return NextResponse.json({
        success: true,
        message: 'Learning path deleted successfully'
      });
    }
  );
});
