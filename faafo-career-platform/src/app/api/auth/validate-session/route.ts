/**
 * API endpoint for session validation
 * Provides server-side session validation for the authentication state manager
 */

import { NextRequest, NextResponse } from 'next/server';
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import { isUserAdmin } from '@/lib/auth-utils';
import { rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface SessionValidationResponse {
  valid: boolean;
  user: {
    id: string;
    email: string;
    name: string | null;
    emailVerified: Date | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  isAdmin: boolean;
  sessionInfo?: {
    sessionId: string | null;
    issuedAt: number;
    expiresAt: number;
    lastActivity: number;
  };
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SessionValidationResponse>>> => {
  // Apply rate limiting
  const rateLimitResult = rateLimiters.api.check(request);

  if (!rateLimitResult.allowed) {
    const error = new Error('Too many session validation requests') as any;
    error.statusCode = 429;
    error.headers = {
      'X-RateLimit-Limit': rateLimitResult.limit.toString(),
      'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
      'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
      'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
    };
    throw error;
  }

  // Enhanced session validation using unified service
  const validation = await UnifiedAuthenticationService.validateSession(request, {
    validateUserExists: true,
    checkAccountLock: true,
    refreshSession: true,
    enableSecurityLogging: true,
    sessionType: 'general'
  });

  if (!validation.isValid) {
    const error = new Error(validation.error);
    (error as any).statusCode = validation.statusCode || 401;
    throw error;
  }

  // Check admin status
  let isAdmin = false;
  try {
    isAdmin = await isUserAdmin(validation.userId!);
  } catch (error) {
    console.error('Error checking admin status during session validation:', error);
    // Don't fail validation just because admin check failed
  }

  // Get current timestamp for session info
  const now = Math.floor(Date.now() / 1000);

  // Return successful validation with unified format
  return NextResponse.json({
    success: true,
    data: {
      valid: true,
      user: validation.user ? {
        id: validation.user.id,
        email: validation.user.email,
        name: validation.user.name,
        emailVerified: validation.user.emailVerified,
        createdAt: validation.user.createdAt || new Date(),
        updatedAt: validation.user.updatedAt || new Date()
      } : null,
      isAdmin,
      sessionInfo: {
        sessionId: validation.sessionData?.sessionId || null,
        issuedAt: validation.sessionData?.iat || now,
        expiresAt: validation.sessionData?.exp || (now + 30 * 24 * 60 * 60),
        lastActivity: now
      }
    }
  });
});

// Only allow GET requests
export const POST = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {
  const error = new Error('Method not allowed') as any;
  error.statusCode = 405;
  throw error;
});

export const PUT = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {
  const error = new Error('Method not allowed') as any;
  error.statusCode = 405;
  throw error;
});

export const DELETE = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<never>>> => {
  const error = new Error('Method not allowed') as any;
  error.statusCode = 405;
  throw error;
});
