// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { isAdminUser } from '@/lib/auth-utils';
import { AuditService } from '@/lib/audit';
import { IssueStatus } from '@/lib/audit/types';
import { z } from 'zod';
import { logger } from '@/lib/logger';

// Request validation schemas
const issueUpdateSchema = z.object({
  status: z.enum(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'DEFERRED', 'FALSE_POSITIVE']).optional(),
  assignedToId: z.string().nullable().optional(),
  falsePositive: z.boolean().optional(),
  resolution: z.string().optional()
});

// Response interfaces
interface AuditIssueDetailResponse {
  id: string;
  auditRunId: string;
  severity: string;
  category: string;
  title: string;
  description: string;
  filePath: string;
  lineNumber: number | null;
  columnNumber: number | null;
  codeSnippet: string | null;
  recommendation: string | null;
  fixExample: string | null;
  status: string;
  assignedToId: string | null;
  resolvedAt: Date | null;
  falsePositive: boolean;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
  assignedTo?: {
    id: string;
    name: string | null;
    email: string;
  };
  auditRun: {
    id: string;
    startedAt: Date;
    status: string;
  };
  comments: Array<{
    id: string;
    comment: string;
    createdAt: Date;
    user: {
      id: string;
      name: string | null;
      email: string;
    };
  }>;
}

interface IssueUpdateResponse {
  id: string;
  status: string;
  message: string;
}

// GET - Retrieve specific audit issue details
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<AuditIssueDetailResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 300 }, // 300 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      const { id } = await params;
      
      // Check authentication - audit access requires admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      if (!id) {
        const error = new Error('Issue ID is required') as any;
        error.statusCode = 400;
        throw error;
      }

      logger.info('Fetching audit issue details', {
        component: 'audit_api',
        action: 'get_issue_details',
        userId: session.user.email
      });

      // Initialize audit service and fetch issue details
      const auditService = new AuditService();
      const issue = await auditService.getIssue(id);

      if (!issue) {
        const error = new Error('Audit issue not found') as any;
        error.statusCode = 404;
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: issue,
        timestamp: new Date().toISOString()
      });
    }
  ) as Promise<NextResponse<ApiResponse<AuditIssueDetailResponse>>>;
});

// PATCH - Update audit issue (status, assignment, etc.)
export const PATCH = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<IssueUpdateResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 updates per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);
        const { id } = await params;
        
        // Check authentication - audit operations require admin privileges
        if (!session?.user?.email || !isAdminUser(session.user.email)) {
          const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
          error.statusCode = 403;
          throw error;
        }

        if (!id) {
          const error = new Error('Issue ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = issueUpdateSchema.safeParse(body);

        if (!validation.success) {
          const error = new Error('Invalid issue update data') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const updateData = validation.data;

        // Convert string status to enum if provided
        const processedUpdateData = {
          ...updateData,
          status: updateData.status ? updateData.status as IssueStatus : undefined
        };

        logger.info('Updating audit issue', {
          component: 'audit_api',
          action: 'update_issue',
          userId: session.user.email
        });

        // Initialize audit service and update issue
        const auditService = new AuditService();
        const updatedIssue = await auditService.updateIssue(id, processedUpdateData);

        if (!updatedIssue) {
          const error = new Error('Issue not found or update failed') as any;
          error.statusCode = 404;
          throw error;
        }

        return NextResponse.json({
          success: true,
          data: {
            id: updatedIssue.id,
            status: updatedIssue.status,
            message: 'Issue updated successfully'
          },
          timestamp: new Date().toISOString()
        });
      }
    ) as Promise<NextResponse<ApiResponse<IssueUpdateResponse>>>;
  }) as Promise<NextResponse<ApiResponse<IssueUpdateResponse>>>;
});

// DELETE - Delete audit issue (admin only, for false positives or cleanup)
export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<{ message: string }>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      const { id } = await params;
      
      // Check authentication - audit operations require admin privileges
      if (!session?.user?.email || !isAdminUser(session.user.email)) {
        const error = new Error('Unauthorized. Admin access required for audit operations.') as any;
        error.statusCode = 403;
        throw error;
      }

      if (!id) {
        const error = new Error('Issue ID is required') as any;
        error.statusCode = 400;
        throw error;
      }

      logger.info('Deleting audit issue', {
        component: 'audit_api',
        action: 'delete_issue',
        userId: session.user.email
      });

      // Initialize audit service and delete issue
      const auditService = new AuditService();
      const result = await auditService.deleteIssue(id);

      if (!result) {
        const error = new Error('Issue not found or deletion failed') as any;
        error.statusCode = 404;
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: {
          message: 'Issue deleted successfully'
        },
        timestamp: new Date().toISOString()
      });
    }
  ) as Promise<NextResponse<ApiResponse<{ message: string }>>>;
});
