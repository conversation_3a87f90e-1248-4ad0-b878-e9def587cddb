import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isUserAdmin } from '@/lib/auth-utils';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rate-limit';

interface AIPerformanceDashboardResponse {
  overview: {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    errorRate: number;
  };
  metrics: {
    requestsPerHour: Array<{ hour: string; requests: number }>;
    responseTimeDistribution: Array<{ range: string; count: number }>;
    errorsByType: Array<{ type: string; count: number }>;
  };
  services: {
    gemini: {
      status: 'healthy' | 'degraded' | 'down';
      responseTime: number;
      successRate: number;
    };
    cache: {
      hitRate: number;
      size: number;
      evictions: number;
    };
  };
  generatedAt: string;
}

// GET /api/admin/ai-performance-dashboard - Get AI performance metrics
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AIPerformanceDashboardResponse>>> => {
  const session = await getServerSession(authOptions);
      
      // Check authentication and admin privileges
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const isAdmin = await isUserAdmin(session.user.id);
      if (!isAdmin) {
        const error = new Error('Admin access required') as any;
        error.statusCode = 403;
        throw error;
      }

      const { searchParams } = new URL(request.url);
      const view = searchParams.get('view') || 'overview';
      const timeRange = searchParams.get('timeRange') || '24h';

      // Generate mock performance data for now
      // In production, this would pull from actual monitoring systems
      const performanceData: AIPerformanceDashboardResponse = {
        overview: {
          totalRequests: 1250,
          successRate: 98.4,
          averageResponseTime: 850,
          errorRate: 1.6
        },
        metrics: {
          requestsPerHour: Array.from({ length: 24 }, (_, i) => ({
            hour: `${i}:00`,
            requests: Math.floor(Math.random() * 100) + 20
          })),
          responseTimeDistribution: [
            { range: '0-500ms', count: 45 },
            { range: '500-1000ms', count: 35 },
            { range: '1000-2000ms', count: 15 },
            { range: '2000ms+', count: 5 }
          ],
          errorsByType: [
            { type: 'Rate Limit', count: 8 },
            { type: 'Timeout', count: 5 },
            { type: 'API Error', count: 3 },
            { type: 'Network', count: 4 }
          ]
        },
        services: {
          gemini: {
            status: 'healthy',
            responseTime: 750,
            successRate: 99.2
          },
          cache: {
            hitRate: 85.6,
            size: 1024,
            evictions: 12
          }
        },
    generatedAt: new Date().toISOString()
  };

  return NextResponse.json({
    success: true,
    data: performanceData
  });
});
