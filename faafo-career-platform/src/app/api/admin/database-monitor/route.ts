import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isUserAdmin } from '@/lib/auth-utils';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rate-limit';
import { prisma } from '@/lib/prisma';

interface DatabaseMonitorResponse {
  connectionStatus: {
    status: 'connected' | 'disconnected' | 'degraded';
    activeConnections: number;
    maxConnections: number;
    connectionPoolUtilization: number;
  };
  performance: {
    averageQueryTime: number;
    slowQueries: Array<{
      query: string;
      duration: number;
      timestamp: string;
    }>;
    queryCount: {
      total: number;
      successful: number;
      failed: number;
    };
  };
  storage: {
    databaseSize: string;
    tableStats: Array<{
      tableName: string;
      rowCount: number;
      size: string;
    }>;
    indexUsage: number;
  };
  health: {
    replicationLag: number;
    lockWaitTime: number;
    cacheHitRatio: number;
  };
  generatedAt: string;
}

// GET /api/admin/database-monitor - Get database monitoring metrics
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<DatabaseMonitorResponse>>> => {
  const session = await getServerSession(authOptions);
      
      // Check authentication and admin privileges
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const isAdmin = await isUserAdmin(session.user.id);
      if (!isAdmin) {
        const error = new Error('Admin access required') as any;
        error.statusCode = 403;
        throw error;
      }

      try {
        // Test database connection
        await prisma.$queryRaw`SELECT 1`;

        // Get basic table counts for monitoring
        const [userCount, assessmentCount, resourceCount] = await Promise.all([
          prisma.user.count(),
          prisma.assessment.count(),
          prisma.learningResource.count()
        ]);

        // Generate mock database monitoring data
        // In production, this would pull from actual database monitoring tools
        const databaseData: DatabaseMonitorResponse = {
          connectionStatus: {
            status: 'connected',
            activeConnections: 8,
            maxConnections: 20,
            connectionPoolUtilization: 40
          },
          performance: {
            averageQueryTime: 45,
            slowQueries: [
              {
                query: 'SELECT * FROM users WHERE...',
                duration: 1250,
                timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()
              },
              {
                query: 'SELECT * FROM assessments JOIN...',
                duration: 980,
                timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString()
              }
            ],
            queryCount: {
              total: 15420,
              successful: 15385,
              failed: 35
            }
          },
          storage: {
            databaseSize: '2.4 GB',
            tableStats: [
              {
                tableName: 'users',
                rowCount: userCount,
                size: '45 MB'
              },
              {
                tableName: 'assessments',
                rowCount: assessmentCount,
                size: '128 MB'
              },
              {
                tableName: 'learning_resources',
                rowCount: resourceCount,
                size: '89 MB'
              }
            ],
            indexUsage: 94.2
          },
          health: {
            replicationLag: 0,
            lockWaitTime: 12,
            cacheHitRatio: 96.8
          },
          generatedAt: new Date().toISOString()
        };

        return NextResponse.json({
          success: true,
          data: databaseData
        });

      } catch (error) {
        console.error('Database monitoring error:', error);
        
        // Return degraded status if database issues
        const degradedData: DatabaseMonitorResponse = {
          connectionStatus: {
            status: 'degraded',
            activeConnections: 0,
            maxConnections: 20,
            connectionPoolUtilization: 0
          },
          performance: {
            averageQueryTime: 0,
            slowQueries: [],
            queryCount: {
              total: 0,
              successful: 0,
              failed: 1
            }
          },
          storage: {
            databaseSize: 'Unknown',
            tableStats: [],
            indexUsage: 0
          },
          health: {
            replicationLag: -1,
            lockWaitTime: -1,
            cacheHitRatio: 0
          },
          generatedAt: new Date().toISOString()
        };

      return NextResponse.json({
        success: true,
        data: degradedData
      });
    }
});
