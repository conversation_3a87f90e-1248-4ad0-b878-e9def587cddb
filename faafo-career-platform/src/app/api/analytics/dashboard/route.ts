import { NextRequest, NextResponse } from 'next/server';

// GET /api/analytics/dashboard - Get analytics data (personal or platform-wide for admins)
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Analytics dashboard temporarily disabled for debugging",
    type: 'personal',
    generatedAt: new Date().toISOString()
  });
}

// POST /api/analytics/dashboard - Track analytics events
export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: "Analytics event tracking temporarily disabled for debugging"
  });
}
