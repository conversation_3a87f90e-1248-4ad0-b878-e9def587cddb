'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertTriangle,
  ArrowRight,
  Play,
  Pause,
  RefreshCw,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AuditRun {
  id: string;
  startedAt: string;
  completedAt: string | null;
  status: string;
  totalIssues: number;
  criticalCount: number;
  highCount: number;
  mediumCount: number;
  lowCount: number;
  triggeredBy: string | null;
  metadata?: any;
}

interface AuditRunsData {
  runs: AuditRun[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function AuditRunsPage() {
  const [data, setData] = useState<AuditRunsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRunningAudit, setIsRunningAudit] = useState(false);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  const fetchAuditRuns = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
      });
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }
      
      if (searchQuery) {
        params.append('triggeredBy', searchQuery);
      }

      const response = await fetch(`/api/audit/runs?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch audit runs');
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      console.error('Error fetching audit runs:', err);
      setError('Failed to load audit runs');
    } finally {
      setLoading(false);
    }
  };

  const startNewAudit = async () => {
    try {
      setIsRunningAudit(true);
      
      const response = await fetch('/api/audit/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enabledAnalyzers: ['typescript', 'eslint', 'security', 'performance'],
          categories: ['SECURITY', 'PERFORMANCE', 'MAINTAINABILITY', 'TESTING']
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start audit');
      }

      const result = await response.json();
      toast.success('Audit started successfully!');
      
      // Refresh the list
      setTimeout(() => {
        fetchAuditRuns();
      }, 2000);
      
    } catch (err) {
      console.error('Error starting audit:', err);
      toast.error('Failed to start audit');
    } finally {
      setIsRunningAudit(false);
    }
  };

  useEffect(() => {
    fetchAuditRuns();
  }, [currentPage, statusFilter, searchQuery]);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'running':
        return <Clock className="h-4 w-4 animate-spin" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'cancelled':
        return <Pause className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'default';
      case 'running':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (startedAt: string, completedAt: string | null) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    if (diffMinutes > 0) {
      return `${diffMinutes}m ${diffSeconds}s`;
    } else {
      return `${diffSeconds}s`;
    }
  };

  if (loading && !data) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Runs</h1>
          <p className="text-muted-foreground">
            View and manage all codebase audit executions.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchAuditRuns}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={startNewAudit}
            disabled={isRunningAudit}
          >
            {isRunningAudit ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {isRunningAudit ? 'Starting...' : 'Run Audit'}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by triggered by..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="RUNNING">Running</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Audit Runs List */}
      <div className="space-y-4">
        {data?.runs.map((run) => (
          <Link key={run.id} href={`/audit/runs/${run.id}`}>
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(run.status)}
                        <Badge variant={getStatusVariant(run.status) as any}>
                          {run.status}
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        {run.criticalCount > 0 && (
                          <Badge variant="destructive">
                            {run.criticalCount} Critical
                          </Badge>
                        )}
                        {run.highCount > 0 && (
                          <Badge variant="destructive">
                            {run.highCount} High
                          </Badge>
                        )}
                        {run.totalIssues > 0 && (
                          <Badge variant="outline">
                            {run.totalIssues} Total Issues
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Started</p>
                        <p className="font-medium">{formatDateTime(run.startedAt)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Duration</p>
                        <p className="font-medium">
                          {formatDuration(run.startedAt, run.completedAt)}
                          {run.status.toLowerCase() === 'running' && ' (ongoing)'}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Triggered By</p>
                        <p className="font-medium">{run.triggeredBy || 'System'}</p>
                      </div>
                    </div>
                  </div>
                  
                  <ArrowRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Pagination */}
      {data && data.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Showing {((data.pagination.page - 1) * data.pagination.limit) + 1} to{' '}
            {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} of{' '}
            {data.pagination.total} results
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === data.pagination.pages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {data?.runs.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Play className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              No audit runs found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {statusFilter || searchQuery 
                ? 'Try adjusting your filters to see more results.'
                : 'Start your first audit run to analyze your codebase.'
              }
            </p>
            {!statusFilter && !searchQuery && (
              <Button onClick={startNewAudit} disabled={isRunningAudit}>
                <Play className="h-4 w-4 mr-2" />
                Run First Audit
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
