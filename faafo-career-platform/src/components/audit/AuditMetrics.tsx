'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Bug,
  Shield,
  Zap,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuditMetricsProps {
  data: {
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    openCount: number;
    resolvedCount: number;
    lastRunAt: string | null;
    lastRunStatus: string | null;
  };
}

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'stable';
  variant?: 'default' | 'critical' | 'warning' | 'success';
  className?: string;
}

function MetricCard({ 
  title, 
  value, 
  icon, 
  description, 
  trend, 
  variant = 'default',
  className 
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'critical':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'warning':
        return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20';
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      default:
        return 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800';
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'critical':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-orange-600 dark:text-orange-400';
      case 'success':
        return 'text-green-600 dark:text-green-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  return (
    <Card className={cn(getVariantStyles(), className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn('p-2 rounded-lg', getIconColor())}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {title}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {value.toLocaleString()}
              </p>
              {description && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  {description}
                </p>
              )}
            </div>
          </div>
          {trend && (
            <div className={cn(
              'flex items-center gap-1 text-xs',
              trend === 'up' ? 'text-red-600' : 
              trend === 'down' ? 'text-green-600' : 
              'text-gray-500'
            )}>
              <TrendingUp className={cn(
                'h-3 w-3',
                trend === 'down' && 'rotate-180'
              )} />
              {trend === 'up' ? 'Increasing' : 
               trend === 'down' ? 'Decreasing' : 
               'Stable'}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function AuditMetrics({ data }: AuditMetricsProps) {
  const formatLastRun = (lastRunAt: string | null, status: string | null) => {
    if (!lastRunAt) return 'No runs yet';
    
    const date = new Date(lastRunAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    let timeAgo = '';
    if (diffDays > 0) {
      timeAgo = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      timeAgo = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      timeAgo = 'Less than an hour ago';
    }
    
    return `${timeAgo} (${status})`;
  };

  const getStatusVariant = (status: string | null) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'FAILED':
        return 'critical';
      case 'RUNNING':
        return 'warning';
      default:
        return 'default';
    }
  };

  const resolutionRate = data.totalIssues > 0 
    ? Math.round((data.resolvedCount / data.totalIssues) * 100)
    : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Critical Issues */}
      <MetricCard
        title="Critical Issues"
        value={data.criticalCount}
        icon={<AlertTriangle className="h-5 w-5" />}
        variant={data.criticalCount > 0 ? 'critical' : 'success'}
        description={data.criticalCount > 0 ? 'Requires immediate attention' : 'No critical issues'}
      />

      {/* High Priority Issues */}
      <MetricCard
        title="High Priority"
        value={data.highCount}
        icon={<Bug className="h-5 w-5" />}
        variant={data.highCount > 5 ? 'warning' : 'default'}
        description={`${data.mediumCount + data.lowCount} medium/low priority`}
      />

      {/* Open Issues */}
      <MetricCard
        title="Open Issues"
        value={data.openCount}
        icon={<Activity className="h-5 w-5" />}
        variant={data.openCount > 10 ? 'warning' : 'default'}
        description={`${data.resolvedCount} resolved issues`}
      />

      {/* Resolution Rate */}
      <MetricCard
        title="Resolution Rate"
        value={resolutionRate}
        icon={<CheckCircle className="h-5 w-5" />}
        variant={resolutionRate > 80 ? 'success' : resolutionRate > 60 ? 'warning' : 'critical'}
        description={`${resolutionRate}% of issues resolved`}
      />

      {/* Last Audit Run - Full Width */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Last Audit Run
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {formatLastRun(data.lastRunAt, data.lastRunStatus)}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Total issues found: {data.totalIssues}
              </p>
            </div>
            {data.lastRunStatus && (
              <Badge 
                variant={
                  data.lastRunStatus === 'COMPLETED' ? 'default' :
                  data.lastRunStatus === 'FAILED' ? 'destructive' :
                  data.lastRunStatus === 'RUNNING' ? 'secondary' :
                  'outline'
                }
              >
                {data.lastRunStatus}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
