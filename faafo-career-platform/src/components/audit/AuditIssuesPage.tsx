'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  Bug, 
  Shield, 
  Zap, 
  Code, 
  Database, 
  TestTube,
  FileText,
  ArrowRight,
  Clock,
  RefreshCw,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AuditIssue {
  id: string;
  auditRunId: string;
  severity: string;
  category: string;
  title: string;
  description: string;
  filePath: string;
  lineNumber: number | null;
  columnNumber: number | null;
  codeSnippet: string | null;
  recommendation: string | null;
  fixExample: string | null;
  status: string;
  assignedToId: string | null;
  resolvedAt: string | null;
  falsePositive: boolean;
  createdAt: string;
  updatedAt: string;
  assignedTo?: {
    id: string;
    name: string | null;
    email: string;
  };
  auditRun: {
    id: string;
    startedAt: string;
    status: string;
  };
  _count: {
    comments: number;
  };
}

interface AuditIssuesData {
  issues: AuditIssue[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  summary: {
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    openCount: number;
    resolvedCount: number;
  };
}

export function AuditIssuesPage() {
  const [data, setData] = useState<AuditIssuesData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  const fetchIssues = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
      });
      
      if (severityFilter !== 'all') {
        params.append('severity', severityFilter);
      }
      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter);
      }
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }
      
      const response = await fetch(`/api/audit/issues?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch audit issues');
      }
      
      const result = await response.json();
      setData(result.data);
    } catch (err) {
      console.error('Error fetching audit issues:', err);
      setError('Failed to load audit issues');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIssues();
  }, [currentPage, severityFilter, categoryFilter, statusFilter]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchIssues();
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'high':
        return <Bug className="h-4 w-4" />;
      case 'medium':
        return <Zap className="h-4 w-4" />;
      case 'low':
        return <Code className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'security':
        return <Shield className="h-4 w-4" />;
      case 'performance':
        return <Zap className="h-4 w-4" />;
      case 'testing':
        return <TestTube className="h-4 w-4" />;
      case 'architecture':
        return <Database className="h-4 w-4" />;
      default:
        return <Code className="h-4 w-4" />;
    }
  };

  const getSeverityVariant = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'resolved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'false_positive':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-orange-600" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  if (loading && !data) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Issues</h1>
          <p className="text-muted-foreground">
            View and manage all codebase audit issues.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/audit">
              <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
              Back to Dashboard
            </Link>
          </Button>
          <Button
            variant="outline"
            onClick={fetchIssues}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      {data?.summary && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {data.summary.totalIssues}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Total</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-red-600">
                {data.summary.criticalCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Critical</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-orange-600">
                {data.summary.highCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">High</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-yellow-600">
                {data.summary.mediumCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Medium</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-blue-600">
                {data.summary.lowCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Low</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-orange-600">
                {data.summary.openCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Open</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-green-600">
                {data.summary.resolvedCount}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Resolved</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Filters:</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Select value={severityFilter} onValueChange={setSeverityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value="CRITICAL">Critical</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="SECURITY">Security</SelectItem>
                  <SelectItem value="PERFORMANCE">Performance</SelectItem>
                  <SelectItem value="MAINTAINABILITY">Maintainability</SelectItem>
                  <SelectItem value="TESTING">Testing</SelectItem>
                  <SelectItem value="DOCUMENTATION">Documentation</SelectItem>
                  <SelectItem value="ARCHITECTURE">Architecture</SelectItem>
                  <SelectItem value="ACCESSIBILITY">Accessibility</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="OPEN">Open</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="RESOLVED">Resolved</SelectItem>
                  <SelectItem value="DEFERRED">Deferred</SelectItem>
                  <SelectItem value="FALSE_POSITIVE">False Positive</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex gap-2">
                <Input
                  placeholder="Search issues..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} size="sm">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Issues List */}
      <div className="space-y-4">
        {data?.issues.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Bug className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No issues found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {Object.values({ severityFilter, categoryFilter, statusFilter }).some(f => f !== 'all') || searchQuery
                  ? 'No issues match your current filters.'
                  : 'No audit issues have been found yet.'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          data?.issues.map((issue) => (
            <Link key={issue.id} href={`/audit/issues/${issue.id}`}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <div className={cn(
                          'flex-shrink-0',
                          issue.severity.toLowerCase() === 'critical' ? 'text-red-600' :
                          issue.severity.toLowerCase() === 'high' ? 'text-orange-600' :
                          issue.severity.toLowerCase() === 'medium' ? 'text-yellow-600' :
                          'text-blue-600'
                        )}>
                          {getSeverityIcon(issue.severity)}
                        </div>
                        <Badge variant={getSeverityVariant(issue.severity) as any}>
                          {issue.severity}
                        </Badge>
                        <div className="flex items-center gap-1 text-gray-500">
                          {getCategoryIcon(issue.category)}
                          <span className="text-xs">{issue.category}</span>
                        </div>
                        {getStatusIcon(issue.status)}
                        <Badge variant="outline" className="text-xs">
                          {issue.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        {issue.title}
                      </h3>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                        {issue.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="truncate">
                          {issue.filePath}
                          {issue.lineNumber && `:${issue.lineNumber}`}
                        </span>
                        <div className="flex items-center gap-4 flex-shrink-0">
                          {issue._count.comments > 0 && (
                            <span>{issue._count.comments} comment{issue._count.comments !== 1 ? 's' : ''}</span>
                          )}
                          <span>{formatTimeAgo(issue.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <ArrowRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))
        )}
      </div>

      {/* Pagination */}
      {data && data.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Showing {((data.pagination.page - 1) * data.pagination.limit) + 1} to{' '}
            {Math.min(data.pagination.page * data.pagination.limit, data.pagination.total)} of{' '}
            {data.pagination.total} results
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === data.pagination.pages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
