'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertTriangle,
  ArrowRight,
  Play,
  Pause
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuditRun {
  id: string;
  startedAt: string;
  completedAt: string | null;
  status: string;
  totalIssues: number;
}

interface AuditRunsListProps {
  runs: AuditRun[];
  showAll?: boolean;
}

function getStatusIcon(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
      return <CheckCircle className="h-4 w-4" />;
    case 'running':
      return <Clock className="h-4 w-4 animate-spin" />;
    case 'failed':
      return <XCircle className="h-4 w-4" />;
    case 'cancelled':
      return <Pause className="h-4 w-4" />;
    default:
      return <Activity className="h-4 w-4" />;
  }
}

function getStatusVariant(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'default';
    case 'running':
      return 'secondary';
    case 'failed':
      return 'destructive';
    case 'cancelled':
      return 'outline';
    default:
      return 'outline';
  }
}

function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'text-green-600 dark:text-green-400';
    case 'running':
      return 'text-blue-600 dark:text-blue-400';
    case 'failed':
      return 'text-red-600 dark:text-red-400';
    case 'cancelled':
      return 'text-gray-600 dark:text-gray-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
}

function formatDuration(startedAt: string, completedAt: string | null) {
  const start = new Date(startedAt);
  const end = completedAt ? new Date(completedAt) : new Date();
  const diffMs = end.getTime() - start.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);

  if (diffMinutes > 0) {
    return `${diffMinutes}m ${diffSeconds}s`;
  } else {
    return `${diffSeconds}s`;
  }
}

function formatDateTime(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

function AuditRunCard({ run }: { run: AuditRun }) {
  const isRunning = run.status.toLowerCase() === 'running';
  
  return (
    <Link href={`/audit/runs/${run.id}`}>
      <Card className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className={cn('flex-shrink-0', getStatusColor(run.status))}>
                  {getStatusIcon(run.status)}
                </div>
                <Badge variant={getStatusVariant(run.status) as any}>
                  {run.status}
                </Badge>
                {run.totalIssues > 0 && (
                  <Badge variant="outline">
                    {run.totalIssues} issue{run.totalIssues !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>
              
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Started: {formatDateTime(run.startedAt)}
                </p>
                
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Duration: {formatDuration(run.startedAt, run.completedAt)}
                  {isRunning && ' (ongoing)'}
                </p>
                
                {run.completedAt && (
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Completed: {formatDateTime(run.completedAt)}
                  </p>
                )}
              </div>
            </div>
            
            <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

export function AuditRunsList({ runs, showAll = false }: AuditRunsListProps) {
  const displayRuns = showAll ? runs : runs.slice(0, 5);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Audit Runs
            </CardTitle>
            <CardDescription>
              History of codebase audit executions
            </CardDescription>
          </div>
          {!showAll && runs.length > 5 && (
            <Button variant="outline" size="sm" asChild>
              <Link href="/audit/runs">
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {displayRuns.length === 0 ? (
          <div className="text-center py-8">
            <Play className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400 mb-2">
              No audit runs yet
            </p>
            <p className="text-sm text-gray-500">
              Click "Run Audit" to start your first codebase analysis
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayRuns.map((run) => (
              <AuditRunCard key={run.id} run={run} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
