'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Bug, 
  Shield, 
  Zap, 
  Code, 
  Database, 
  TestTube,
  FileText,
  ArrowRight,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Issue {
  id: string;
  title: string;
  severity: string;
  category: string;
  filePath: string;
  createdAt: string;
}

interface RecentIssuesProps {
  issues: Issue[];
  showAll?: boolean;
}

function getSeverityIcon(severity: string) {
  switch (severity.toLowerCase()) {
    case 'critical':
      return <AlertTriangle className="h-4 w-4" />;
    case 'high':
      return <Bug className="h-4 w-4" />;
    case 'medium':
      return <Zap className="h-4 w-4" />;
    case 'low':
      return <Code className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
}

function getCategoryIcon(category: string) {
  switch (category.toLowerCase()) {
    case 'security':
      return <Shield className="h-4 w-4" />;
    case 'performance':
      return <Zap className="h-4 w-4" />;
    case 'testing':
      return <TestTube className="h-4 w-4" />;
    case 'architecture':
      return <Database className="h-4 w-4" />;
    default:
      return <Code className="h-4 w-4" />;
  }
}

function getSeverityVariant(severity: string) {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'destructive';
    case 'high':
      return 'destructive';
    case 'medium':
      return 'secondary';
    case 'low':
      return 'outline';
    default:
      return 'outline';
  }
}

function getSeverityColor(severity: string) {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'text-red-600 dark:text-red-400';
    case 'high':
      return 'text-orange-600 dark:text-orange-400';
    case 'medium':
      return 'text-yellow-600 dark:text-yellow-400';
    case 'low':
      return 'text-blue-600 dark:text-blue-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
}

function IssueCard({ issue }: { issue: Issue }) {
  return (
    <Link href={`/audit/issues/${issue.id}`}>
      <Card className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <div className={cn('flex-shrink-0', getSeverityColor(issue.severity))}>
                  {getSeverityIcon(issue.severity)}
                </div>
                <Badge variant={getSeverityVariant(issue.severity) as any}>
                  {issue.severity}
                </Badge>
                <div className="flex items-center gap-1 text-gray-500">
                  {getCategoryIcon(issue.category)}
                  <span className="text-xs">{issue.category}</span>
                </div>
              </div>
              
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1 truncate">
                {issue.title}
              </h3>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {issue.filePath}
              </p>
              
              <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                {formatTimeAgo(issue.createdAt)}
              </div>
            </div>
            
            <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

export function RecentIssues({ issues, showAll = false }: RecentIssuesProps) {
  const displayIssues = showAll ? issues : issues.slice(0, 5);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-5 w-5" />
              Recent Issues
            </CardTitle>
            <CardDescription>
              Latest issues found in codebase audits
            </CardDescription>
          </div>
          {!showAll && issues.length > 5 && (
            <Button variant="outline" size="sm" asChild>
              <Link href="/audit/issues">
                View All
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {displayIssues.length === 0 ? (
          <div className="text-center py-8">
            <Bug className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              No issues found. Great job!
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayIssues.map((issue) => (
              <IssueCard key={issue.id} issue={issue} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
