"use client";

import { SessionProvider } from 'next-auth/react';
import React, { useEffect, useState } from 'react';

interface SessionWrapperProps {
  children: React.ReactNode;
}

export default function SessionWrapper({
  children,
}: SessionWrapperProps) {
  const [isOnline, setIsOnline] = useState(true); // Default to true for SSR
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Set mounted to true to prevent hydration mismatch
    setMounted(true);

    // Only access browser APIs after mounting
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      // Set initial online state from navigator
      setIsOnline(navigator.onLine);

      // Monitor online/offline status
      const handleOnline = () => setIsOnline(true);
      const handleOffline = () => setIsOnline(false);

      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  // Use conservative defaults during SSR and initial hydration
  const sessionProviderProps = mounted ? {
    refetchInterval: isOnline ? 5 * 60 : 0, // Only refetch when online
    refetchOnWindowFocus: isOnline, // Only refetch on focus when online
    refetchWhenOffline: false as const, // Don't refetch when offline
    basePath: "/api/auth", // Ensure correct auth path
    baseUrl: typeof window !== 'undefined' ? window.location.origin : undefined
  } : {
    // Conservative defaults for SSR
    refetchInterval: 5 * 60, // Default refetch interval
    refetchOnWindowFocus: true, // Default behavior
    refetchWhenOffline: false as const, // Don't refetch when offline
    basePath: "/api/auth", // Ensure correct auth path
    baseUrl: undefined // Let NextAuth handle this during SSR
  };

  return (
    <SessionProvider {...sessionProviderProps}>
      {children}
    </SessionProvider>
  );
}
