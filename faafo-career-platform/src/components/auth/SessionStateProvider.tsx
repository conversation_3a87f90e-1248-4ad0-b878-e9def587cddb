'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { getAuthStateManager, AuthState } from '@/lib/auth-state-manager';

interface SessionStateContextType {
  authState: AuthState;
  refreshSession: () => Promise<void>;
  isSessionValid: () => Promise<boolean>;
  updateActivity: () => void;
  forceLogout: (reason?: string) => Promise<void>;
}

const SessionStateContext = createContext<SessionStateContextType | null>(null);

interface SessionStateProviderProps {
  children: React.ReactNode;
}

/**
 * Session State Provider
 * 
 * Provides real-time session state management throughout the application.
 * This provider ensures that all components have access to the latest
 * authentication state and can respond to session changes immediately.
 */
// Track last session check time outside component to persist across renders
let lastSessionCheckTime = 0;

export function SessionStateProvider({ children }: SessionStateProviderProps) {
  const { data: session, status } = useSession();
  const authStateManager = getAuthStateManager();
  const [authState, setAuthState] = useState<AuthState>(authStateManager.getState());
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only running client-side effects after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Refresh session state
  const refreshSession = useCallback(async () => {
    try {
      await authStateManager.refreshState();
    } catch (error) {
      console.error('SessionStateProvider: Failed to refresh session', error);
    }
  }, [authStateManager]);

  // Check if session is valid (with throttling)
  const isSessionValid = useCallback(async (): Promise<boolean> => {
    try {
      // Throttle session validation requests (max once per 30 seconds)
      const now = Date.now();
      if (now - lastSessionCheckTime < 30000) {
        return true; // Assume valid if checked recently
      }
      lastSessionCheckTime = now;

      return await authStateManager.validateSession();
    } catch (error) {
      console.error('SessionStateProvider: Session validation failed', error);
      return false;
    }
  }, [authStateManager]);

  // Update user activity
  const updateActivity = useCallback(() => {
    authStateManager.updateLastActivity();
  }, [authStateManager]);

  // Force logout
  const forceLogout = useCallback(async (reason?: string) => {
    try {
      await authStateManager.forceLogout(reason);
    } catch (error) {
      console.error('SessionStateProvider: Force logout failed', error);
    }
  }, [authStateManager]);

  // Set up real-time state updates
  useEffect(() => {
    const unsubscribe = authStateManager.addStateChangeListener((newState) => {
      setAuthState(newState);
    });

    // Initial state sync
    setAuthState(authStateManager.getState());

    return () => {
      unsubscribe();
    };
  }, [authStateManager]);

  // Sync with NextAuth session changes
  useEffect(() => {
    if (status !== 'loading') {
      refreshSession();
    }
  }, [session, status, refreshSession]);

  // Set up activity tracking (disabled in development to reduce noise)
  useEffect(() => {
    if (!authState.isAuthenticated || process.env.NODE_ENV === 'development') {
      return;
    }

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    // Add activity listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    return () => {
      // Remove activity listeners
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, [authState.isAuthenticated, updateActivity]);

  // Set up session timeout monitoring
  useEffect(() => {
    if (!authState.isAuthenticated) {
      return;
    }

    const checkSessionTimeout = () => {
      if (authStateManager.checkSessionExpiry()) {
        forceLogout('Session expired due to inactivity');
      }
    };

    // Check session timeout every 5 minutes (reduced from 1 minute)
    const interval = setInterval(checkSessionTimeout, 5 * 60 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [authState.isAuthenticated, authStateManager, forceLogout]);

  // Set up periodic session validation (only after mount)
  useEffect(() => {
    if (!mounted || !authState.isAuthenticated) {
      return;
    }

    // In development, reduce session validation frequency to prevent spam
    const isDevelopment = process.env.NODE_ENV === 'development';
    const validationInterval = isDevelopment ? 30 * 60 * 1000 : 20 * 60 * 1000; // 30 min dev, 20 min prod

    const validatePeriodically = async () => {
      const isValid = await isSessionValid();
      if (!isValid) {
        console.log('SessionStateProvider: Session validation failed, forcing logout');
        await forceLogout('Session validation failed');
      }
    };

    // Validate session with environment-specific frequency
    const interval = setInterval(validatePeriodically, validationInterval);

    return () => {
      clearInterval(interval);
    };
  }, [mounted, authState.isAuthenticated, isSessionValid, forceLogout]);

  const contextValue: SessionStateContextType = {
    authState,
    refreshSession,
    isSessionValid,
    updateActivity,
    forceLogout
  };

  return (
    <SessionStateContext.Provider value={contextValue}>
      {children}
    </SessionStateContext.Provider>
  );
}

/**
 * Hook to use session state context
 */
export function useSessionState(): SessionStateContextType {
  const context = useContext(SessionStateContext);
  
  if (!context) {
    throw new Error('useSessionState must be used within a SessionStateProvider');
  }
  
  return context;
}

/**
 * Hook for components that need to monitor session changes
 */
export function useSessionMonitoring() {
  const { authState, refreshSession, isSessionValid, updateActivity } = useSessionState();
  
  // Monitor for session changes and provide callbacks
  const onSessionChange = useCallback((callback: (state: AuthState) => void) => {
    callback(authState);
  }, [authState]);

  const onSessionExpired = useCallback((callback: () => void) => {
    if (authState.error === 'Session expired') {
      callback();
    }
  }, [authState.error]);

  const onSessionInvalid = useCallback((callback: () => void) => {
    if (authState.error === 'Session validation failed') {
      callback();
    }
  }, [authState.error]);

  return {
    authState,
    refreshSession,
    isSessionValid,
    updateActivity,
    onSessionChange,
    onSessionExpired,
    onSessionInvalid
  };
}

/**
 * Hook for session timeout warnings
 */
export function useSessionTimeout(warningMinutes: number = 5) {
  const { authState, forceLogout } = useSessionState();
  const [showWarning, setShowWarning] = useState(false);
  const [minutesLeft, setMinutesLeft] = useState(0);

  useEffect(() => {
    if (!authState.isAuthenticated || !authState.lastActivity) {
      setShowWarning(false);
      return;
    }

    const checkTimeout = () => {
      const now = Date.now();
      const timeSinceActivity = now - authState.lastActivity!;
      const sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours - matches auth configuration
      const warningThreshold = sessionTimeout - (warningMinutes * 60 * 1000);

      if (timeSinceActivity >= sessionTimeout) {
        // Session expired
        forceLogout('Session expired due to inactivity');
        setShowWarning(false);
      } else if (timeSinceActivity >= warningThreshold) {
        // Show warning
        const timeLeft = sessionTimeout - timeSinceActivity;
        const minutesLeft = Math.ceil(timeLeft / (60 * 1000));
        setMinutesLeft(minutesLeft);
        setShowWarning(true);
      } else {
        // No warning needed
        setShowWarning(false);
      }
    };

    // Check immediately and then every 2 minutes (reduced from 30 seconds)
    checkTimeout();
    const interval = setInterval(checkTimeout, 2 * 60 * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [authState.isAuthenticated, authState.lastActivity, warningMinutes, forceLogout]);

  const dismissWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  const { updateActivity } = useSessionState();

  const extendSession = useCallback(() => {
    updateActivity();
    setShowWarning(false);
  }, [updateActivity]);

  return {
    showWarning,
    minutesLeft,
    dismissWarning,
    extendSession
  };
}
