/**
 * Skill Gap Radar Chart Tests
 * 
 * Tests Skill Gap Radar Chart component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SkillGapRadarChart from '@/components/skills/visualizations/SkillGapRadarChart';

// Mock recharts components
jest.mock('recharts', () => ({
  RadarChart: ({ children }: any) => <div data-testid="radar-chart">{children}</div>,
  PolarGrid: () => <div data-testid="polar-grid" />,
  PolarAngleAxis: ({ dataKey }: any) => <div data-testid="polar-angle-axis" data-key={dataKey} />,
  PolarRadiusAxis: () => <div data-testid="polar-radius-axis" />,
  Radar: ({ name, dataKey, stroke }: any) => (
    <div data-testid="radar" data-name={name} data-key={dataKey} data-stroke={stroke} />
  ),
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  Legend: () => <div data-testid="legend" />,
  Tooltip: () => <div data-testid="tooltip" />,
}));

const mockSkillData = [
  {
    skill: 'JavaScript',
    current: 7,
    target: 9,
    market: 8,
    category: 'Programming',
  },
  {
    skill: 'React',
    current: 6,
    target: 8,
    market: 7,
    category: 'Frontend',
  },
  {
    skill: 'Node.js',
    current: 5,
    target: 7,
    market: 6,
    category: 'Backend',
  },
];

describe('SkillGapRadarChart', () => {
  describe('Component Rendering', () => {
    it('should render with default props', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      expect(screen.getByText('Skill Gap Analysis')).toBeInTheDocument();
      expect(screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();
      expect(screen.getByTestId('radar-chart')).toBeInTheDocument();
    });

    it('should render with custom title and description', () => {
      const customTitle = 'Custom Skill Analysis';
      const customDescription = 'Custom description for testing';
      
      render(
        <SkillGapRadarChart
          data={mockSkillData}
          title={customTitle}
          description={customDescription}
        />
      );
      
      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.getByText(customDescription)).toBeInTheDocument();
    });

    it('should render without description when not provided', () => {
      render(
        <SkillGapRadarChart
          data={mockSkillData}
          title="Test Title"
          description=""
        />
      );
      
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.queryByText('Compare your current skills')).not.toBeInTheDocument();
    });
  });

  describe('Chart Components', () => {
    it('should render all required chart components', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
      expect(screen.getByTestId('radar-chart')).toBeInTheDocument();
      expect(screen.getByTestId('polar-grid')).toBeInTheDocument();
      expect(screen.getByTestId('polar-angle-axis')).toBeInTheDocument();
      expect(screen.getByTestId('polar-radius-axis')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    });

    it('should render three radar lines for current, target, and market', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      const radarElements = screen.getAllByTestId('radar');
      expect(radarElements).toHaveLength(3);
      
      expect(radarElements[0]).toHaveAttribute('data-name', 'Current Level');
      expect(radarElements[0]).toHaveAttribute('data-key', 'current');
      expect(radarElements[0]).toHaveAttribute('data-stroke', '#3b82f6');
      
      expect(radarElements[1]).toHaveAttribute('data-name', 'Target Level');
      expect(radarElements[1]).toHaveAttribute('data-key', 'target');
      expect(radarElements[1]).toHaveAttribute('data-stroke', '#10b981');
      
      expect(radarElements[2]).toHaveAttribute('data-name', 'Market Average');
      expect(radarElements[2]).toHaveAttribute('data-key', 'market');
      expect(radarElements[2]).toHaveAttribute('data-stroke', '#f59e0b');
    });

    it('should render legend when showLegend is true', () => {
      render(<SkillGapRadarChart data={mockSkillData} showLegend={true} />);
      expect(screen.getByTestId('legend')).toBeInTheDocument();
    });

    it('should not render legend when showLegend is false', () => {
      render(<SkillGapRadarChart data={mockSkillData} showLegend={false} />);
      expect(screen.queryByTestId('legend')).not.toBeInTheDocument();
    });
  });

  describe('Summary Statistics', () => {
    it('should calculate and display average current level correctly', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      // Average current: (7 + 6 + 5) / 3 = 6.0
      expect(screen.getByText('6.0/10')).toBeInTheDocument();
      expect(screen.getByText('Average Current')).toBeInTheDocument();
    });

    it('should calculate and display average target level correctly', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      // Average target: (9 + 8 + 7) / 3 = 8.0
      expect(screen.getByText('8.0/10')).toBeInTheDocument();
      expect(screen.getByText('Average Target')).toBeInTheDocument();
    });

    it('should calculate and display gap to close correctly', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      // Gap to close: ((9-7) + (8-6) + (7-5)) / 3 = (2 + 2 + 2) / 3 = 2.0
      expect(screen.getByText('2.0')).toBeInTheDocument();
      expect(screen.getByText('Gap to Close')).toBeInTheDocument();
    });

    it('should handle empty data gracefully', () => {
      render(<SkillGapRadarChart data={[]} />);

      expect(screen.getAllByText('0.0/10')).toHaveLength(2); // Current and Target averages
      expect(screen.getByText('0.0')).toBeInTheDocument(); // Gap to close
    });

    it('should handle single skill data correctly', () => {
      const singleSkill = [mockSkillData[0]];
      render(<SkillGapRadarChart data={singleSkill} />);
      
      expect(screen.getByText('7.0/10')).toBeInTheDocument(); // Current
      expect(screen.getByText('9.0/10')).toBeInTheDocument(); // Target
      expect(screen.getByText('2.0')).toBeInTheDocument(); // Gap
    });
  });

  describe('Data Handling', () => {
    it('should handle skills with zero values', () => {
      const dataWithZeros = [
        {
          skill: 'New Skill',
          current: 0,
          target: 5,
          market: 3,
          category: 'Learning',
        },
      ];
      
      render(<SkillGapRadarChart data={dataWithZeros} />);
      
      expect(screen.getByText('0.0/10')).toBeInTheDocument();
      expect(screen.getByText('5.0/10')).toBeInTheDocument();
      expect(screen.getByText('5.0')).toBeInTheDocument(); // Gap: 5-0 = 5
    });

    it('should handle skills where current exceeds target', () => {
      const dataWithExcess = [
        {
          skill: 'Expert Skill',
          current: 9,
          target: 7,
          market: 8,
          category: 'Advanced',
        },
      ];
      
      render(<SkillGapRadarChart data={dataWithExcess} />);
      
      expect(screen.getByText('9.0/10')).toBeInTheDocument();
      expect(screen.getByText('7.0/10')).toBeInTheDocument();
      expect(screen.getByText('0.0')).toBeInTheDocument(); // Gap: max(0, 7-9) = 0
    });

    it('should set correct dataKey for polar angle axis', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      const polarAngleAxis = screen.getByTestId('polar-angle-axis');
      expect(polarAngleAxis).toHaveAttribute('data-key', 'skill');
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);

      const title = screen.getByText('Skill Gap Analysis');
      expect(title.tagName).toBe('DIV'); // CardTitle renders as div with proper styling
    });

    it('should have descriptive text for screen readers', () => {
      render(<SkillGapRadarChart data={mockSkillData} />);
      
      expect(screen.getByText('Compare your current skills with target and market requirements')).toBeInTheDocument();
    });
  });

  describe('Customization', () => {
    it('should accept custom height', () => {
      const customHeight = 500;
      render(<SkillGapRadarChart data={mockSkillData} height={customHeight} />);
      
      const container = screen.getByTestId('responsive-container').parentElement;
      expect(container).toHaveStyle(`height: ${customHeight}px`);
    });

    it('should accept custom maxValue', () => {
      // This would be tested by checking if the PolarRadiusAxis receives the correct domain
      // Since we're mocking recharts, we can't test the actual domain prop
      // In a real implementation, this would be verified through integration tests
      render(<SkillGapRadarChart data={mockSkillData} maxValue={5} />);
      
      expect(screen.getByTestId('polar-radius-axis')).toBeInTheDocument();
    });
  });
});
