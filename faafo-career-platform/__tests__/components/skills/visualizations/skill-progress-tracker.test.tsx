/**
 * Skill Progress Tracker Tests
 * 
 * Tests Skill Progress Tracker component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SkillProgressTracker from '@/components/skills/visualizations/SkillProgressTracker';

// Mock the Progress component
jest.mock('@/components/ui/progress', () => ({
  Progress: ({ value, className }: any) => (
    <div data-testid="progress-bar" data-value={value} className={className} />
  ),
}));

const mockSkillProgressData = [
  {
    id: 'skill-1',
    name: 'JavaScript',
    category: 'Programming',
    currentLevel: 7,
    targetLevel: 9,
    progress: 75,
    status: 'in_progress' as const,
    estimatedTimeToComplete: 4,
    priority: 'high' as const,
    lastUpdated: '2025-06-21T10:00:00Z',
    milestones: [
      {
        id: 'milestone-1',
        title: 'Complete ES6 modules',
        completed: true,
        dueDate: '2025-06-15T00:00:00Z',
      },
      {
        id: 'milestone-2',
        title: 'Build React project',
        completed: false,
        dueDate: '2025-07-01T00:00:00Z',
      },
    ],
  },
  {
    id: 'skill-2',
    name: 'React',
    category: 'Frontend',
    currentLevel: 6,
    targetLevel: 8,
    progress: 50,
    status: 'at_risk' as const,
    estimatedTimeToComplete: 6,
    priority: 'critical' as const,
    lastUpdated: '2025-06-20T15:30:00Z',
    milestones: [
      {
        id: 'milestone-3',
        title: 'Learn hooks',
        completed: true,
      },
      {
        id: 'milestone-4',
        title: 'State management',
        completed: false,
      },
    ],
  },
  {
    id: 'skill-3',
    name: 'Node.js',
    category: 'Backend',
    currentLevel: 8,
    targetLevel: 8,
    progress: 100,
    status: 'completed' as const,
    estimatedTimeToComplete: 0,
    priority: 'medium' as const,
    lastUpdated: '2025-06-19T12:00:00Z',
    milestones: [
      {
        id: 'milestone-5',
        title: 'Express.js mastery',
        completed: true,
      },
    ],
  },
];

describe('SkillProgressTracker', () => {
  describe('Component Rendering', () => {
    it('should render with default props', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      expect(screen.getByText('Skill Development Progress')).toBeInTheDocument();
      expect(screen.getByText('Track your progress towards your skill development goals')).toBeInTheDocument();
    });

    it('should render with custom title and description', () => {
      const customTitle = 'Custom Progress Tracker';
      const customDescription = 'Custom description for testing';
      
      render(
        <SkillProgressTracker
          skills={mockSkillProgressData}
          title={customTitle}
          description={customDescription}
        />
      );
      
      expect(screen.getByText(customTitle)).toBeInTheDocument();
      expect(screen.getByText(customDescription)).toBeInTheDocument();
    });

    it('should render without description when not provided', () => {
      render(
        <SkillProgressTracker
          skills={mockSkillProgressData}
          title="Test Title"
          description=""
        />
      );
      
      expect(screen.getByText('Test Title')).toBeInTheDocument();
      expect(screen.queryByText('Track your progress')).not.toBeInTheDocument();
    });
  });

  describe('Overall Progress Summary', () => {
    it('should calculate and display overall progress correctly', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      // Overall progress: (75 + 50 + 100) / 3 = 75.0%
      expect(screen.getByText('75.0%')).toBeInTheDocument();
      expect(screen.getByText('Overall Progress')).toBeInTheDocument();
    });

    it('should count completed skills correctly', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      // Only Node.js is completed
      expect(screen.getByText('1/3')).toBeInTheDocument();
      expect(screen.getByText('Completed Skills')).toBeInTheDocument();
    });

    it('should count at-risk skills correctly', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      // Only React is at risk
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('At Risk')).toBeInTheDocument();
    });

    it('should handle empty skills array', () => {
      render(<SkillProgressTracker skills={[]} />);
      
      expect(screen.getByText('0.0%')).toBeInTheDocument(); // Overall progress
      expect(screen.getByText('0/0')).toBeInTheDocument(); // Completed skills
      expect(screen.getByText('0')).toBeInTheDocument(); // At risk skills
    });
  });

  describe('Skill Items Display', () => {
    it('should display all skills with correct information', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      // Check skill names
      expect(screen.getByText('JavaScript')).toBeInTheDocument();
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('Node.js')).toBeInTheDocument();
      
      // Check level progression
      expect(screen.getByText('Level 7 → 9')).toBeInTheDocument();
      expect(screen.getByText('Level 6 → 8')).toBeInTheDocument();
      expect(screen.getByText('Level 8 → 8')).toBeInTheDocument();
    });

    it('should display progress bars for each skill', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      const progressBars = screen.getAllByTestId('progress-bar');
      expect(progressBars).toHaveLength(3);
      
      expect(progressBars[0]).toHaveAttribute('data-value', '75');
      expect(progressBars[1]).toHaveAttribute('data-value', '50');
      expect(progressBars[2]).toHaveAttribute('data-value', '100');
    });

    it('should display correct status badges', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      expect(screen.getByText('in progress')).toBeInTheDocument();
      expect(screen.getByText('at risk')).toBeInTheDocument();
      expect(screen.getByText('completed')).toBeInTheDocument();
    });

    it('should display correct priority badges', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      expect(screen.getByText('high')).toBeInTheDocument();
      expect(screen.getByText('critical')).toBeInTheDocument();
      expect(screen.getByText('medium')).toBeInTheDocument();
    });

    it('should display estimated time to complete', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      expect(screen.getByText('Est. 4 weeks')).toBeInTheDocument();
      expect(screen.getByText('Est. 6 weeks')).toBeInTheDocument();
      expect(screen.getByText('Est. 0 weeks')).toBeInTheDocument();
    });
  });

  describe('Milestones Display', () => {
    it('should display milestones when showMilestones is true', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);
      
      expect(screen.getByText('Complete ES6 modules')).toBeInTheDocument();
      expect(screen.getByText('Build React project')).toBeInTheDocument();
      expect(screen.getByText('Learn hooks')).toBeInTheDocument();
      expect(screen.getByText('State management')).toBeInTheDocument();
      expect(screen.getByText('Express.js mastery')).toBeInTheDocument();
    });

    it('should not display milestones when showMilestones is false', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={false} />);
      
      expect(screen.queryByText('Complete ES6 modules')).not.toBeInTheDocument();
      expect(screen.queryByText('Milestones')).not.toBeInTheDocument();
    });

    it('should display milestone due dates when available', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);
      
      // Check for formatted dates
      expect(screen.getByText('6/15/2025')).toBeInTheDocument();
      expect(screen.getByText('7/1/2025')).toBeInTheDocument();
    });

    it('should handle milestones without due dates', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} showMilestones={true} />);
      
      // Milestones without due dates should still be displayed
      expect(screen.getByText('Learn hooks')).toBeInTheDocument();
      expect(screen.getByText('State management')).toBeInTheDocument();
    });
  });

  describe('Category Grouping', () => {
    it('should group skills by category when groupByCategory is true', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} groupByCategory={true} />);
      
      expect(screen.getByText('Programming')).toBeInTheDocument();
      expect(screen.getByText('Frontend')).toBeInTheDocument();
      expect(screen.getByText('Backend')).toBeInTheDocument();
    });

    it('should not show category headers when groupByCategory is false', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} groupByCategory={false} />);
      
      expect(screen.queryByText('Programming')).not.toBeInTheDocument();
      expect(screen.queryByText('Frontend')).not.toBeInTheDocument();
      expect(screen.queryByText('Backend')).not.toBeInTheDocument();
    });
  });

  describe('Status Icons', () => {
    it('should display correct status icons', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      // We can't easily test the actual icons, but we can verify the component renders
      // In a real test, you might check for specific icon classes or data attributes
      expect(screen.getByText('JavaScript')).toBeInTheDocument();
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('Node.js')).toBeInTheDocument();
    });
  });

  describe('Last Updated Display', () => {
    it('should display last updated dates', () => {
      render(<SkillProgressTracker skills={mockSkillProgressData} />);
      
      expect(screen.getByText('Last updated: 6/21/2025')).toBeInTheDocument();
      expect(screen.getByText('Last updated: 6/20/2025')).toBeInTheDocument();
      expect(screen.getByText('Last updated: 6/19/2025')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle skills with no milestones', () => {
      const skillsWithoutMilestones = [
        {
          ...mockSkillProgressData[0],
          milestones: [],
        },
      ];
      
      render(<SkillProgressTracker skills={skillsWithoutMilestones} showMilestones={true} />);
      
      expect(screen.getByText('JavaScript')).toBeInTheDocument();
      // Should not show milestones section if no milestones exist
    });

    it('should handle skills with not_started status', () => {
      const notStartedSkill = [
        {
          ...mockSkillProgressData[0],
          status: 'not_started' as const,
          progress: 0,
        },
      ];
      
      render(<SkillProgressTracker skills={notStartedSkill} />);
      
      expect(screen.getByText('not started')).toBeInTheDocument();
    });

    it('should handle skills with low priority', () => {
      const lowPrioritySkill = [
        {
          ...mockSkillProgressData[0],
          priority: 'low' as const,
        },
      ];
      
      render(<SkillProgressTracker skills={lowPrioritySkill} />);
      
      expect(screen.getByText('low')).toBeInTheDocument();
    });
  });
});
