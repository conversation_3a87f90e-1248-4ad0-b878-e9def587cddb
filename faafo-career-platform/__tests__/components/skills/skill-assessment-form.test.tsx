/**
 * Skill Assessment Form Tests
 * 
 * Tests Skill Assessment Form component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import SkillAssessmentForm from '@/components/skills/SkillAssessmentForm';

// Mock toast
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

// Mock UI components that cause issues in tests
jest.mock('@/components/ui/slider', () => ({
  Slider: ({ value, onValueChange, ...props }: any) => (
    <input
      type="range"
      value={value[0]}
      onChange={(e) => onValueChange([parseInt(e.target.value)])}
      data-testid="slider"
      {...props}
    />
  ),
}));

jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <div data-testid="card-title" {...props}>{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} data-testid="button" {...props}>
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input data-testid="input" {...props} />,
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label data-testid="label" {...props}>{children}</label>,
}));

jest.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => <textarea data-testid="textarea" {...props} />,
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span data-testid="badge" {...props}>{children}</span>,
}));

jest.mock('@/components/ui/progress', () => ({
  Progress: ({ value, ...props }: any) => <div data-testid="progress" data-value={value} {...props} />,
}));

jest.mock('@/components/ui/alert', () => ({
  Alert: ({ children, ...props }: any) => <div data-testid="alert" {...props}>{children}</div>,
  AlertDescription: ({ children, ...props }: any) => <div data-testid="alert-description" {...props}>{children}</div>,
}));

// Mock Lucide icons
jest.mock('lucide-react', () => ({
  Loader2: () => <div data-testid="loader2-icon" />,
  Star: () => <div data-testid="star-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  BookOpen: () => <div data-testid="book-open-icon" />,
  Award: () => <div data-testid="award-icon" />,
}));

// Mock skill data
const mockSkills = [
  {
    id: 'skill-1',
    name: 'JavaScript',
    category: 'Programming',
    description: 'JavaScript programming language',
  },
  {
    id: 'skill-2',
    name: 'React',
    category: 'Frontend',
    description: 'React framework',
  },
  {
    id: 'common-1',
    name: 'Python',
    category: 'Programming',
    description: 'Python programming language',
  },
];

const mockInitialAssessments = [
  {
    skillId: 'skill-1',
    skillName: 'JavaScript',
    selfRating: 7,
    confidenceLevel: 8,
    assessmentType: 'SELF_ASSESSMENT' as const,
    yearsOfExperience: 3,
    lastUsed: 'Currently using',
    notes: 'Strong in ES6+',
  },
];

describe('SkillAssessmentForm - TDD State Synchronization Fix', () => {
  const mockOnSubmit = jest.fn();
  const mockOnSkillSearch = jest.fn();
  const mockOnAssessmentsChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnSkillSearch.mockResolvedValue(mockSkills);
  });

  describe('State Synchronization (Critical Bug Fix)', () => {
    it('should sync internal state with initialAssessments prop on mount', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          initialAssessments={mockInitialAssessments}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      // Verify initial assessment data is displayed
      await waitFor(() => {
        expect(screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
      });

      expect(screen.getByDisplayValue('Currently using')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Strong in ES6+')).toBeInTheDocument();
    });

    it('should update internal state when initialAssessments prop changes', async () => {
      const { rerender } = render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          initialAssessments={[]}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      // Initially should show empty form
      expect(screen.getByPlaceholderText('e.g., JavaScript, React, Python')).toHaveValue('');

      // Update with initial assessments
      rerender(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          initialAssessments={mockInitialAssessments}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      // Should now show the assessment data
      await waitFor(() => {
        expect(screen.getByDisplayValue('JavaScript')).toBeInTheDocument();
      });
    });

    it('should call onAssessmentsChange when internal state updates', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          initialAssessments={[]}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
      
      await userEvent.type(skillInput, 'TypeScript');

      await waitFor(() => {
        expect(mockOnAssessmentsChange).toHaveBeenCalled();
      });
    });
  });

  describe('Search Result Selection', () => {
    it('should populate form fields when selecting a skill from search results', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');
      
      // Type to trigger search
      await userEvent.type(searchInput, 'Java');

      // Wait for search results
      await waitFor(() => {
        expect(mockOnSkillSearch).toHaveBeenCalledWith('Java');
      });

      // Click on a search result
      const searchResult = await screen.findByText('JavaScript');
      await userEvent.click(searchResult);

      // Verify form field is populated
      await waitFor(() => {
        const skillNameInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
        expect(skillNameInput).toHaveValue('JavaScript');
      });

      // Verify search is cleared
      expect(searchInput).toHaveValue('');
    });

    it('should handle fallback skills (common-* ids) correctly', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          onAssessmentsChange={mockOnAssessmentsChange}
        />
      );

      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');
      
      await userEvent.type(searchInput, 'Python');

      await waitFor(() => {
        expect(mockOnSkillSearch).toHaveBeenCalledWith('Python');
      });

      // Click on fallback skill (common-1)
      const searchResult = await screen.findByText('Python');
      await userEvent.click(searchResult);

      // Verify form field is populated
      await waitFor(() => {
        const skillNameInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
        expect(skillNameInput).toHaveValue('Python');
      });

      // Verify onAssessmentsChange was called with updated data
      expect(mockOnAssessmentsChange).toHaveBeenCalled();
    });
  });

  describe('Form Validation', () => {
    it('should validate required skill name', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
        />
      );

      const submitButton = screen.getByText('Submit Assessment');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Skill name is required')).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });

    it('should validate rating ranges', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
        />
      );

      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
      await userEvent.type(skillInput, 'JavaScript');

      // Try to submit with invalid ratings (this would require manipulating the slider values)
      // For now, we'll test the validation function indirectly
      const submitButton = screen.getByText('Submit Assessment');
      await userEvent.click(submitButton);

      // Should pass validation with default values (5/10)
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalled();
      });
    });
  });

  describe('Bulk Mode', () => {
    it('should allow adding multiple assessments in bulk mode', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          mode="bulk"
          maxAssessments={5}
        />
      );

      // Should start with one assessment
      expect(screen.getByText('Assessment 1')).toBeInTheDocument();

      // Add another assessment
      const addButton = screen.getByText('Add Another Skill Assessment');
      await userEvent.click(addButton);

      // Should now have two assessments
      expect(screen.getByText('Assessment 2')).toBeInTheDocument();
    });

    it('should preserve state on submit in bulk mode', async () => {
      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearch}
          mode="bulk"
          preserveStateOnSubmit={true}
        />
      );

      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
      await userEvent.type(skillInput, 'JavaScript');

      const submitButton = screen.getByText('Submit Assessment');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalled();
      });

      // Form should still have the data
      expect(skillInput).toHaveValue('JavaScript');
    });
  });

  describe('Error Handling', () => {
    it('should handle search errors gracefully', async () => {
      const mockOnSkillSearchError = jest.fn().mockRejectedValue(new Error('Search failed'));

      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmit}
          onSkillSearch={mockOnSkillSearchError}
        />
      );

      const searchInput = screen.getByPlaceholderText('Search for skills to assess...');
      await userEvent.type(searchInput, 'Java');

      await waitFor(() => {
        expect(mockOnSkillSearchError).toHaveBeenCalledWith('Java');
      });

      // Should not crash and should handle error gracefully
      expect(searchInput).toBeInTheDocument();
    });

    it('should handle submit errors gracefully', async () => {
      const mockOnSubmitError = jest.fn().mockRejectedValue(new Error('Submit failed'));

      render(
        <SkillAssessmentForm
          onSubmit={mockOnSubmitError}
          onSkillSearch={mockOnSkillSearch}
        />
      );

      const skillInput = screen.getByPlaceholderText('e.g., JavaScript, React, Python');
      await userEvent.type(skillInput, 'JavaScript');

      const submitButton = screen.getByText('Submit Assessment');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmitError).toHaveBeenCalled();
      });

      // Button should not be stuck in loading state
      expect(screen.getByText('Submit Assessment')).toBeInTheDocument();
    });
  });
});
