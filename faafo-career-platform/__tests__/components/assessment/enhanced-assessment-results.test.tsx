/**
 * Enhanced Assessment Results Tests
 * 
 * Tests Enhanced Assessment Results component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { EnhancedAssessmentResults } from '@/components/assessment/EnhancedAssessmentResults';

// Mock the session
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: { user: { id: '1', email: '<EMAIL>' } },
    status: 'authenticated',
  }),
}));

// Mock the tabs component
jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, ...props }: any) => <div data-testid="tabs" {...props}>{children}</div>,
  TabsContent: ({ children, ...props }: any) => <div data-testid="tabs-content" {...props}>{children}</div>,
  TabsList: ({ children, ...props }: any) => <div data-testid="tabs-list" {...props}>{children}</div>,
  TabsTrigger: ({ children, ...props }: any) => <button data-testid="tabs-trigger" {...props}>{children}</button>,
}));

// Mock other UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 data-testid="card-title" {...props}>{children}</h3>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button data-testid="button" onClick={onClick} {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/progress', () => ({
  Progress: ({ value, ...props }: any) => (
    <div data-testid="progress" data-value={value} {...props}>
      <div style={{ width: `${value}%` }}></div>
    </div>
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span data-testid="badge" {...props}>{children}</span>,
}));

// Mock the AI Insights Panel
jest.mock('@/components/assessment/AIInsightsPanel', () => ({
  AIInsightsPanel: ({ assessmentData }: any) => (
    <div data-testid="ai-insights-panel">AI Insights for {assessmentData?.id}</div>
  ),
}));

const mockAssessmentResult = {
  id: 'test-assessment-1',
  userId: '1',
  responses: {
    'career-interests': ['technology', 'problem-solving'],
    'work-style': ['collaborative', 'analytical'],
    'skills': ['programming', 'communication'],
    'values': ['innovation', 'work-life-balance'],
  },
  scores: {
    technology: 85,
    business: 60,
    creative: 45,
    healthcare: 30,
  },
  careerPaths: [
    {
      id: '1',
      title: 'Software Developer',
      description: 'Build software applications',
      match: 90,
      reasoning: 'Strong technical skills and problem-solving abilities',
      requiredSkills: ['Programming', 'Problem-solving', 'Teamwork'],
      averageSalary: '$95,000',
      jobGrowth: 'High',
      education: "Bachelor's degree in Computer Science",
    },
    {
      id: '2',
      title: 'Data Scientist',
      description: 'Analyze complex data sets',
      match: 85,
      reasoning: 'Analytical mindset and technical background',
      requiredSkills: ['Statistics', 'Programming', 'Data Analysis'],
      averageSalary: '$120,000',
      jobGrowth: 'Very High',
      education: "Bachelor's degree in Statistics or related field",
    },
  ],
  skillAnalysis: {
    strengths: ['Programming', 'Problem-solving', 'Communication'],
    gaps: ['Cloud computing', 'DevOps', 'Machine Learning'],
    recommendations: [
      'Take an AWS certification course',
      'Learn Docker and Kubernetes',
      'Complete a machine learning bootcamp',
    ],
  },
  learningPath: {
    immediate: [
      { skill: 'Cloud Computing', priority: 'High', timeframe: '3 months' },
      { skill: 'DevOps', priority: 'Medium', timeframe: '6 months' },
    ],
    shortTerm: [
      { skill: 'Machine Learning', priority: 'High', timeframe: '6-12 months' },
    ],
    longTerm: [
      { skill: 'Leadership', priority: 'Medium', timeframe: '1-2 years' },
    ],
  },
  nextSteps: [
    'Complete your profile to get personalized recommendations',
    'Explore the recommended learning resources',
    'Connect with professionals in your field of interest',
    'Set up learning goals and track your progress',
  ],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

describe('EnhancedAssessmentResults', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all four main tabs', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    expect(screen.getByText('Career Paths')).toBeInTheDocument();
    expect(screen.getByText('Skill Analysis')).toBeInTheDocument();
    expect(screen.getByText('Learning Path')).toBeInTheDocument();
    expect(screen.getByText('Next Steps')).toBeInTheDocument();
  });

  it('should display career path recommendations with match percentages', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    expect(screen.getByText('Software Developer')).toBeInTheDocument();
    expect(screen.getByText('Data Scientist')).toBeInTheDocument();
    expect(screen.getByText('90%')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
  });

  it('should show detailed career information when expanded', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    expect(screen.getByText('Strong technical skills and problem-solving abilities')).toBeInTheDocument();
    expect(screen.getByText('$95,000')).toBeInTheDocument();
    expect(screen.getByText('High')).toBeInTheDocument();
  });

  it('should display skill analysis with strengths and gaps', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    // Switch to Skill Analysis tab
    const skillAnalysisTab = screen.getByText('Skill Analysis');
    fireEvent.click(skillAnalysisTab);

    expect(screen.getByText('Programming')).toBeInTheDocument();
    expect(screen.getByText('Cloud computing')).toBeInTheDocument();
    expect(screen.getByText('Take an AWS certification course')).toBeInTheDocument();
  });

  it('should show learning path with different timeframes', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    // Switch to Learning Path tab
    const learningPathTab = screen.getByText('Learning Path');
    fireEvent.click(learningPathTab);

    expect(screen.getByText('Cloud Computing')).toBeInTheDocument();
    expect(screen.getByText('3 months')).toBeInTheDocument();
    expect(screen.getByText('Machine Learning')).toBeInTheDocument();
    expect(screen.getByText('6-12 months')).toBeInTheDocument();
  });

  it('should display actionable next steps', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    // Switch to Next Steps tab
    const nextStepsTab = screen.getByText('Next Steps');
    fireEvent.click(nextStepsTab);

    expect(screen.getByText('Complete your profile to get personalized recommendations')).toBeInTheDocument();
    expect(screen.getByText('Set up learning goals and track your progress')).toBeInTheDocument();
  });

  it('should include AI Insights panel', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    expect(screen.getByTestId('ai-insights-panel')).toBeInTheDocument();
    expect(screen.getByText('AI Insights for test-assessment-1')).toBeInTheDocument();
  });

  it('should handle missing career paths gracefully', () => {
    const resultWithoutPaths = {
      ...mockAssessmentResult,
      careerPaths: [],
    };

    render(<EnhancedAssessmentResults result={resultWithoutPaths} />);

    expect(screen.getByText(/no career paths found/i)).toBeInTheDocument();
  });

  it('should handle missing skill analysis gracefully', () => {
    const resultWithoutSkills = {
      ...mockAssessmentResult,
      skillAnalysis: null,
    };

    render(<EnhancedAssessmentResults result={resultWithoutSkills} />);

    // Switch to Skill Analysis tab
    const skillAnalysisTab = screen.getByText('Skill Analysis');
    fireEvent.click(skillAnalysisTab);

    expect(screen.getByText(/skill analysis not available/i)).toBeInTheDocument();
  });

  it('should display progress bars for skill levels', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    const progressBars = screen.getAllByTestId('progress');
    expect(progressBars.length).toBeGreaterThan(0);
    
    // Check that progress values are set correctly
    const techProgress = progressBars.find(bar => bar.getAttribute('data-value') === '85');
    expect(techProgress).toBeInTheDocument();
  });

  it('should allow bookmarking career paths', () => {
    render(<EnhancedAssessmentResults result={mockAssessmentResult} />);

    const bookmarkButtons = screen.getAllByText(/bookmark/i);
    expect(bookmarkButtons.length).toBeGreaterThan(0);

    fireEvent.click(bookmarkButtons[0]);
    // Note: In a real test, we'd mock the API call and verify it was made
  });
});
