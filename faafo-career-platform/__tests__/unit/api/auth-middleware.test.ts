/**
 * Comprehensive Authentication Middleware Tests
 * Tests route protection, admin access control, and security headers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Mock next-auth/jwt
jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn(),
}));

const mockGetToken = getToken as jest.MockedFunction<typeof getToken>;

// Import middleware after mocking
import { middleware } from '../../../middleware';

describe('Authentication Middleware', () => {
  let mockRequest: Partial<NextRequest>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRequest = {
      nextUrl: {
        pathname: '/dashboard',
        search: '',
        searchParams: new URLSearchParams(),
      } as any,
      url: 'http://localhost:3000/dashboard',
      headers: new Headers(),
    };
  });

  describe('Public Routes', () => {
    it('should allow access to public routes without authentication', async () => {
      mockRequest.nextUrl!.pathname = '/';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      // Should not redirect to login
      expect(response.status).not.toBe(307);
    });

    it('should allow access to auth pages when not authenticated', async () => {
      mockRequest.nextUrl!.pathname = '/login';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).not.toBe(307);
    });

    it('should allow access to static assets', async () => {
      mockRequest.nextUrl!.pathname = '/favicon.ico';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).not.toBe(307);
    });
  });

  describe('Protected Routes', () => {
    it('should redirect unauthenticated users to login', async () => {
      mockRequest.nextUrl!.pathname = '/dashboard';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(307); // Redirect status
      
      const location = response.headers.get('location');
      expect(location).toContain('/login');
      expect(location).toContain('callbackUrl');
    });

    it('should allow authenticated users to access protected routes', async () => {
      mockRequest.nextUrl!.pathname = '/dashboard';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).not.toBe(307);
    });

    it('should preserve callback URL in login redirect', async () => {
      mockRequest.nextUrl!.pathname = '/profile';
      mockRequest.nextUrl!.search = '?tab=settings';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      const location = response.headers.get('location');
      expect(location).toContain('callbackUrl=%2Fprofile%3Ftab%3Dsettings');
    });
  });

  describe('Admin Routes', () => {
    it('should redirect non-admin users from admin routes', async () => {
      mockRequest.nextUrl!.pathname = '/admin/dashboard';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(403);
      
      const body = await response.json();
      expect(body.error).toBe('Admin access required');
    });

    it('should allow admin users to access admin routes', async () => {
      mockRequest.nextUrl!.pathname = '/admin/dashboard';
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        email: '<EMAIL>',
        role: 'admin'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response).toBeDefined();
      expect(response.status).not.toBe(403);
      expect(response.status).not.toBe(307);
    });

    it('should redirect unauthenticated users from admin routes to login', async () => {
      mockRequest.nextUrl!.pathname = '/admin/users';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).toBe(307);
      const location = response.headers.get('location');
      expect(location).toContain('/login');
    });
  });

  describe('API Route Protection', () => {
    it('should block unauthenticated access to protected API routes', async () => {
      mockRequest.nextUrl!.pathname = '/api/profile';
      mockGetToken.mockResolvedValue(null);

      const response = await middleware(mockRequest as NextRequest);

      expect(response.status).toBe(401);
      const body = await response.json();
      expect(body.error).toBe('Authentication required');
    });

    it('should allow authenticated access to protected API routes', async () => {
      mockRequest.nextUrl!.pathname = '/api/profile';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).not.toBe(401);
    });

    it('should block non-admin access to admin API routes', async () => {
      mockRequest.nextUrl!.pathname = '/api/admin/database';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).toBe(403);
      const body = await response.json();
      expect(body.error).toBe('Admin access required');
    });

    it('should allow admin access to admin API routes', async () => {
      mockRequest.nextUrl!.pathname = '/api/admin/database';
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        email: '<EMAIL>',
        role: 'admin'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).not.toBe(403);
      expect(response.status).not.toBe(401);
    });
  });

  describe('Auth Page Redirects', () => {
    it('should redirect authenticated users away from login page', async () => {
      mockRequest.nextUrl!.pathname = '/login';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).toBe(307);
      const location = response.headers.get('location');
      expect(location).toContain('/dashboard');
    });

    it('should redirect authenticated users away from signup page', async () => {
      mockRequest.nextUrl!.pathname = '/signup';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      expect(response.status).toBe(307);
      const location = response.headers.get('location');
      expect(location).toContain('/dashboard');
    });

    it('should respect callback URL when redirecting from auth pages', async () => {
      mockRequest.nextUrl!.pathname = '/login';
      mockRequest.nextUrl!.searchParams.set('callbackUrl', '/profile');
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      const location = response.headers.get('location');
      expect(location).toContain('/profile');
    });
  });

  describe('Security Headers', () => {
    it('should add security headers to responses', async () => {
      mockRequest.nextUrl!.pathname = '/dashboard';
      mockGetToken.mockResolvedValue({
        sub: 'user-id',
        email: '<EMAIL>',
        role: 'user'
      });

      const response = await middleware(mockRequest as NextRequest);
      
      // Check for common security headers
      expect(response.headers.get('X-Frame-Options')).toBeDefined();
      expect(response.headers.get('X-Content-Type-Options')).toBeDefined();
      expect(response.headers.get('Referrer-Policy')).toBeDefined();
    });
  });
});
