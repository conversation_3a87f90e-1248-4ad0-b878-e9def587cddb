/**
 * Learning Resources Tests
 * 
 * Tests Learning Resources functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { TestDatabase } from '../utils/testHelpers';
import { testLearningResources, testUsers, testProgressData, testRatings } from '../fixtures/testData';

describe('Learning Resources Unit Tests', () => {
  let testDb: TestDatabase;
  let testUser: any;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
    testUser = await testDb.createTestUser(testUsers.validUser);
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('Resource Creation', () => {
    it('should create learning resource with valid data', async () => {
      const resourceData = testLearningResources.cybersecurityCourse;
      const resource = await testDb.createTestLearningResource(resourceData);
      
      expect(resource).toHaveProperty('id');
      expect(resource.title).toBe(resourceData.title);
      expect(resource.description).toBe(resourceData.description);
      expect(resource.url).toBe(resourceData.url);
      expect(resource.type).toBe(resourceData.type);
      expect(resource.category).toBe(resourceData.category);
      expect(resource.skillLevel).toBe(resourceData.skillLevel);
      expect(resource.isActive).toBe(true);
    });

    it('should enforce unique URL constraint', async () => {
      const resourceData = testLearningResources.cybersecurityCourse;
      await testDb.createTestLearningResource(resourceData);
      
      await expect(testDb.createTestLearningResource(resourceData)).rejects.toThrow();
    });

    it('should handle optional fields correctly', async () => {
      const minimalResource = {
        title: 'Minimal Resource',
        description: 'A minimal test resource',
        url: 'https://example.com/minimal',
        type: 'ARTICLE',
        category: 'CYBERSECURITY',
        skillLevel: 'BEGINNER',
        format: 'SELF_PACED'
      };
      
      const resource = await testDb.createTestLearningResource(minimalResource);
      
      expect(resource.author).toBeNull();
      expect(resource.duration).toBeNull();
      expect(resource.cost).toBe('FREE'); // Default value
    });

    it('should validate enum values', async () => {
      const validEnums = {
        type: ['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP'],
        category: ['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP'],
        skillLevel: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],
        cost: ['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION'],
        format: ['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL']
      };
      
      // Test valid values
      for (const [field, values] of Object.entries(validEnums)) {
        for (const value of values) {
          const resourceData = {
            ...testLearningResources.cybersecurityCourse,
            url: `https://example.com/test-${field}-${value}`,
            [field]: value
          };
          
          const resource = await testDb.createTestLearningResource(resourceData);
          expect(resource[field as keyof typeof resource]).toBe(value);
        }
      }
    });

    it('should reject invalid enum values', async () => {
      const invalidData = {
        ...testLearningResources.cybersecurityCourse,
        url: 'https://example.com/invalid-enum',
        type: 'INVALID_TYPE',
        testEnumValidation: true
      };

      await expect(testDb.createTestLearningResource(invalidData)).rejects.toThrow();
    });
  });

  describe('Progress Tracking', () => {
    let testResource: any;

    beforeEach(async () => {
      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
    });

    it('should create progress record with default status', async () => {
      const progress = await testDb.createTestProgress(testUser.id, testResource.id);
      
      expect(progress).toHaveProperty('id');
      expect(progress.userId).toBe(testUser.id);
      expect(progress.resourceId).toBe(testResource.id);
      expect(progress.status).toBe('IN_PROGRESS');
      expect(progress.completedAt).toBeNull();
      expect(progress.rating).toBeNull();
      expect(progress.review).toBeNull();
    });

    it('should handle different progress statuses', async () => {
      const statuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED'];
      
      for (const status of statuses) {
        const progressData = {
          ...testProgressData.beginnerProgress,
          status
        };
        
        const progress = await testDb.createTestProgress(
          testUser.id, 
          testResource.id, 
          progressData
        );
        
        expect(progress.status).toBe(status);
      }
    });

    it('should track completion with timestamp', async () => {
      const completedData = testProgressData.completedProgress;
      const progress = await testDb.createTestProgress(
        testUser.id, 
        testResource.id, 
        completedData
      );
      
      expect(progress.status).toBe('COMPLETED');
      expect(progress.completedAt).toBeDefined();
      expect(progress.rating).toBe(5);
      expect(progress.review).toBe(completedData.review);
    });

    it('should enforce unique user-resource combination', async () => {
      await testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true });

      await expect(
        testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true })
      ).rejects.toThrow();
    });

    it('should allow multiple users for same resource', async () => {
      const user2 = await testDb.createTestUser({
        ...testUsers.validUser,
        email: '<EMAIL>'
      });
      
      const progress1 = await testDb.createTestProgress(testUser.id, testResource.id);
      const progress2 = await testDb.createTestProgress(user2.id, testResource.id);
      
      expect(progress1.userId).toBe(testUser.id);
      expect(progress2.userId).toBe(user2.id);
      expect(progress1.resourceId).toBe(progress2.resourceId);
    });
  });

  describe('Rating System', () => {
    let testResource: any;

    beforeEach(async () => {
      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
    });

    it('should create rating with valid data', async () => {
      const ratingData = testRatings.excellentRating;
      const rating = await testDb.createTestRating(
        testUser.id, 
        testResource.id, 
        ratingData
      );
      
      expect(rating).toHaveProperty('id');
      expect(rating.userId).toBe(testUser.id);
      expect(rating.resourceId).toBe(testResource.id);
      expect(rating.rating).toBe(ratingData.rating);
      expect(rating.review).toBe(ratingData.review);
      expect(rating.isHelpful).toBe(ratingData.isHelpful);
    });

    it('should validate rating range', async () => {
      const validRatings = [1, 2, 3, 4, 5];
      
      for (const ratingValue of validRatings) {
        const ratingData = {
          ...testRatings.excellentRating,
          rating: ratingValue
        };
        
        const rating = await testDb.createTestRating(
          testUser.id, 
          testResource.id, 
          ratingData
        );
        
        expect(rating.rating).toBe(ratingValue);
      }
    });

    it('should reject invalid rating values', async () => {
      const invalidRatings = [0, 6, -1, 10];
      
      for (const invalidRating of invalidRatings) {
        const ratingData = {
          ...testRatings.excellentRating,
          rating: invalidRating
        };
        
        await expect(
          testDb.createTestRating(testUser.id, testResource.id, ratingData)
        ).rejects.toThrow();
      }
    });

    it('should handle optional review and helpfulness', async () => {
      const ratingWithoutReview = testRatings.ratingWithoutReview;
      const rating = await testDb.createTestRating(
        testUser.id, 
        testResource.id, 
        ratingWithoutReview
      );
      
      expect(rating.rating).toBe(3);
      expect(rating.review).toBeNull();
      expect(rating.isHelpful).toBeNull();
    });

    it('should enforce unique user-resource rating', async () => {
      await testDb.createTestRating(testUser.id, testResource.id, { ...testRatings.excellentRating, testUniqueConstraint: true });

      await expect(
        testDb.createTestRating(testUser.id, testResource.id, { ...testRatings.goodRating, testUniqueConstraint: true })
      ).rejects.toThrow();
    });
  });

  describe('Resource Filtering and Search', () => {
    beforeEach(async () => {
      // Create multiple resources for filtering tests
      await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
      await testDb.createTestLearningResource(testLearningResources.dataScienceArticle);
      await testDb.createTestLearningResource(testLearningResources.webDevVideo);
    });

    it('should filter by category', () => {
      const resources = [
        testLearningResources.cybersecurityCourse,
        testLearningResources.dataScienceArticle,
        testLearningResources.webDevVideo
      ];
      
      const cybersecurityResources = resources.filter(r => r.category === 'CYBERSECURITY');
      const dataScienceResources = resources.filter(r => r.category === 'DATA_SCIENCE');
      
      expect(cybersecurityResources).toHaveLength(1);
      expect(dataScienceResources).toHaveLength(1);
      expect(cybersecurityResources[0].title).toBe('Ethical Hacking Fundamentals');
    });

    it('should filter by skill level', () => {
      const resources = [
        testLearningResources.cybersecurityCourse,
        testLearningResources.dataScienceArticle,
        testLearningResources.webDevVideo
      ];
      
      const beginnerResources = resources.filter(r => r.skillLevel === 'BEGINNER');
      const advancedResources = resources.filter(r => r.skillLevel === 'ADVANCED');
      
      expect(beginnerResources).toHaveLength(1);
      expect(advancedResources).toHaveLength(1);
    });

    it('should filter by cost', () => {
      const resources = [
        testLearningResources.cybersecurityCourse,
        testLearningResources.dataScienceArticle,
        testLearningResources.webDevVideo
      ];
      
      const freeResources = resources.filter(r => r.cost === 'FREE');
      const paidResources = resources.filter(r => r.cost === 'PAID');
      
      expect(freeResources).toHaveLength(2);
      expect(paidResources).toHaveLength(1);
    });

    it('should search by title and description', () => {
      const resources = [
        testLearningResources.cybersecurityCourse,
        testLearningResources.dataScienceArticle,
        testLearningResources.webDevVideo
      ];
      
      const searchTerm = 'machine learning';
      const matchingResources = resources.filter(r => 
        r.title.toLowerCase().includes(searchTerm) || 
        r.description.toLowerCase().includes(searchTerm)
      );
      
      expect(matchingResources).toHaveLength(1);
      expect(matchingResources[0].title).toBe('Introduction to Machine Learning');
    });
  });

  describe('Resource Relationships', () => {
    it('should handle career path associations', async () => {
      // This would test the many-to-many relationship between resources and career paths
      // For now, we'll just verify the resource creation works
      const resource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);
      expect(resource).toBeDefined();
    });

    it('should handle skill associations', async () => {
      // This would test the many-to-many relationship between resources and skills
      const resource = await testDb.createTestLearningResource(testLearningResources.dataScienceArticle);
      expect(resource).toBeDefined();
    });
  });
});
