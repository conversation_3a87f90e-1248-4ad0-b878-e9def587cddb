/**
 * Resume Builder Component Tests
 * 
 * Tests for the main ResumeBuilder component including form interactions,
 * data management, and user workflows.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useSession } from 'next-auth/react';
import { ResumeBuilder } from '@/components/resume-builder/ResumeBuilder';

// Mock dependencies
jest.mock('next-auth/react');
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('ResumeBuilder Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSession.mockReturnValue({
      data: {
        user: {
          email: '<EMAIL>',
          name: 'Test User'
        }
      },
      status: 'authenticated'
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Initial Render', () => {
    it('should render the resume builder form', () => {
      render(<ResumeBuilder />);
      
      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
      expect(screen.getByText('Create and customize your professional resume')).toBeInTheDocument();
      expect(screen.getByLabelText('Resume Title')).toBeInTheDocument();
      expect(screen.getByDisplayValue('My Resume')).toBeInTheDocument();
    });

    it('should render all form tabs', () => {
      render(<ResumeBuilder />);
      
      expect(screen.getByRole('tab', { name: 'Personal' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Experience' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Education' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Skills' })).toBeInTheDocument();
    });

    it('should show personal info tab by default', () => {
      render(<ResumeBuilder />);
      
      expect(screen.getByLabelText('First Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Last Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Email Address *')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('should update resume title when input changes', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      const titleInput = screen.getByLabelText('Resume Title');
      await user.clear(titleInput);
      await user.type(titleInput, 'Software Engineer Resume');
      
      expect(titleInput).toHaveValue('Software Engineer Resume');
    });

    it('should update template selection', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      const templateSelect = screen.getByRole('combobox');
      await user.click(templateSelect);
      
      const classicOption = screen.getByText('Classic');
      await user.click(classicOption);
      
      expect(screen.getByDisplayValue('classic')).toBeInTheDocument();
    });

    it('should switch between tabs', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      // Click on Experience tab
      const experienceTab = screen.getByRole('tab', { name: 'Experience' });
      await user.click(experienceTab);
      
      expect(screen.getByText('Work Experience')).toBeInTheDocument();
      expect(screen.getByText('Add your professional work experience, starting with the most recent')).toBeInTheDocument();
    });

    it('should update personal information', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      const firstNameInput = screen.getByLabelText('First Name *');
      const lastNameInput = screen.getByLabelText('Last Name *');
      
      await user.type(firstNameInput, 'John');
      await user.type(lastNameInput, 'Doe');
      
      expect(firstNameInput).toHaveValue('John');
      expect(lastNameInput).toHaveValue('Doe');
    });

    it('should update professional summary', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      const summaryTextarea = screen.getByPlaceholderText(/Experienced software engineer/);
      await user.type(summaryTextarea, 'Passionate developer with 5 years of experience');
      
      expect(summaryTextarea).toHaveValue('Passionate developer with 5 years of experience');
    });
  });

  describe('Data Loading', () => {
    it('should load existing resume when resumeId is provided', async () => {
      const mockResumeData = {
        id: 'resume-1',
        title: 'Existing Resume',
        personalInfo: {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>'
        },
        summary: 'Existing summary',
        experience: [],
        education: [],
        skills: [],
        template: 'classic',
        isPublic: false
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockResumeData
        })
      } as Response);

      render(<ResumeBuilder resumeId="resume-1" />);

      await waitFor(() => {
        expect(screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();
      });

      expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    it('should show loading spinner while loading resume', () => {
      mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves
      
      render(<ResumeBuilder resumeId="resume-1" />);
      
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('should show error when loading fails', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          success: false,
          error: 'Resume not found'
        })
      } as Response);

      render(<ResumeBuilder resumeId="resume-1" />);

      await waitFor(() => {
        expect(screen.getByText('Resume not found')).toBeInTheDocument();
      });
    });
  });

  describe('Save Functionality', () => {
    it('should save new resume when save button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnSave = jest.fn();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            id: 'new-resume-1',
            title: 'My Resume',
            personalInfo: {
              firstName: '',
              lastName: '',
              email: '<EMAIL>'
            },
            summary: '',
            experience: [],
            education: [],
            skills: [],
            template: 'modern',
            isPublic: false
          }
        })
      } as Response);

      render(<ResumeBuilder onSave={mockOnSave} />);
      
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('"title":"My Resume"')
        });
      });

      expect(mockOnSave).toHaveBeenCalled();
    });

    it('should update existing resume when save button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnSave = jest.fn();

      // Mock initial load
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            id: 'resume-1',
            title: 'Existing Resume',
            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            template: 'modern'
          }
        })
      } as Response);

      // Mock save
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { id: 'resume-1', title: 'Updated Resume' }
        })
      } as Response);

      render(<ResumeBuilder resumeId="resume-1" onSave={mockOnSave} />);

      await waitFor(() => {
        expect(screen.getByDisplayValue('Existing Resume')).toBeInTheDocument();
      });

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/resume-builder/resume-1', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.any(String)
        });
      });
    });

    it('should show error when save fails', async () => {
      const user = userEvent.setup();

      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          success: false,
          error: 'Failed to save resume'
        })
      } as Response);

      render(<ResumeBuilder />);
      
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save resume')).toBeInTheDocument();
      });
    });
  });

  describe('Preview Functionality', () => {
    it('should show preview when preview button is clicked', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      const previewButton = screen.getByRole('button', { name: /preview/i });
      await user.click(previewButton);
      
      expect(screen.getByText('Resume Preview')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
    });

    it('should return to edit mode from preview', async () => {
      const user = userEvent.setup();
      render(<ResumeBuilder />);
      
      // Go to preview
      const previewButton = screen.getByRole('button', { name: /preview/i });
      await user.click(previewButton);
      
      // Return to edit
      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);
      
      expect(screen.getByText('Resume Builder')).toBeInTheDocument();
      expect(screen.getByLabelText('Resume Title')).toBeInTheDocument();
    });
  });

  describe('Cancel Functionality', () => {
    it('should call onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      const mockOnCancel = jest.fn();
      
      render(<ResumeBuilder onCancel={mockOnCancel} />);
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Authentication', () => {
    it('should show error when user is not authenticated', async () => {
      const user = userEvent.setup();
      
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated'
      });

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: async () => ({
          success: false,
          error: 'You must be logged in to save a resume'
        })
      } as Response);

      render(<ResumeBuilder />);
      
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('You must be logged in to save a resume')).toBeInTheDocument();
      });
    });
  });
});
