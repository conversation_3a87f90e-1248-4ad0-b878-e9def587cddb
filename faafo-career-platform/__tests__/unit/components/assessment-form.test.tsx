/**
 * Assessment Form Integration Tests
 * Tests the complete assessment form submission flow
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AssessmentPage from '../src/app/assessment/page';

// Mock CSRF hook
jest.mock('../src/hooks/useCSRFToken', () => ({
  useCSRFToken: () => ({
    csrfFetch: jest.fn(),
    isLoading: false
  })
}));

// Mock logger
jest.mock('../src/lib/logger', () => ({
  log: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

// Mock SEO utils
jest.mock('../src/lib/seo/seo-utils', () => ({
  injectStructuredData: jest.fn()
}));

describe('Assessment Form Submission', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Set up authenticated session for assessment tests
    global.mockUseSession.mockReturnValue({
      data: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User'
        }
      },
      status: 'authenticated'
    });

    // Mock the fetch call for assessment API to return 404 (no existing assessment)
    global.fetch.mockResolvedValue({
      ok: false,
      status: 404,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
      headers: new Headers(),
      redirected: false,
      statusText: 'Not Found',
      type: 'basic',
      url: '',
      clone: jest.fn(),
      body: null,
      bodyUsed: false,
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
      blob: () => Promise.resolve(new Blob()),
      formData: () => Promise.resolve(new FormData()),
    });
  });

  test('should render assessment page without crashing', async () => {
    // This basic test will reveal if there are any fundamental rendering issues
    render(<AssessmentPage />);

    // Wait for the loading to complete and form to appear
    await waitFor(() => {
      expect(screen.getByText('Self-Assessment Questionnaire')).toBeInTheDocument();
    }, { timeout: 15000 });

    // Check if the step indicator is present
    await waitFor(() => {
      expect(screen.getByText(/Step \d+ of \d+/)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('should show progress bar', async () => {
    render(<AssessmentPage />);

    await waitFor(() => {
      expect(screen.getByText('Self-Assessment Questionnaire')).toBeInTheDocument();
    }, { timeout: 15000 });

    // Check if progress bar exists (it should be a div with specific styling)
    await waitFor(() => {
      const progressBars = document.querySelectorAll('.bg-blue-600');
      expect(progressBars.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  test('should have form elements present', async () => {
    render(<AssessmentPage />);

    await waitFor(() => {
      expect(screen.getByText('Self-Assessment Questionnaire')).toBeInTheDocument();
    }, { timeout: 15000 });

    // Check if there's a form element
    await waitFor(() => {
      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();
    }, { timeout: 5000 });
  });
});
