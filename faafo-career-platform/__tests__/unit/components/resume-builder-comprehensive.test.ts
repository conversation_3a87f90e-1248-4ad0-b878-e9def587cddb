/**
 * Memory-Optimized Resume Builder Tests
 *
 * Tests for the complete Resume Builder functionality including
 * data flow, validation, and business logic
 *
 * MEMORY OPTIMIZATION: Reduced test data sizes and improved cleanup
 */

import { z } from 'zod';
import { createCleanMock, ResourcePool } from '../../test-utils/memory-leak-fixes';

// Memory-optimized mock database with automatic cleanup
const mockResumes = new Map();
let mockResumeIdCounter = 1;

// Resource pool for reusing test objects
const resumePool = new ResourcePool(
  () => ({
    id: `resume-${mockResumeIdCounter++}`,
    userId: 'user-1',
    title: 'Test Resume',
    content: {},
    isActive: true,
    exportCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  (resume) => {
    resume.id = `resume-${mockResumeIdCounter++}`;
    resume.content = {};
    resume.exportCount = 0;
    resume.createdAt = new Date();
    resume.updatedAt = new Date();
    return resume;
  },
  5 // Limit pool size to 5 objects
);

// Memory-optimized Mock Prisma with clean mocks
const mockPrisma = {
  user: {
    findUnique: createCleanMock().mockResolvedValue({ id: 'user-1' })
  },
  resume: {
    findMany: createCleanMock().mockImplementation(() => {
      return Promise.resolve(Array.from(mockResumes.values()).filter(r => r.isActive));
    }),
    findFirst: createCleanMock().mockImplementation(({ where }) => {
      const resume = Array.from(mockResumes.values()).find(r =>
        r.id === where?.id && r.userId === where?.userId && r.isActive
      );
      return Promise.resolve(resume || null);
    }),
    create: createCleanMock().mockImplementation(({ data }) => {
      const newResume = resumePool.acquire();
      Object.assign(newResume, data, {
        id: `resume-${mockResumeIdCounter++}`,
        isActive: true,
        exportCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      mockResumes.set(newResume.id, newResume);
      return Promise.resolve(newResume);
    }),
    update: createCleanMock().mockImplementation(({ where, data }) => {
      const existing = mockResumes.get(where?.id);
      if (existing) {
        const updated = { ...existing, ...data, updatedAt: new Date() };
        mockResumes.set(where.id, updated);
        return Promise.resolve(updated);
      }
      return Promise.resolve(null);
    })
  }
};

// Mock session
const mockSession = {
  user: { email: '<EMAIL>' }
};

jest.mock('@/lib/prisma', () => mockPrisma);
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn().mockResolvedValue(mockSession)
}));

describe('Resume Builder - Comprehensive Tests', () => {
  beforeEach(() => {
    // Clear mock data before each test
    mockResumes.clear();
    mockResumeIdCounter = 1;
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Memory cleanup after each test
    mockResumes.clear();
    resumePool.clear();

    // Clear all mock implementations to prevent memory leaks
    Object.values(mockPrisma.resume).forEach(mock => {
      if (mock.mockClear) mock.mockClear();
    });
    Object.values(mockPrisma.user).forEach(mock => {
      if (mock.mockClear) mock.mockClear();
    });
  });

  describe('Resume Data Management', () => {
    it('should create a complete resume with all sections', async () => {
      const resumeData = {
        title: 'Full Stack Developer Resume',
        personalInfo: {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '******-0456',
          location: 'New York, NY',
          website: 'https://janesmith.dev',
          linkedIn: 'https://linkedin.com/in/janesmith'
        },
        summary: 'Passionate full-stack developer with 3 years of experience building scalable web applications.',
        experience: [
          {
            company: 'StartupCo',
            position: 'Full Stack Developer',
            startDate: '2021-06',
            endDate: '2024-01',
            description: 'Develop and maintain web applications using React, Node.js, and PostgreSQL.',
            achievements: [
              'Built user authentication system serving 10,000+ users',
              'Reduced page load times by 60% through optimization',
              'Implemented CI/CD pipeline reducing deployment time by 80%'
            ]
          },
          {
            company: 'Current Company',
            position: 'Senior Developer',
            startDate: '2024-01',
            description: 'Leading frontend development team.'
          }
        ],
        education: [
          {
            institution: 'State University',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            startDate: '2017-09',
            endDate: '2021-05',
            gpa: '3.7',
            honors: 'Magna Cum Laude'
          }
        ],
        skills: [
          { name: 'React', level: 'ADVANCED', category: 'Frontend' },
          { name: 'Node.js', level: 'INTERMEDIATE', category: 'Backend' },
          { name: 'PostgreSQL', level: 'INTERMEDIATE', category: 'Database' },
          { name: 'Git', level: 'ADVANCED', category: 'Tools' }
        ],
        template: 'modern',
        isPublic: false
      };

      const result = await mockPrisma.resume.create({
        data: { ...resumeData, userId: 'user-1' }
      });

      expect(result).toBeDefined();
      expect(result.id).toBe('resume-1');
      expect(result.title).toBe('Full Stack Developer Resume');
      expect(result.personalInfo.firstName).toBe('Jane');
      expect(result.experience).toHaveLength(2);
      expect(result.education).toHaveLength(1);
      expect(result.skills).toHaveLength(4);
      expect(result.isActive).toBe(true);
    });

    it('should handle minimal resume data', async () => {
      const minimalResumeData = {
        title: 'Minimal Resume',
        personalInfo: {
          firstName: 'Min',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: []
      };

      const result = await mockPrisma.resume.create({
        data: { ...minimalResumeData, userId: 'user-1' }
      });

      expect(result).toBeDefined();
      expect(result.title).toBe('Minimal Resume');
      expect(result.personalInfo.firstName).toBe('Min');
      expect(result.experience).toHaveLength(0);
      expect(result.education).toHaveLength(0);
      expect(result.skills).toHaveLength(0);
    });

    it('should update existing resume', async () => {
      // Create initial resume
      const initialData = {
        title: 'Initial Resume',
        personalInfo: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: []
      };

      const created = await mockPrisma.resume.create({
        data: { ...initialData, userId: 'user-1' }
      });

      // Update the resume
      const updateData = {
        title: 'Updated Resume',
        summary: 'Added summary',
        experience: [
          {
            company: 'New Company',
            position: 'Developer',
            startDate: '2024-01',
            description: 'New role'
          }
        ]
      };

      const updated = await mockPrisma.resume.update({
        where: { id: created.id },
        data: updateData
      });

      expect(updated.title).toBe('Updated Resume');
      expect(updated.summary).toBe('Added summary');
      expect(updated.experience).toHaveLength(1);
      expect(updated.experience[0].company).toBe('New Company');
    });

    it('should soft delete resume', async () => {
      // Create resume
      const resumeData = {
        title: 'To Be Deleted',
        personalInfo: {
          firstName: 'Delete',
          lastName: 'Me',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: []
      };

      const created = await mockPrisma.resume.create({
        data: { ...resumeData, userId: 'user-1' }
      });

      // Soft delete
      const deleted = await mockPrisma.resume.update({
        where: { id: created.id },
        data: { isActive: false }
      });

      expect(deleted.isActive).toBe(false);

      // Verify it doesn't appear in active resumes
      const activeResumes = await mockPrisma.resume.findMany();
      expect(activeResumes).toHaveLength(0);
    });

    it('should list user resumes', async () => {
      // Create multiple resumes
      const resume1Data = {
        title: 'Resume 1',
        personalInfo: { firstName: 'User', lastName: 'One', email: '<EMAIL>' },
        experience: [], education: [], skills: []
      };

      const resume2Data = {
        title: 'Resume 2',
        personalInfo: { firstName: 'User', lastName: 'Two', email: '<EMAIL>' },
        experience: [], education: [], skills: []
      };

      await mockPrisma.resume.create({ data: { ...resume1Data, userId: 'user-1' } });
      await mockPrisma.resume.create({ data: { ...resume2Data, userId: 'user-1' } });

      const resumes = await mockPrisma.resume.findMany();
      expect(resumes).toHaveLength(2);
      expect(resumes[0].title).toBe('Resume 1');
      expect(resumes[1].title).toBe('Resume 2');
    });
  });

  describe('Resume Business Logic', () => {
    it('should handle experience with no end date (current position)', () => {
      const currentExperience = {
        company: 'Current Company',
        position: 'Senior Developer',
        startDate: '2024-01',
        // No endDate - current position
        description: 'Currently working here'
      };

      expect(currentExperience.endDate).toBeUndefined();
      expect(currentExperience.startDate).toBe('2024-01');
    });

    it('should validate skill levels', () => {
      const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
      
      validLevels.forEach(level => {
        const skill = {
          name: 'Test Skill',
          level,
          category: 'Test Category'
        };
        
        expect(validLevels).toContain(skill.level);
      });
    });

    it('should handle optional fields correctly', () => {
      const resumeWithOptionals = {
        title: 'Resume with Optionals',
        personalInfo: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          phone: '******-0123',
          location: 'Test City',
          website: 'https://test.com',
          linkedIn: 'https://linkedin.com/in/test'
        },
        summary: 'Test summary',
        experience: [
          {
            company: 'Test Company',
            position: 'Test Position',
            startDate: '2024-01',
            endDate: '2024-12',
            description: 'Test description',
            achievements: ['Achievement 1', 'Achievement 2']
          }
        ],
        education: [
          {
            institution: 'Test University',
            degree: 'Test Degree',
            field: 'Test Field',
            startDate: '2020-09',
            endDate: '2024-05',
            gpa: '3.8',
            honors: 'Test Honors'
          }
        ],
        skills: [
          {
            name: 'Test Skill',
            level: 'ADVANCED',
            category: 'Test Category'
          }
        ],
        template: 'modern',
        isPublic: false
      };

      // All fields should be present and valid
      expect(resumeWithOptionals.personalInfo.phone).toBeDefined();
      expect(resumeWithOptionals.personalInfo.location).toBeDefined();
      expect(resumeWithOptionals.personalInfo.website).toBeDefined();
      expect(resumeWithOptionals.personalInfo.linkedIn).toBeDefined();
      expect(resumeWithOptionals.summary).toBeDefined();
      expect(resumeWithOptionals.experience[0].endDate).toBeDefined();
      expect(resumeWithOptionals.experience[0].description).toBeDefined();
      expect(resumeWithOptionals.experience[0].achievements).toBeDefined();
      expect(resumeWithOptionals.education[0].field).toBeDefined();
      expect(resumeWithOptionals.education[0].gpa).toBeDefined();
      expect(resumeWithOptionals.education[0].honors).toBeDefined();
      expect(resumeWithOptionals.skills[0].level).toBeDefined();
      expect(resumeWithOptionals.skills[0].category).toBeDefined();
    });

    it('should handle template selection', () => {
      const templates = ['modern', 'classic', 'minimal', 'creative'];
      
      templates.forEach(template => {
        const resumeWithTemplate = {
          title: `${template} Resume`,
          personalInfo: {
            firstName: 'Template',
            lastName: 'User',
            email: '<EMAIL>'
          },
          experience: [],
          education: [],
          skills: [],
          template
        };

        expect(templates).toContain(resumeWithTemplate.template);
      });
    });
  });

  describe('Data Integrity', () => {
    it('should maintain data consistency across operations', async () => {
      const resumeData = {
        title: 'Consistency Test',
        personalInfo: {
          firstName: 'Consistent',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [
          {
            company: 'Test Company',
            position: 'Test Position',
            startDate: '2024-01'
          }
        ],
        education: [],
        skills: []
      };

      // Create
      const created = await mockPrisma.resume.create({
        data: { ...resumeData, userId: 'user-1' }
      });

      // Read
      const retrieved = await mockPrisma.resume.findFirst({
        where: { id: created.id, userId: 'user-1', isActive: true }
      });

      // Update
      const updated = await mockPrisma.resume.update({
        where: { id: created.id },
        data: { title: 'Updated Consistency Test' }
      });

      expect(retrieved).toBeDefined();
      expect(retrieved.title).toBe('Consistency Test');
      expect(updated.title).toBe('Updated Consistency Test');
      expect(updated.personalInfo.firstName).toBe('Consistent');
      expect(updated.experience).toHaveLength(1);
    });

    it('should handle concurrent operations safely', async () => {
      const resumeData = {
        title: 'Concurrent Test',
        personalInfo: {
          firstName: 'Concurrent',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: []
      };

      const created = await mockPrisma.resume.create({
        data: { ...resumeData, userId: 'user-1' }
      });

      // Simulate concurrent updates
      const update1Promise = mockPrisma.resume.update({
        where: { id: created.id },
        data: { title: 'Update 1' }
      });

      const update2Promise = mockPrisma.resume.update({
        where: { id: created.id },
        data: { summary: 'Update 2 summary' }
      });

      const [result1, result2] = await Promise.all([update1Promise, update2Promise]);

      // Both updates should succeed (in our mock implementation)
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
    });
  });
});
