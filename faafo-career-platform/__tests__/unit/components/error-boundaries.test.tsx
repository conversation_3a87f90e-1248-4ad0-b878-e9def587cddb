/**
 * Error Boundary Integration Tests
 * 
 * Tests that error boundaries are properly implemented and working
 * across all major components in the application.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { EnhancedErrorBoundary } from '@/components/EnhancedErrorBoundary';
import { InterviewPracticeErrorBoundary } from '@/components/error-boundary/InterviewPracticeErrorBoundary';
import { AIInsightsErrorBoundary } from '@/components/assessment/AIInsightsErrorBoundary';
import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

// Mock session for components that require authentication
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  },
  expires: '2024-12-31'
};

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = true }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error for error boundary');
  }
  return <div>Component rendered successfully</div>;
};

// Component that throws an async error
const ThrowAsyncError = ({ shouldThrow = true }: { shouldThrow?: boolean }) => {
  React.useEffect(() => {
    if (shouldThrow) {
      setTimeout(() => {
        throw new Error('Async test error');
      }, 100);
    }
  }, [shouldThrow]);
  
  return <div>Async component rendered</div>;
};

describe('Error Boundary Components', () => {
  describe('Basic ErrorBoundary', () => {
    it('should catch and display error fallback UI', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
    });

    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component rendered successfully')).toBeInTheDocument();
    });

    it('should call onError callback when error occurs', () => {
      const onErrorMock = jest.fn();
      
      render(
        <ErrorBoundary onError={onErrorMock}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(onErrorMock).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });

    it('should reset error state when retry button is clicked', () => {
      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      // Error should be displayed
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Click retry button
      fireEvent.click(screen.getByText(/try again/i));
      
      // Re-render with non-throwing component
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component rendered successfully')).toBeInTheDocument();
    });
  });

  describe('EnhancedErrorBoundary', () => {
    it('should provide enhanced error handling with retry logic', () => {
      render(
        <EnhancedErrorBoundary maxRetries={3} autoRetry={false}>
          <ThrowError />
        </EnhancedErrorBoundary>
      );

      expect(screen.getByText(/enhanced error boundary/i)).toBeInTheDocument();
      expect(screen.getByText(/retry/i)).toBeInTheDocument();
    });

    it('should show retry count in error UI', () => {
      render(
        <EnhancedErrorBoundary maxRetries={3}>
          <ThrowError />
        </EnhancedErrorBoundary>
      );

      // Should show retry information
      expect(screen.getByText(/retry/i)).toBeInTheDocument();
    });
  });

  describe('Specialized Error Boundaries', () => {
    it('should handle interview practice errors specifically', () => {
      render(
        <InterviewPracticeErrorBoundary>
          <ThrowError />
        </InterviewPracticeErrorBoundary>
      );

      expect(screen.getByText(/interview practice error/i)).toBeInTheDocument();
    });

    it('should handle AI insights errors specifically', () => {
      render(
        <AIInsightsErrorBoundary>
          <ThrowError />
        </AIInsightsErrorBoundary>
      );

      expect(screen.getByText(/ai insights error/i)).toBeInTheDocument();
    });

    it('should handle security errors with appropriate context', () => {
      render(
        <SecurityErrorBoundary context="auth">
          <ThrowError />
        </SecurityErrorBoundary>
      );

      expect(screen.getByText(/security error/i)).toBeInTheDocument();
    });
  });

  describe('Error Boundary Integration', () => {
    it('should work with session provider', () => {
      render(
        <SessionProvider session={mockSession}>
          <ErrorBoundary>
            <ThrowError />
          </ErrorBoundary>
        </SessionProvider>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });

    it('should handle nested error boundaries correctly', () => {
      render(
        <ErrorBoundary>
          <div>
            <h1>Outer Component</h1>
            <SecurityErrorBoundary context="api">
              <ThrowError />
            </SecurityErrorBoundary>
          </div>
        </ErrorBoundary>
      );

      // Inner error boundary should catch the error
      expect(screen.getByText(/security error/i)).toBeInTheDocument();
      // Outer component should still be visible
      expect(screen.getByText('Outer Component')).toBeInTheDocument();
    });
  });

  describe('Error Recovery', () => {
    it('should provide recovery options in error UI', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      // Should have recovery buttons
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
      expect(screen.getByText(/go home/i)).toBeInTheDocument();
    });

    it('should show error details for debugging', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      // Should show error information
      expect(screen.getByText(/error details/i)).toBeInTheDocument();
    });
  });

  describe('Production Error Handling', () => {
    const originalEnv = process.env.NODE_ENV;

    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    afterEach(() => {
      process.env.NODE_ENV = originalEnv;
    });

    it('should handle production errors appropriately', () => {
      // Mock Sentry for production error tracking
      (global as any).window = {
        Sentry: {
          captureException: jest.fn()
        }
      };

      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Clean up
      delete (global as any).window;
    });
  });
});

describe('Error Boundary Performance', () => {
  it('should not impact performance when no errors occur', () => {
    const startTime = performance.now();
    
    render(
      <ErrorBoundary>
        <div>
          {Array.from({ length: 100 }, (_, i) => (
            <div key={i}>Component {i}</div>
          ))}
        </div>
      </ErrorBoundary>
    );
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Should render quickly (less than 100ms for 100 components)
    expect(renderTime).toBeLessThan(100);
  });

  it('should handle multiple error boundaries efficiently', () => {
    render(
      <div>
        {Array.from({ length: 10 }, (_, i) => (
          <ErrorBoundary key={i}>
            <ThrowError shouldThrow={false} />
          </ErrorBoundary>
        ))}
      </div>
    );

    // All components should render successfully
    expect(screen.getAllByText('Component rendered successfully')).toHaveLength(10);
  });
});
