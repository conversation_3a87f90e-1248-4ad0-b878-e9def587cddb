/**
 * Interview Practice Useeffect Fix Tests
 * 
 * Tests Interview Practice Useeffect Fix component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import InterviewPracticePage from '@/components/interview-practice/InterviewPracticePage';

// Mock next-auth
jest.mock('next-auth/react');
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

// Mock the CSRF hook
jest.mock('@/hooks/useCSRFToken', () => ({
  useCSRFToken: () => ({
    csrfFetch: jest.fn(),
    isLoading: false,
    refreshToken: jest.fn(),
  }),
}));

// Mock fetch globally
global.fetch = jest.fn();

describe('InterviewPracticePage useEffect Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          sessions: [],
          overallProgress: {
            totalSessions: 0,
            completedSessions: 0,
            averageScore: undefined,
            totalPracticeTime: 0,
            currentStreak: 0,
            improvementRate: undefined,
          }
        }
      })
    });
  });

  it('should not cause infinite re-renders when authenticated', async () => {
    // Mock authenticated session
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'test-user-id', email: '<EMAIL>' },
      },
      status: 'authenticated',
      update: jest.fn(),
    });

    // Track how many times fetch is called to detect infinite loops
    const fetchSpy = jest.spyOn(global, 'fetch');

    render(<InterviewPracticePage />);

    // Wait for initial render and effects to complete
    await waitFor(() => {
      expect(screen.getByText('Interview Practice')).toBeInTheDocument();
    }, { timeout: 3000 });

    // Verify that fetch was called a reasonable number of times (not infinite)
    // Should be called twice: once for sessions, once for progress
    expect(fetchSpy).toHaveBeenCalledTimes(2);
    
    // Verify the correct API endpoints were called
    expect(fetchSpy).toHaveBeenCalledWith('/api/interview-practice?limit=5', expect.any(Object));
    expect(fetchSpy).toHaveBeenCalledWith('/api/interview-practice/progress', expect.any(Object));
  });

  it('should handle unauthenticated state without infinite loops', async () => {
    // Mock unauthenticated session
    mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated',
      update: jest.fn(),
    });

    const fetchSpy = jest.spyOn(global, 'fetch');

    render(<InterviewPracticePage />);

    // Wait for component to render
    await waitFor(() => {
      expect(screen.getByText('Interview Practice')).toBeInTheDocument();
    });

    // For unauthenticated users, no API calls should be made
    expect(fetchSpy).not.toHaveBeenCalled();
  });

  it('should handle loading state without infinite loops', async () => {
    // Mock loading session
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading',
      update: jest.fn(),
    });

    const fetchSpy = jest.spyOn(global, 'fetch');

    render(<InterviewPracticePage />);

    // Wait for component to render - should show loading state
    await waitFor(() => {
      expect(screen.getByText('Initializing Interview Practice...')).toBeInTheDocument();
    });

    // During loading, no API calls should be made yet
    expect(fetchSpy).not.toHaveBeenCalled();
  });

  it('should not re-render excessively when session changes', async () => {
    let renderCount = 0;
    const TestWrapper = () => {
      renderCount++;
      return <InterviewPracticePage />;
    };

    // Start with loading
    mockUseSession.mockReturnValue({
      data: null,
      status: 'loading',
      update: jest.fn(),
    });

    const { rerender } = render(<TestWrapper />);

    // Change to authenticated
    mockUseSession.mockReturnValue({
      data: {
        user: { id: 'test-user-id', email: '<EMAIL>' },
      },
      status: 'authenticated',
      update: jest.fn(),
    });

    rerender(<TestWrapper />);

    // Wait for effects to settle
    await waitFor(() => {
      expect(screen.getByText('Interview Practice')).toBeInTheDocument();
    });

    // Should not have excessive re-renders (allow some reasonable number)
    expect(renderCount).toBeLessThan(10);
  });
});
