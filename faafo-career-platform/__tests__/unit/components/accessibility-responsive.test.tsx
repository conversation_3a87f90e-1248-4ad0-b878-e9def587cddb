/**
 * Tests for accessibility and responsive design improvements
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

// Mock the accessibility provider for testing
const mockAccessibilityProvider = {
  settings: {
    reducedMotion: false,
    highContrast: false,
    largeText: false,
    screenReaderMode: false,
    keyboardNavigation: true,
    focusVisible: true,
  },
  updateSetting: jest.fn(),
  announceToScreenReader: jest.fn(),
  setFocusToElement: jest.fn(),
  skipToContent: jest.fn(),
};

jest.mock('../hooks/useAccessibilityEnhanced', () => ({
  useAccessibilityEnhanced: () => ({
    ...mockAccessibilityProvider,
    isReducedMotion: false,
    prefersHighContrast: false,
    prefersDarkMode: false,
  }),
  useAriaLiveRegion: () => ({
    announce: jest.fn(),
    createLiveRegion: () => React.createElement('div', { 'aria-live': 'polite', className: 'sr-only' }),
  }),
  useFormAccessibility: () => ({
    announceError: jest.fn(),
    announceSuccess: jest.fn(),
    getFieldProps: (name: string, error?: string, description?: string) => ({
      id: name,
      name,
      'aria-required': true,
      'aria-invalid': !!error,
      'aria-describedby': error ? `${name}-error` : description ? `${name}-description` : undefined,
    }),
  }),
}));

jest.mock('../hooks/useResponsiveDesign', () => ({
  useResponsiveDesign: () => ({
    windowSize: { width: 1024, height: 768 },
    currentBreakpoint: 'lg',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    getResponsiveValue: (values: any) => values.lg || values.md || values.sm || values.xs,
  }),
  useTouchDevice: () => ({
    isTouchDevice: false,
    hasHover: true,
  }),
}));

// Mock accessibility audit utility
jest.mock('../utils/accessibility-audit', () => ({
  auditAccessibility: jest.fn((container?: Element) => {
    const issues = [];

    // Check for images without alt text
    if (container?.querySelector('img:not([alt])')) {
      issues.push({
        rule: 'img-alt',
        severity: 'serious',
        message: 'Image missing alt text',
        element: container.querySelector('img:not([alt])'),
      });
    }

    // Check for form controls without labels
    if (container?.querySelector('input:not([aria-label]):not([aria-labelledby])')) {
      issues.push({
        rule: 'form-label',
        severity: 'critical',
        message: 'Form control missing accessible label',
        element: container.querySelector('input:not([aria-label]):not([aria-labelledby])'),
      });
    }

    // Check for heading hierarchy - h3 after h1 without h2
    if (container?.querySelector('h1') && container?.querySelector('h3') && !container?.querySelector('h2')) {
      issues.push({
        rule: 'heading-hierarchy',
        severity: 'moderate',
        message: 'Improper heading hierarchy',
        element: container.querySelector('h3'),
      });
    }

    return {
      score: issues.length === 0 ? 100 : Math.max(0, 100 - (issues.length * 10)),
      passed: issues.length === 0,
      issues,
      summary: {
        critical: issues.filter(i => i.severity === 'critical').length,
        serious: issues.filter(i => i.severity === 'serious').length,
        moderate: issues.filter(i => i.severity === 'moderate').length,
        minor: issues.filter(i => i.severity === 'minor').length,
      },
    };
  }),
}));

import { AccessibleForm, AccessibleField, AccessibleSubmitButton } from '@/components/ui/accessible-form';
import { ResponsiveContainer, ResponsiveGrid, ResponsiveStack } from '@/components/ui/responsive-layout';

// Import the mocked function
const { auditAccessibility } = require('../utils/accessibility-audit');

// Mock window.matchMedia for responsive tests
const mockMatchMedia = (query: string) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(mockMatchMedia),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

describe('Accessibility Improvements', () => {
  describe('AccessibleForm', () => {
    it('should render form with proper ARIA attributes', () => {
      render(
        <AccessibleForm aria-label="Test form">
          <AccessibleField
            name="email"
            label="Email Address"
            type="email"
            required
          />
        </AccessibleForm>
      );

      const form = screen.getByRole('form');
      expect(form).toHaveAttribute('aria-label', 'Test form');
      expect(form).toHaveAttribute('novalidate');
    });

    it('should associate labels with form fields', () => {
      render(
        <AccessibleForm>
          <AccessibleField
            name="email"
            label="Email Address"
            type="email"
            required
          />
        </AccessibleForm>
      );

      const input = screen.getByRole('textbox', { name: /email address/i });
      const label = screen.getByText('Email Address');
      
      expect(input).toBeInTheDocument();
      expect(label).toBeInTheDocument();
      expect(input).toHaveAttribute('aria-required', 'true');
    });

    it('should display validation errors with proper ARIA attributes', async () => {
      const user = userEvent.setup();
      
      render(
        <AccessibleForm>
          <AccessibleField
            name="email"
            label="Email Address"
            type="email"
            required
            error="Please enter a valid email address"
          />
        </AccessibleForm>
      );

      const input = screen.getByRole('textbox', { name: /email address/i });
      
      // Trigger validation by focusing and blurring
      await user.click(input);
      await user.tab();

      await waitFor(() => {
        const errorMessage = screen.getByRole('alert');
        expect(errorMessage).toBeInTheDocument();
        expect(errorMessage).toHaveTextContent('Please enter a valid email address');
        expect(input).toHaveAttribute('aria-invalid', 'true');
      });
    });

    it('should show required field indicators', () => {
      render(
        <AccessibleForm>
          <AccessibleField
            name="email"
            label="Email Address"
            type="email"
            required
          />
        </AccessibleForm>
      );

      const label = screen.getByText('Email Address');
      expect(label).toHaveClass('after:content-[\'*\']');
    });
  });

  describe('AccessibleSubmitButton', () => {
    it('should show loading state with proper announcements', () => {
      render(
        <AccessibleSubmitButton isLoading loadingText="Submitting form...">
          Submit
        </AccessibleSubmitButton>
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveTextContent('Submitting form...');
      
      const loadingStatus = screen.getByText('Form is being submitted, please wait');
      expect(loadingStatus).toHaveClass('sr-only');
    });

    it('should be keyboard accessible', async () => {
      const user = userEvent.setup();
      const handleClick = jest.fn();

      render(
        <AccessibleSubmitButton onClick={handleClick}>
          Submit
        </AccessibleSubmitButton>
      );

      const button = screen.getByRole('button');

      // Test that button is focusable and clickable
      await user.tab();
      expect(button).toHaveFocus();

      // Test button click functionality
      await user.click(button);
      expect(handleClick).toHaveBeenCalled();
    });
  });

  describe('Touch Target Compliance', () => {
    it('should meet minimum touch target size requirements', () => {
      render(
        <AccessibleSubmitButton>
          Submit
        </AccessibleSubmitButton>
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('min-h-[44px]');
    });
  });
});

describe('Responsive Design Improvements', () => {
  describe('ResponsiveContainer', () => {
    it('should render with responsive classes', () => {
      render(
        <ResponsiveContainer maxWidth="xl" padding="md">
          <div>Content</div>
        </ResponsiveContainer>
      );

      const container = screen.getByText('Content').parentElement;
      expect(container).toHaveClass('mx-auto', 'w-full', 'max-w-xl');
    });

    it('should apply different padding based on screen size', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <ResponsiveContainer padding="md">
          <div>Mobile Content</div>
        </ResponsiveContainer>
      );

      const container = screen.getByText('Mobile Content').parentElement;
      // Check for the actual classes applied by ResponsiveContainer
      expect(container).toHaveClass('mx-auto', 'w-full');
      expect(container).toHaveClass('px-8'); // Default padding
    });
  });

  describe('ResponsiveGrid', () => {
    it('should render with responsive grid classes', () => {
      render(
        <ResponsiveGrid cols={{ xs: 1, md: 2, lg: 3 }} gap="md">
          <div>Item 1</div>
          <div>Item 2</div>
          <div>Item 3</div>
        </ResponsiveGrid>
      );

      const grid = screen.getByText('Item 1').parentElement;
      expect(grid).toHaveClass('grid', 'gap-4');
    });
  });

  describe('ResponsiveStack', () => {
    it('should render with responsive flex classes', () => {
      render(
        <ResponsiveStack direction={{ xs: 'col', md: 'row' }} gap="lg">
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveStack>
      );

      const stack = screen.getByText('Item 1').parentElement;
      expect(stack).toHaveClass('flex', 'gap-6');
    });
  });
});

describe('Accessibility Audit Utility', () => {
  it('should detect missing alt text on images', () => {
    const container = document.createElement('div');
    container.innerHTML = '<img src="test.jpg" />';
    
    const result = auditAccessibility(container);
    
    expect(result.issues).toContainEqual(
      expect.objectContaining({
        rule: 'img-alt',
        severity: 'serious',
        message: 'Image missing alt text'
      })
    );
  });

  it('should detect form controls without labels', () => {
    const container = document.createElement('div');
    container.innerHTML = '<input type="text" />';
    
    const result = auditAccessibility(container);
    
    expect(result.issues).toContainEqual(
      expect.objectContaining({
        rule: 'form-label',
        severity: 'critical',
        message: 'Form control missing accessible label'
      })
    );
  });

  it('should detect improper heading hierarchy', () => {
    const container = document.createElement('div');
    container.innerHTML = '<h1>Title</h1><h3>Subtitle</h3>';
    
    const result = auditAccessibility(container);
    
    expect(result.issues).toContainEqual(
      expect.objectContaining({
        rule: 'heading-hierarchy',
        severity: 'moderate'
      })
    );
  });

  it('should calculate accessibility score', () => {
    const container = document.createElement('div');
    container.innerHTML = '<h1>Perfect Page</h1><p>No issues here!</p>';
    
    const result = auditAccessibility(container);
    
    expect(result.score).toBeGreaterThan(90);
    expect(result.passed).toBe(true);
  });
});

describe('CSS Utilities', () => {
  it('should include screen reader only classes', () => {
    render(<div className="sr-only">Screen reader only text</div>);
    
    const element = screen.getByText('Screen reader only text');
    expect(element).toHaveClass('sr-only');
  });

  it('should include responsive text classes', () => {
    render(<div className="text-responsive-lg">Responsive text</div>);
    
    const element = screen.getByText('Responsive text');
    expect(element).toHaveClass('text-responsive-lg');
  });

  it('should include touch target classes', () => {
    render(<button className="touch-target">Touch button</button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('touch-target');
  });
});

describe('Integration Tests', () => {
  it('should work together - accessible form with responsive layout', async () => {
    const user = userEvent.setup();
    
    render(
      <ResponsiveContainer maxWidth="md" padding="lg">
        <AccessibleForm aria-label="Contact form">
          <ResponsiveStack direction={{ xs: 'col', md: 'row' }} gap="md">
            <AccessibleField
              name="firstName"
              label="First Name"
              required
            />
            <AccessibleField
              name="lastName"
              label="Last Name"
              required
            />
          </ResponsiveStack>
          
          <AccessibleField
            name="email"
            label="Email Address"
            type="email"
            required
            description="We'll never share your email"
          />
          
          <AccessibleSubmitButton>
            Send Message
          </AccessibleSubmitButton>
        </AccessibleForm>
      </ResponsiveContainer>
    );

    // Test form accessibility
    const form = screen.getByRole('form');
    expect(form).toHaveAttribute('aria-label', 'Contact form');

    // Test field accessibility
    const firstNameInput = screen.getByRole('textbox', { name: /first name/i });
    const emailInput = screen.getByRole('textbox', { name: /email address/i });
    
    expect(firstNameInput).toHaveAttribute('aria-required', 'true');
    expect(emailInput).toHaveAttribute('aria-required', 'true');

    // Test keyboard navigation
    await user.tab();
    expect(firstNameInput).toHaveFocus();
    
    await user.tab();
    expect(screen.getByRole('textbox', { name: /last name/i })).toHaveFocus();

    // Test responsive layout classes
    const container = form.closest('[class*="max-w"]');
    expect(container).toHaveClass('max-w-md');
  });
});
