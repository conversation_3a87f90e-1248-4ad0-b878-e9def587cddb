/**
 * Gemini Service Skill Gap Tests
 * 
 * Tests Gemini Service Skill Gap functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock environment variables before importing geminiService
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing-purposes-only';

import { geminiService } from '@/lib/services/geminiService';

// Mock the generateContent method
const mockGenerateContent = jest.fn();

// Mock the GeminiService class
jest.mock('@/lib/services/geminiService', () => {
  const actualGeminiService = jest.requireActual('@/lib/services/geminiService');
  return {
    ...actualGeminiService,
    geminiService: {
      ...actualGeminiService.geminiService,
      generateContent: mockGenerateContent,
      analyzeComprehensiveSkillGap: jest.fn(),
      generatePersonalizedLearningPlan: jest.fn(),
      analyzeSkillMarketTrends: jest.fn(),
      validateSkillAssessment: jest.fn(),
    },
  };
});

const mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;

describe('GeminiService - Skill Gap Analysis', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('analyzeComprehensiveSkillGap', () => {
    const mockCurrentSkills = [
      {
        skillName: 'JavaScript',
        selfRating: 7,
        confidenceLevel: 8,
        yearsOfExperience: 3,
      },
      {
        skillName: 'React',
        selfRating: 6,
        confidenceLevel: 7,
        yearsOfExperience: 2,
      },
    ];

    const mockTargetCareerPath = {
      careerPathName: 'Full Stack Developer',
      targetLevel: 'ADVANCED' as const,
    };

    const mockPreferences = {
      timeframe: 'ONE_YEAR' as const,
      hoursPerWeek: 10,
      learningStyle: ['VISUAL', 'HANDS_ON'],
      budget: 'FREEMIUM' as const,
      focusAreas: ['Backend Development'],
    };

    const mockCareerPathData = {
      requiredSkills: [
        { name: 'JavaScript' },
        { name: 'Node.js' },
        { name: 'PostgreSQL' },
      ],
      learningResources: [
        {
          title: 'Node.js Fundamentals',
          type: 'COURSE',
          skillLevel: 'INTERMEDIATE',
          skills: ['Node.js'],
        },
      ],
    };

    it('should analyze comprehensive skill gap successfully', async () => {
      // Arrange
      const expectedResponse = {
        success: true,
        data: {
          skillGaps: [
            {
              skillId: 'generated-id-1',
              skillName: 'Node.js',
              currentLevel: 2,
              targetLevel: 8,
              gapSeverity: 'HIGH',
              priority: 90,
              estimatedLearningTime: 120,
              marketDemand: 'VERY_HIGH',
              salaryImpact: 15,
            },
          ],
          learningPlan: {
            totalEstimatedHours: 120,
            milestones: [
              {
                month: 3,
                skills: ['Node.js Basics'],
                estimatedHours: 60,
                learningPaths: ['Backend Development Path'],
              },
            ],
            recommendedResources: [
              {
                resourceId: 'resource-1',
                resourceType: 'COURSE',
                priority: 'HIGH',
                skillsAddressed: ['Node.js'],
                estimatedHours: 60,
              },
            ],
          },
          careerReadiness: {
            currentScore: 65,
            targetScore: 85,
            improvementPotential: 20,
            timeToTarget: 8,
          },
        },
      };

      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockGeminiService.analyzeComprehensiveSkillGap(
        mockCurrentSkills,
        mockTargetCareerPath,
        mockPreferences,
        mockCareerPathData,
        'test-user-id'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.skillGaps).toHaveLength(1);
      expect(result.data.skillGaps[0].skillName).toBe('Node.js');
      expect(result.data.skillGaps[0].gapSeverity).toBe('HIGH');
      expect(result.data.learningPlan.totalEstimatedHours).toBe(120);
      expect(result.data.careerReadiness.currentScore).toBe(65);

      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(
        mockCurrentSkills,
        mockTargetCareerPath,
        mockPreferences,
        mockCareerPathData,
        'test-user-id'
      );
    });

    it('should handle AI service failures', async () => {
      // Arrange
      const errorResponse = {
        success: false,
        error: 'AI service temporarily unavailable',
      };

      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(errorResponse);

      // Act
      const result = await mockGeminiService.analyzeComprehensiveSkillGap(
        mockCurrentSkills,
        mockTargetCareerPath,
        mockPreferences,
        mockCareerPathData,
        'test-user-id'
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('AI service temporarily unavailable');
    });

    it('should include market insights when requested', async () => {
      // Arrange
      const responseWithMarketData = {
        success: true,
        data: {
          skillGaps: [],
          learningPlan: {
            totalEstimatedHours: 0,
            milestones: [],
            recommendedResources: [],
          },
          careerReadiness: {
            currentScore: 70,
            targetScore: 85,
            improvementPotential: 15,
            timeToTarget: 6,
          },
          marketInsights: {
            industryTrends: [
              {
                skill: 'Node.js',
                trend: 'GROWING',
                demandLevel: 'VERY_HIGH',
              },
            ],
            salaryProjections: {
              currentEstimate: 75000,
              targetEstimate: 95000,
              improvementPotential: 26.7,
            },
          },
        },
      };

      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(responseWithMarketData);

      // Act
      const result = await mockGeminiService.analyzeComprehensiveSkillGap(
        mockCurrentSkills,
        mockTargetCareerPath,
        mockPreferences,
        mockCareerPathData,
        'test-user-id'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.marketInsights).toBeDefined();
      expect(result.data.marketInsights.industryTrends).toHaveLength(1);
      expect(result.data.marketInsights.salaryProjections.improvementPotential).toBe(26.7);
    });
  });

  describe('generatePersonalizedLearningPlan', () => {
    const mockSkillGaps = [
      {
        skillName: 'Node.js',
        currentLevel: 3,
        targetLevel: 8,
        gapSeverity: 'HIGH',
        priority: 90,
        estimatedLearningTime: 120,
      },
    ];

    const mockUserPreferences = {
      timeframe: 'ONE_YEAR' as const,
      hoursPerWeek: 10,
      learningStyle: ['VISUAL'],
      budget: 'FREEMIUM' as const,
      focusAreas: ['Backend Development'],
    };

    const mockMarketData = [
      {
        skillName: 'Node.js',
        demandLevel: 'VERY_HIGH',
        growthTrend: 'GROWING',
        salaryImpact: 15,
      },
    ];

    it('should generate personalized learning plan successfully', async () => {
      // Arrange
      const expectedResponse = {
        success: true,
        data: {
          learningPlan: {
            totalDuration: '12 months',
            totalHours: 120,
            weeklyCommitment: 10,
            phases: [
              {
                phaseNumber: 1,
                phaseName: 'Foundation',
                duration: '3 months',
                objectives: ['Learn Node.js fundamentals'],
                skills: [
                  {
                    skillName: 'Node.js',
                    currentLevel: 3,
                    targetLevel: 6,
                    hoursAllocated: 60,
                    learningApproach: 'Hands-on projects with video tutorials',
                    resources: [
                      {
                        type: 'COURSE',
                        title: 'Node.js Complete Guide',
                        description: 'Comprehensive Node.js course',
                        estimatedHours: 40,
                        cost: 'FREEMIUM',
                        difficulty: 'INTERMEDIATE',
                        learningStyle: ['VISUAL'],
                        priority: 'HIGH',
                      },
                    ],
                    milestones: [
                      {
                        week: 4,
                        milestone: 'Build first Node.js API',
                        assessmentMethod: 'Project completion',
                      },
                    ],
                  },
                ],
                practiceProjects: [
                  {
                    projectName: 'REST API with Node.js',
                    description: 'Build a complete REST API',
                    skillsApplied: ['Node.js', 'Express.js'],
                    estimatedHours: 20,
                    difficulty: 'INTERMEDIATE',
                    deliverables: ['Working API', 'Documentation'],
                  },
                ],
              },
            ],
          },
        },
      };

      mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockGeminiService.generatePersonalizedLearningPlan(
        mockSkillGaps,
        mockUserPreferences,
        mockMarketData,
        'test-user-id'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.learningPlan.totalHours).toBe(120);
      expect(result.data.learningPlan.phases).toHaveLength(1);
      expect(result.data.learningPlan.phases[0].skills[0].skillName).toBe('Node.js');
      expect(result.data.learningPlan.phases[0].practiceProjects).toHaveLength(1);
    });

    it('should optimize for user preferences', async () => {
      // Arrange
      const visualLearnerResponse = {
        success: true,
        data: {
          learningPlan: {
            phases: [
              {
                skills: [
                  {
                    skillName: 'Node.js',
                    resources: [
                      {
                        type: 'COURSE',
                        learningStyle: ['VISUAL'],
                        priority: 'HIGH',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
      };

      mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(visualLearnerResponse);

      // Act
      const result = await mockGeminiService.generatePersonalizedLearningPlan(
        mockSkillGaps,
        mockUserPreferences,
        mockMarketData,
        'test-user-id'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.learningPlan.phases[0].skills[0].resources[0].learningStyle).toContain('VISUAL');
    });
  });

  describe('analyzeSkillMarketTrends', () => {
    const mockSkills = ['JavaScript', 'React', 'Node.js'];
    const mockTargetIndustry = 'Technology';
    const mockRegion = 'North America';

    it('should analyze skill market trends successfully', async () => {
      // Arrange
      const expectedResponse = {
        success: true,
        data: {
          marketAnalysis: {
            analysisDate: expect.any(String),
            region: 'North America',
            industry: 'Technology',
            overallMarketHealth: 'EXCELLENT',
          },
          skillTrends: [
            {
              skillName: 'JavaScript',
              demandLevel: 'VERY_HIGH',
              growthTrend: 'STABLE',
              marketSaturation: 'MODERATE',
              averageSalaryImpact: 20,
              jobPostingsGrowth: 5,
              futureOutlook: {
                nextYear: 'Continued high demand',
                nextFiveYears: 'Evolving with new frameworks',
                emergingOpportunities: ['Full-stack development'],
                potentialThreats: ['Framework fragmentation'],
              },
            },
          ],
        },
      };

      mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockGeminiService.analyzeSkillMarketTrends(
        mockSkills,
        mockTargetIndustry,
        mockRegion
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.marketAnalysis.region).toBe('North America');
      expect(result.data.marketAnalysis.industry).toBe('Technology');
      expect(result.data.skillTrends).toHaveLength(1);
      expect(result.data.skillTrends[0].skillName).toBe('JavaScript');
      expect(result.data.skillTrends[0].demandLevel).toBe('VERY_HIGH');
    });

    it('should handle empty skills array', async () => {
      // Arrange
      const emptySkillsResponse = {
        success: true,
        data: {
          marketAnalysis: {
            region: 'GLOBAL',
            industry: 'Technology',
            overallMarketHealth: 'GOOD',
          },
          skillTrends: [],
        },
      };

      mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(emptySkillsResponse);

      // Act
      const result = await mockGeminiService.analyzeSkillMarketTrends(
        [],
        mockTargetIndustry,
        'GLOBAL'
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.skillTrends).toHaveLength(0);
    });
  });

  describe('validateSkillAssessment', () => {
    const mockSkillName = 'JavaScript';
    const mockSelfRating = 7;
    const mockUserContext = {
      experienceLevel: 'INTERMEDIATE',
      industry: 'Technology',
      yearsOfExperience: 3,
      relatedSkills: ['React', 'Node.js'],
      previousAssessments: [
        {
          skillName: 'React',
          rating: 6,
          date: '2024-01-01',
        },
      ],
    };

    it('should validate skill assessment successfully', async () => {
      // Arrange
      const expectedResponse = {
        success: true,
        data: {
          validationResult: {
            assessmentAccuracy: 'ACCURATE',
            confidenceLevel: 85,
            reasoning: 'Rating aligns well with experience level and related skills',
            suggestedRating: 7,
            ratingJustification: 'Consistent with 3 years experience and related skill levels',
          },
          skillAnalysis: {
            skillComplexity: 'INTERMEDIATE',
            learningCurve: 'MODERATE',
            marketValue: 'VERY_HIGH',
            industryRelevance: 'CRITICAL',
            skillCategory: 'TECHNICAL',
          },
          assessmentGuidance: {
            ratingCriteria: {
              'level1to2': 'Basic syntax understanding',
              'level3to4': 'Can write simple programs',
              'level5to6': 'Comfortable with frameworks',
              'level7to8': 'Advanced patterns and optimization',
              'level9to10': 'Expert-level architecture and mentoring',
            },
            selfAssessmentTips: [
              'Compare with concrete examples',
              'Consider real-world project complexity',
            ],
            validationMethods: [
              {
                method: 'Code Review',
                description: 'Have experienced developer review your code',
                timeRequired: '2-3 hours',
                cost: 'FREE',
              },
            ],
          },
        },
      };

      mockGeminiService.validateSkillAssessment.mockResolvedValue(expectedResponse);

      // Act
      const result = await mockGeminiService.validateSkillAssessment(
        mockSkillName,
        mockSelfRating,
        mockUserContext
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.validationResult.assessmentAccuracy).toBe('ACCURATE');
      expect(result.data.validationResult.suggestedRating).toBe(7);
      expect(result.data.skillAnalysis.skillComplexity).toBe('INTERMEDIATE');
      expect(result.data.assessmentGuidance.validationMethods).toHaveLength(1);
    });

    it('should identify overestimated ratings', async () => {
      // Arrange
      const overestimatedResponse = {
        success: true,
        data: {
          validationResult: {
            assessmentAccuracy: 'SIGNIFICANTLY_HIGH',
            confidenceLevel: 60,
            reasoning: 'Rating seems high for stated experience level',
            suggestedRating: 5,
            ratingJustification: 'More realistic for 3 years experience',
          },
        },
      };

      mockGeminiService.validateSkillAssessment.mockResolvedValue(overestimatedResponse);

      // Act
      const result = await mockGeminiService.validateSkillAssessment(
        mockSkillName,
        9, // High rating
        { ...mockUserContext, yearsOfExperience: 1 } // Low experience
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.validationResult.assessmentAccuracy).toBe('SIGNIFICANTLY_HIGH');
      expect(result.data.validationResult.suggestedRating).toBe(5);
    });
  });
});
