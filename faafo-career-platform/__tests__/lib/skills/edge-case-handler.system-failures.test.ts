/**
 * EdgeCaseHandler System Failures Tests
 * Focused on system failure scenarios and error handling
 * Optimized for fast execution
 */

import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

// Shared mock factories for performance
const createMockServices = () => ({
  mockAssessmentEngine: {
    createAssessment: jest.fn(),
    generateQuestions: jest.fn(),
    submitResponse: jest.fn(),
    calculateResults: jest.fn(),
    getAssessment: jest.fn(),
    getAssessmentsByUser: jest.fn(),
  } as any,
  mockMarketDataService: {
    getSkillMarketData: jest.fn(),
    getMultipleSkillsMarketData: jest.fn(),
    analyzeMarketTrends: jest.fn(),
    getSalaryInsights: jest.fn(),
    getLocationBasedMarketData: jest.fn(),
    getMarketBasedRecommendations: jest.fn(),
  } as any,
  mockLearningPathService: {
    generateLearningPath: jest.fn(),
    updateProgress: jest.fn(),
    completeMilestone: jest.fn(),
  } as any,
});

describe('EdgeCaseHandler - System Failures', () => {
  let edgeCaseHandler: EdgeCaseHandler;
  let mocks: ReturnType<typeof createMockServices>;

  beforeEach(() => {
    mocks = createMockServices();
    edgeCaseHandler = new EdgeCaseHandler(
      mocks.mockAssessmentEngine,
      mocks.mockMarketDataService,
      mocks.mockLearningPathService
    );
  });

  describe('Database Failures', () => {
    it('should handle database connection failures', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
      expect(result.errorType).toBe('SYSTEM_ERROR');
      expect(result.retryable).toBe(true);
      expect(result.fallbackData).toBeDefined();
    }, 5000);

    it('should handle database timeout errors', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Query timeout');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Query timeout');
      expect(result.errorType).toBe('TIMEOUT_ERROR');
      expect(result.retryable).toBe(true);
    }, 5000);
  });

  describe('AI Service Failures', () => {
    it('should handle AI service timeouts', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {
        throw new Error('AI service timeout');
      });

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('AI service timeout');
      expect(result.errorType).toBe('TIMEOUT_ERROR');
      expect(result.retryable).toBe(true);
      expect(result.fallbackData).toBeDefined();
    }, 5000);

    it('should handle AI service rate limiting', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {
        throw new Error('Rate limit exceeded');
      });

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
      expect(result.errorType).toBe('AI_SERVICE_ERROR');
      expect(result.retryAfter).toBeDefined();
      expect(result.fallbackData).toBeDefined();
    }, 5000);
  });

  describe('Memory and Performance Limits', () => {
    it('should handle memory exhaustion gracefully', async () => {
      const request = {
        userId: 'user-123',
        skillIds: Array(1000).fill('skill'), // Reduced from 100000 for performance
        careerPathId: 'path-456',
      };

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Values exceed maximum thresholds');
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.suggestedOptimizations).toBeDefined();
    }, 5000);

    it('should handle processing timeouts', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: Array(100).fill({ skill: 'javascript', level: 5, confidence: 6 }), // Reduced from 1000
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      // Mock a timeout operation with shorter delay for testing
      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => resolve({} as any), 6000); // 6 second delay
        });
      });

      const result = await edgeCaseHandler.handleLearningPathGeneration(request, { timeout: 2000 });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Processing timeout');
      expect(result.errorType).toBe('TIMEOUT_ERROR');
      expect(result.partialResults).toBeDefined();
    }, 8000);
  });

  describe('Monitoring and Alerting', () => {
    it('should log security incidents', async () => {
      const maliciousRequest = {
        userId: "'; DROP TABLE users; --",
        skillIds: ["'; DELETE FROM skills; --"],
        careerPathId: "1' OR '1'='1",
      };

      const logSpy = jest.spyOn(console, 'error').mockImplementation();

      await edgeCaseHandler.handleSkillAssessment(maliciousRequest);

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('SECURITY_ALERT'),
        expect.objectContaining({
          type: 'SQL_INJECTION_ATTEMPT',
          userId: maliciousRequest.userId,
          timestamp: expect.any(Date),
        })
      );

      logSpy.mockRestore();
    }, 3000);

    it('should track error patterns', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      // Generate multiple errors with reduced iterations for performance
      for (let i = 0; i < 3; i++) {
        await edgeCaseHandler.handleSkillAssessment(request);
      }

      const errorStats = await edgeCaseHandler.getErrorStatistics();

      expect(errorStats.totalErrors).toBeGreaterThanOrEqual(3);
      expect(errorStats.errorsByType.SYSTEM_ERROR).toBeGreaterThanOrEqual(3);
      expect(errorStats.mostCommonError).toBe('Database connection failed');
    }, 8000);
  });

  describe('Performance Optimization Tests', () => {
    it('should complete validation within performance threshold', async () => {
      const startTime = Date.now();
      
      const request = {
        userId: 'user-123',
        skillIds: ['javascript', 'react', 'node'],
        careerPathId: 'path-456',
      };

      await edgeCaseHandler.handleSkillAssessment(request);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(3000); // Should complete within 3 seconds
    }, 5000);

    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      // Reduced concurrent requests for performance
      const promises = Array(3).fill(null).map((_, index) => 
        edgeCaseHandler.handleSkillAssessment({
          ...request,
          userId: `user-${index}`
        })
      );

      await Promise.all(promises);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    }, 8000);

    it('should maintain memory efficiency during processing', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      // Process multiple requests
      for (let i = 0; i < 5; i++) {
        await edgeCaseHandler.handleSkillAssessment({
          ...request,
          userId: `user-${i}`
        });
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    }, 10000);
  });
});
