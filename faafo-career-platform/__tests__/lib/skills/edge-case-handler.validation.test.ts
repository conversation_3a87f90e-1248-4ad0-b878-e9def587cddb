/**
 * EdgeCaseHandler Validation Tests
 * Focused on input validation and security edge cases
 * Optimized for fast execution
 */

import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

// Shared mock factories for performance
const createMockServices = () => ({
  mockAssessmentEngine: {
    createAssessment: jest.fn(),
    generateQuestions: jest.fn(),
    submitResponse: jest.fn(),
    calculateResults: jest.fn(),
    getAssessment: jest.fn(),
    getAssessmentsByUser: jest.fn(),
  } as any,
  mockMarketDataService: {
    getSkillMarketData: jest.fn(),
    getMultipleSkillsMarketData: jest.fn(),
    analyzeMarketTrends: jest.fn(),
    getSalaryInsights: jest.fn(),
    getLocationBasedMarketData: jest.fn(),
    getMarketBasedRecommendations: jest.fn(),
  } as any,
  mockLearningPathService: {
    generateLearningPath: jest.fn(),
    updateProgress: jest.fn(),
    completeMilestone: jest.fn(),
  } as any,
});

describe('EdgeCaseHandler - Input Validation', () => {
  let edgeCaseHandler: EdgeCaseHandler;
  let mocks: ReturnType<typeof createMockServices>;

  beforeEach(() => {
    mocks = createMockServices();
    edgeCaseHandler = new EdgeCaseHandler(
      mocks.mockAssessmentEngine,
      mocks.mockMarketDataService,
      mocks.mockLearningPathService
    );
  });

  describe('Null and Undefined Inputs', () => {
    it('should handle null skill assessment request', async () => {
      const result = await edgeCaseHandler.handleSkillAssessment(null as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input');
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.fallbackData).toBeDefined();
    }, 5000); // 5 second timeout

    it('should handle undefined learning path request', async () => {
      const result = await edgeCaseHandler.handleLearningPathGeneration(undefined as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input');
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.fallbackData).toBeDefined();
    }, 5000);

    it('should handle null market data request', async () => {
      const result = await edgeCaseHandler.handleMarketDataRequest(null as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input');
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.fallbackData).toBeDefined();
    }, 5000);
  });

  describe('Empty and Invalid Data Types', () => {
    it('should handle empty string inputs', async () => {
      const request = {
        userId: '',
        skillIds: [],
        careerPathId: '',
      };

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Empty required fields');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 3000);

    it('should handle invalid data types', async () => {
      const request = {
        userId: 123, // Should be string
        skillIds: 'not-an-array', // Should be array
        careerPathId: true, // Should be string
      };

      const result = await edgeCaseHandler.handleSkillAssessment(request as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid data types');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 3000);

    it('should handle extremely large inputs', async () => {
      const largeString = 'a'.repeat(10000); // Reduced from 1MB for performance
      const request = {
        userId: largeString,
        skillIds: Array(100).fill('skill-id'), // Reduced from 10000
        careerPathId: largeString,
      };

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Values exceed maximum thresholds');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 5000);
  });

  describe('Security Edge Cases', () => {
    it('should prevent SQL injection attempts', async () => {
      const maliciousRequest = {
        userId: "'; DROP TABLE users; --",
        skillIds: ["'; DELETE FROM skills; --"],
        careerPathId: "1' OR '1'='1",
      };

      const result = await edgeCaseHandler.handleSkillAssessment(maliciousRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Potentially malicious input detected');
      expect(result.errorType).toBe('SECURITY_ERROR');
      expect(result.securityAlert).toBe(true);
    }, 3000);

    it('should prevent XSS attempts', async () => {
      const xssRequest = {
        userId: '<script>alert("xss")</script>',
        skillIds: ['<img src=x onerror=alert(1)>'],
        careerPathId: 'javascript:alert(1)',
      };

      const result = await edgeCaseHandler.handleSkillAssessment(xssRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Potentially malicious input detected');
      expect(result.errorType).toBe('SECURITY_ERROR');
      expect(result.securityAlert).toBe(true);
    }, 3000);

    it('should handle malformed JSON gracefully', async () => {
      const malformedJson = '{"userId": "test", "skillIds": [}';

      const result = await edgeCaseHandler.parseAndValidateInput(malformedJson);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid JSON format');
      expect(result.errorType).toBe('PARSING_ERROR');
    }, 2000);
  });

  describe('Boundary Value Testing', () => {
    it('should handle minimum boundary values', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
        timeframe: 0,
        budget: 0,
      };

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Values below minimum thresholds');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 3000);

    it('should handle maximum boundary values', async () => {
      const request = {
        userId: 'u'.repeat(256),
        skillIds: Array(101).fill('skill'), // Reduced from 1001
        careerPathId: 'c'.repeat(256),
        timeframe: 1000,
        budget: 1000001,
      };

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Values exceed maximum thresholds');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 5000);

    it('should handle negative values', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
        timeframe: -5,
        budget: -100,
        availability: -10,
      };

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Negative values not allowed');
      expect(result.errorType).toBe('VALIDATION_ERROR');
    }, 3000);
  });

  describe('Unicode and Special Characters', () => {
    it('should handle Unicode characters properly', async () => {
      const request = {
        userId: '用户123',
        skillIds: ['JavaScript™', 'React®'],
        careerPathId: 'Développeur Full-Stack',
      };

      mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({
        id: 'assessment-123',
        userId: request.userId,
      } as any);

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.sanitizedInput).toBeDefined();
    }, 3000);

    it('should handle emoji and special Unicode', async () => {
      const request = {
        userId: 'user🚀123',
        skillIds: ['JavaScript💻', 'React⚛️'],
        careerPathId: 'Full-Stack Developer🌟',
      };

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(true);
      expect(result.sanitizedInput.userId).not.toContain('🚀');
      expect(result.sanitizedInput.skillIds[0]).not.toContain('💻');
    }, 3000);
  });
});
