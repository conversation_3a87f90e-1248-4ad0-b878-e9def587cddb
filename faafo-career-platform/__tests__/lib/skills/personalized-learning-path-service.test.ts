/**
 * Personalized Learning Path Service Tests
 * 
 * Tests Personalized Learning Path Service functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

describe('PersonalizedLearningPathService', () => {
  let service: PersonalizedLearningPathService;

  beforeEach(() => {
    service = new PersonalizedLearningPathService();
  });

  describe('Learning Path Generation', () => {
    it('should generate a personalized learning path', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [
          { skill: 'javascript', level: 7, confidence: 8 },
          { skill: 'html', level: 9, confidence: 9 },
          { skill: 'css', level: 8, confidence: 7 },
        ],
        targetRole: 'Full Stack Developer',
        timeframe: 6, // months
        learningStyle: 'hands-on' as const,
        availability: 10, // hours per week
        budget: 500, // dollars
        preferences: {
          preferredFormats: ['video', 'interactive'],
          difficulty: 'intermediate',
          certificationRequired: true,
        },
      };

      const learningPath = await service.generateLearningPath(request);

      expect(learningPath).toBeDefined();
      expect(learningPath.id).toBeDefined();
      expect(learningPath.userId).toBe('user-123');
      expect(learningPath.targetRole).toBe('Full Stack Developer');
      expect(learningPath.estimatedDuration).toBeDefined();
      expect(learningPath.totalCost).toBeLessThanOrEqual(500);
      expect(learningPath.phases).toBeDefined();
      expect(learningPath.phases.length).toBeGreaterThan(0);
      expect(learningPath.skillGaps).toBeDefined();
      expect(learningPath.recommendations).toBeDefined();
    });

    it('should identify skill gaps for target role', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 5, confidence: 6 }],
        targetRole: 'React Developer',
        timeframe: 3,
        learningStyle: 'visual' as const,
        availability: 15,
        budget: 1000,
      };

      const learningPath = await service.generateLearningPath(request);

      expect(learningPath.skillGaps).toBeDefined();
      expect(learningPath.skillGaps.length).toBeGreaterThan(0);
      
      const reactGap = learningPath.skillGaps.find(gap => gap.skill === 'react');
      expect(reactGap).toBeDefined();
      expect(reactGap?.currentLevel).toBe(0);
      expect(reactGap?.targetLevel).toBeGreaterThan(0);
      expect(reactGap?.priority).toBeDefined();
    });

    it('should create learning phases in logical order', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],
        targetRole: 'Frontend Developer',
        timeframe: 12,
        learningStyle: 'structured' as const,
        availability: 8,
        budget: 2000,
      };

      const learningPath = await service.generateLearningPath(request);

      expect(learningPath.phases.length).toBeGreaterThan(1);
      
      // Check that phases have proper dependencies
      for (let i = 1; i < learningPath.phases.length; i++) {
        const currentPhase = learningPath.phases[i];
        const previousPhase = learningPath.phases[i - 1];
        
        expect(currentPhase.startDate.getTime()).toBeGreaterThanOrEqual(
          previousPhase.endDate.getTime()
        );
      }
    });

    it('should respect budget constraints', async () => {
      const lowBudgetRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'self-paced' as const,
        availability: 10,
        budget: 100, // Low budget
      };

      const learningPath = await service.generateLearningPath(lowBudgetRequest);

      expect(learningPath.totalCost).toBeLessThanOrEqual(100);
      expect(learningPath.resources.some(r => r.cost === 0)).toBe(true); // Should include free resources
    });

    it('should adapt to learning style preferences', async () => {
      const visualLearnerRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
        targetRole: 'Frontend Developer',
        timeframe: 4,
        learningStyle: 'visual' as const,
        availability: 12,
        budget: 500,
      };

      const learningPath = await service.generateLearningPath(visualLearnerRequest);

      const visualResources = learningPath.resources.filter(r => 
        r.format === 'video' || r.format === 'interactive'
      );
      
      expect(visualResources.length).toBeGreaterThan(0);
    });
  });

  describe('Learning Path Optimization', () => {
    it('should optimize learning path for time constraints', async () => {
      const timeConstrainedRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 7, confidence: 8 }],
        targetRole: 'React Developer',
        timeframe: 2, // Very short timeframe
        learningStyle: 'intensive' as const,
        availability: 20, // High availability
        budget: 1000,
      };

      const learningPath = await service.generateLearningPath(timeConstrainedRequest);

      expect(learningPath.estimatedDuration).toBeLessThanOrEqual(2 * 4); // 2 months in weeks
      expect(learningPath.phases.every(phase => phase.intensity === 'high')).toBe(true);
    });

    it('should suggest alternative paths when constraints are unrealistic', async () => {
      const unrealisticRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 2, confidence: 3 }],
        targetRole: 'Senior Full Stack Developer',
        timeframe: 1, // Unrealistic timeframe
        learningStyle: 'casual' as const,
        availability: 2, // Very low availability
        budget: 50, // Very low budget
      };

      const learningPath = await service.generateLearningPath(unrealisticRequest);

      expect(learningPath.alternatives).toBeDefined();
      expect(learningPath.alternatives.length).toBeGreaterThan(0);
      expect(learningPath.feasibilityScore).toBeLessThan(0.7); // Low feasibility
    });

    it('should prioritize skills based on market demand and role requirements', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
        targetRole: 'Full Stack Developer',
        timeframe: 8,
        learningStyle: 'balanced' as const,
        availability: 12,
        budget: 800,
      };

      const learningPath = await service.generateLearningPath(request);

      const highPrioritySkills = learningPath.skillGaps.filter(gap => gap.priority === 'high' || gap.priority === 'critical');
      expect(highPrioritySkills.length).toBeGreaterThan(0);
      
      // High priority skills should appear in earlier phases
      const firstPhaseSkills = learningPath.phases[0].skills;
      const hasHighPriorityInFirstPhase = firstPhaseSkills.some(skill => 
        highPrioritySkills.some(gap => gap.skill === skill.name)
      );
      expect(hasHighPriorityInFirstPhase).toBe(true);
    });
  });

  describe('Progress Tracking Integration', () => {
    it.skip('should track learning progress and update path accordingly', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'React Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 600,
      });

      // Debug: Check what resources are available
      console.log('Learning path resources:', learningPath.resources.length);
      console.log('Resource IDs:', learningPath.resources.map(r => r.id));

      // Ensure we have at least one resource to complete
      expect(learningPath.resources.length).toBeGreaterThan(0);

      const progressUpdate = {
        userId: 'user-123',
        pathId: learningPath.id,
        completedResources: [learningPath.resources[0].id], // Complete at least one resource
        skillUpdates: [
          { skill: 'react', newLevel: 4, confidence: 6 },
        ],
        timeSpent: 20, // hours
      };

      const updatedPath = await service.updateProgress(progressUpdate);

      // Debug: Check progress after update
      console.log('Completed resources:', updatedPath.progress.completedResources);
      console.log('Total resources:', updatedPath.resources.length);
      console.log('Completion percentage:', updatedPath.progress.completionPercentage);

      expect(updatedPath).toBeDefined();
      expect(updatedPath.progress.completionPercentage).toBeGreaterThan(0);
      expect(updatedPath.progress.timeSpent).toBe(20);
      expect(updatedPath.estimatedTimeRemaining).toBeLessThan(learningPath.estimatedDuration);
    });

    it('should suggest path adjustments based on progress', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
        targetRole: 'Frontend Developer',
        timeframe: 4,
        learningStyle: 'adaptive' as const,
        availability: 15,
        budget: 500,
      });

      // Simulate faster than expected progress
      const progressUpdate = {
        userId: 'user-123',
        pathId: learningPath.id,
        completedResources: learningPath.resources.slice(0, 3).map(r => r.id),
        skillUpdates: [
          { skill: 'react', newLevel: 7, confidence: 8 },
        ],
        timeSpent: 15, // Less time than estimated
      };

      const updatedPath = await service.updateProgress(progressUpdate);

      expect(updatedPath.adjustments).toBeDefined();
      expect(updatedPath.adjustments.length).toBeGreaterThan(0);
      expect(updatedPath.adjustments.some(adj => adj.type === 'accelerate')).toBe(true);
    });
  });

  describe('Resource Recommendation', () => {
    it('should recommend diverse learning resources', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 8,
        learningStyle: 'mixed' as const,
        availability: 12,
        budget: 1000,
      });

      const resourceTypes = new Set(learningPath.resources.map(r => r.format));
      expect(resourceTypes.size).toBeGreaterThan(2); // Should have multiple formats

      const costLevels = new Set(learningPath.resources.map(r => r.cost > 0 ? 'paid' : 'free'));
      expect(costLevels.has('free')).toBe(true); // Should include free resources
    });

    it('should match resources to learning preferences', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 6, confidence: 7 }],
        targetRole: 'Frontend Developer',
        timeframe: 6,
        learningStyle: 'hands-on' as const,
        availability: 10,
        budget: 500,
        preferences: {
          preferredFormats: ['interactive', 'project'],
          difficulty: 'intermediate',
          certificationRequired: true,
        },
      };

      const learningPath = await service.generateLearningPath(request);

      const interactiveResources = learningPath.resources.filter(r => 
        r.format === 'interactive' || r.format === 'project'
      );
      expect(interactiveResources.length).toBeGreaterThan(0);

      const certificationResources = learningPath.resources.filter(r => r.providesCertification);
      expect(certificationResources.length).toBeGreaterThan(0);
    });
  });

  describe('Milestone and Achievement System', () => {
    it('should define clear milestones for learning path', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 4, confidence: 5 }],
        targetRole: 'React Developer',
        timeframe: 5,
        learningStyle: 'goal-oriented' as const,
        availability: 12,
        budget: 600,
      });

      expect(learningPath.milestones).toBeDefined();
      expect(learningPath.milestones.length).toBeGreaterThan(0);
      
      learningPath.milestones.forEach(milestone => {
        expect(milestone.id).toBeDefined();
        expect(milestone.title).toBeDefined();
        expect(milestone.description).toBeDefined();
        expect(milestone.targetDate).toBeInstanceOf(Date);
        expect(milestone.criteria).toBeDefined();
        expect(milestone.criteria.length).toBeGreaterThan(0);
      });
    });

    it('should track milestone completion', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Frontend Developer',
        timeframe: 4,
        learningStyle: 'structured' as const,
        availability: 15,
        budget: 500,
      });

      const milestoneCompletion = {
        userId: 'user-123',
        pathId: learningPath.id,
        milestoneId: learningPath.milestones[0].id,
        completedCriteria: learningPath.milestones[0].criteria.slice(0, 2),
        evidence: ['completed-project-url', 'assessment-score-85'],
      };

      const result = await service.completeMilestone(milestoneCompletion);

      expect(result.success).toBe(true);
      expect(result.milestone.completed).toBe(true);
      expect(result.milestone.completedDate).toBeInstanceOf(Date);
      expect(result.achievements).toBeDefined();
    });
  });

  describe('Adaptive Learning', () => {
    it('should adapt path based on learning velocity', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'adaptive' as const,
        availability: 12,
        budget: 800,
      });

      // Simulate slow progress
      const slowProgressUpdate = {
        userId: 'user-123',
        pathId: learningPath.id,
        completedResources: [learningPath.resources[0].id],
        skillUpdates: [
          { skill: 'react', newLevel: 2, confidence: 4 }, // Lower than expected
        ],
        timeSpent: 40, // More time than estimated
      };

      const adaptedPath = await service.updateProgress(slowProgressUpdate);

      expect(adaptedPath.adjustments).toBeDefined();
      expect(adaptedPath.adjustments.some(adj => adj.type === 'simplify')).toBe(true);
      expect(adaptedPath.estimatedTimeRemaining).toBeGreaterThan(learningPath.estimatedDuration);
    });

    it('should suggest additional resources when struggling', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 4, confidence: 5 }],
        targetRole: 'Frontend Developer',
        timeframe: 8,
        learningStyle: 'supportive' as const,
        availability: 10,
        budget: 600,
      });

      const strugglingUpdate = {
        userId: 'user-123',
        pathId: learningPath.id,
        completedResources: [],
        skillUpdates: [
          { skill: 'css', newLevel: 3, confidence: 2 }, // Low confidence
        ],
        timeSpent: 30,
        difficulties: ['css-flexbox', 'css-grid'],
      };

      const supportedPath = await service.updateProgress(strugglingUpdate);

      expect(supportedPath.adjustments).toBeDefined();
      const additionalResources = supportedPath.adjustments.filter(adj => adj.type === 'add_resource');
      expect(additionalResources.length).toBeGreaterThan(0);
    });
  });

  describe('Integration with External Services', () => {
    it('should integrate with skill market data for relevance', async () => {
      const learningPath = await service.generateLearningPath({
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'market-driven' as const,
        availability: 12,
        budget: 800,
      });

      expect(learningPath.marketRelevance).toBeDefined();
      expect(learningPath.marketRelevance.demandScore).toBeGreaterThan(0);
      expect(learningPath.marketRelevance.salaryImpact).toBeDefined();
      expect(learningPath.marketRelevance.jobOpportunities).toBeGreaterThan(0);
    });

    it('should validate path feasibility', async () => {
      const ambitiousRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],
        targetRole: 'Senior Full Stack Developer',
        timeframe: 3, // Very ambitious
        learningStyle: 'intensive' as const,
        availability: 40, // Full time
        budget: 2000,
      };

      const learningPath = await service.generateLearningPath(ambitiousRequest);

      expect(learningPath.feasibilityAnalysis).toBeDefined();
      expect(learningPath.feasibilityAnalysis.overallScore).toBeLessThan(0.8); // Should flag as challenging
      expect(learningPath.feasibilityAnalysis.risks).toBeDefined();
      expect(learningPath.feasibilityAnalysis.risks.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid input gracefully', async () => {
      const invalidRequest = {
        userId: '',
        currentSkills: [],
        targetRole: '',
        timeframe: -1,
        learningStyle: 'invalid' as any,
        availability: -5,
        budget: -100,
      };

      await expect(service.generateLearningPath(invalidRequest))
        .rejects.toThrow('Invalid learning path request');
    });

    it('should handle unknown target roles', async () => {
      const unknownRoleRequest = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Quantum Developer', // Non-existent role
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      const learningPath = await service.generateLearningPath(unknownRoleRequest);

      expect(learningPath).toBeDefined();
      expect(learningPath.warnings).toBeDefined();
      expect(learningPath.warnings.some(w => w.includes('unknown role'))).toBe(true);
    });

    it('should handle service unavailability gracefully', async () => {
      // Mock service failure
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Service unavailable'));

      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Frontend Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      const learningPath = await service.generateLearningPath(request);

      expect(learningPath).toBeDefined();
      expect(learningPath.isOffline).toBe(true);
      expect(learningPath.resources.length).toBeGreaterThan(0); // Should use cached/default resources

      global.fetch = originalFetch;
    });
  });
});
