/**
 * Skill Market Data Service Tests
 * 
 * Tests Skill Market Data Service functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';

describe('SkillMarketDataService', () => {
  let service: SkillMarketDataService;

  beforeEach(() => {
    service = new SkillMarketDataService();
  });

  describe('Market Data Collection', () => {
    it('should fetch market data for a single skill', async () => {
      const marketData = await service.getSkillMarketData('javascript');

      expect(marketData).toBeDefined();
      expect(marketData.skill).toBe('javascript');
      expect(marketData.demand).toBeGreaterThan(0);
      expect(marketData.supply).toBeGreaterThan(0);
      expect(marketData.averageSalary).toBeGreaterThan(0);
      expect(marketData.growth).toBeDefined();
      expect(marketData.difficulty).toBeGreaterThan(0);
      expect(marketData.timeToLearn).toBeGreaterThan(0);
      expect(marketData.category).toBeDefined();
      expect(marketData.lastUpdated).toBeInstanceOf(Date);
    });

    it('should fetch market data for multiple skills', async () => {
      const skills = ['javascript', 'react', 'nodejs'];
      const marketDataList = await service.getMultipleSkillsMarketData(skills);

      expect(marketDataList).toHaveLength(3);
      expect(marketDataList.every(data => data.skill)).toBe(true);
      expect(marketDataList.every(data => data.demand > 0)).toBe(true);
      expect(marketDataList.every(data => data.supply > 0)).toBe(true);
    });

    it('should handle unknown skills gracefully', async () => {
      const marketData = await service.getSkillMarketData('unknown-skill-xyz');

      expect(marketData).toBeDefined();
      expect(marketData.skill).toBe('unknown-skill-xyz');
      expect(marketData.demand).toBe(0);
      expect(marketData.supply).toBe(0);
      expect(marketData.averageSalary).toBe(0);
    });

    it('should cache market data to avoid repeated API calls', async () => {
      const startTime = Date.now();
      await service.getSkillMarketData('javascript');
      const firstCallTime = Date.now() - startTime;

      const secondStartTime = Date.now();
      await service.getSkillMarketData('javascript');
      const secondCallTime = Date.now() - secondStartTime;

      expect(secondCallTime).toBeLessThan(firstCallTime);
    });
  });

  describe('Market Trends Analysis', () => {
    it('should analyze market trends for skills', async () => {
      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);

      expect(trends).toBeDefined();
      expect(trends.topDemandSkills).toBeDefined();
      expect(trends.topDemandSkills.length).toBeGreaterThan(0);
      expect(trends.fastestGrowingSkills).toBeDefined();
      expect(trends.highestPayingSkills).toBeDefined();
      expect(trends.marketGaps).toBeDefined();
      expect(trends.emergingSkills).toBeDefined();
      expect(trends.decliningSkills).toBeDefined();
    });

    it('should identify market gaps (high demand, low supply)', async () => {
      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);

      expect(trends.marketGaps).toBeDefined();
      expect(Array.isArray(trends.marketGaps)).toBe(true);
      
      if (trends.marketGaps.length > 0) {
        const gap = trends.marketGaps[0];
        expect(gap.skill).toBeDefined();
        expect(gap.demandSupplyRatio).toBeGreaterThan(1);
        expect(gap.opportunityScore).toBeGreaterThan(0);
      }
    });

    it('should rank skills by growth potential', async () => {
      const trends = await service.analyzeMarketTrends(['javascript', 'react', 'python']);

      expect(trends.fastestGrowingSkills).toBeDefined();
      expect(Array.isArray(trends.fastestGrowingSkills)).toBe(true);
      
      if (trends.fastestGrowingSkills.length > 1) {
        const first = trends.fastestGrowingSkills[0];
        const second = trends.fastestGrowingSkills[1];
        expect(first.growth).toBeGreaterThanOrEqual(second.growth);
      }
    });
  });

  describe('Salary Analysis', () => {
    it('should provide salary insights for skills', async () => {
      const salaryInsights = await service.getSalaryInsights(['javascript', 'react', 'python']);

      expect(salaryInsights).toBeDefined();
      expect(salaryInsights.averageSalary).toBeGreaterThan(0);
      expect(salaryInsights.salaryRange).toBeDefined();
      expect(salaryInsights.salaryRange.min).toBeGreaterThan(0);
      expect(salaryInsights.salaryRange.max).toBeGreaterThan(salaryInsights.salaryRange.min);
      expect(salaryInsights.topPayingSkills).toBeDefined();
      expect(salaryInsights.salaryGrowthPotential).toBeDefined();
    });

    it('should calculate salary percentiles', async () => {
      const salaryInsights = await service.getSalaryInsights(['javascript', 'react', 'python']);

      expect(salaryInsights.percentiles).toBeDefined();
      expect(salaryInsights.percentiles.p25).toBeGreaterThan(0);
      expect(salaryInsights.percentiles.p50).toBeGreaterThan(salaryInsights.percentiles.p25);
      expect(salaryInsights.percentiles.p75).toBeGreaterThan(salaryInsights.percentiles.p50);
      expect(salaryInsights.percentiles.p90).toBeGreaterThan(salaryInsights.percentiles.p75);
    });
  });

  describe('Geographic Analysis', () => {
    it('should provide location-based market data', async () => {
      const locationData = await service.getLocationBasedMarketData('javascript', 'San Francisco');

      expect(locationData).toBeDefined();
      expect(locationData.skill).toBe('javascript');
      expect(locationData.location).toBe('San Francisco');
      expect(locationData.localDemand).toBeGreaterThan(0);
      expect(locationData.localSalary).toBeGreaterThan(0);
      expect(locationData.costOfLivingAdjustment).toBeDefined();
      expect(locationData.remoteOpportunities).toBeDefined();
    });

    it('should compare market data across multiple locations', async () => {
      const locations = ['San Francisco', 'New York', 'Austin'];
      const comparison = await service.compareLocationMarkets('javascript', locations);

      expect(comparison).toBeDefined();
      expect(comparison.skill).toBe('javascript');
      expect(comparison.locationComparison).toHaveLength(3);
      
      comparison.locationComparison.forEach(loc => {
        expect(loc.location).toBeDefined();
        expect(loc.demand).toBeGreaterThan(0);
        expect(loc.salary).toBeGreaterThan(0);
        expect(loc.costOfLivingIndex).toBeGreaterThan(0);
      });
    });
  });

  describe('Industry Analysis', () => {
    it('should provide industry-specific market data', async () => {
      const industryData = await service.getIndustryMarketData('javascript', 'technology');

      expect(industryData).toBeDefined();
      expect(industryData.skill).toBe('javascript');
      expect(industryData.industry).toBe('technology');
      expect(industryData.industryDemand).toBeGreaterThan(0);
      expect(industryData.averageIndustrySalary).toBeGreaterThan(0);
      expect(industryData.growthProjection).toBeDefined();
      expect(industryData.keyCompanies).toBeDefined();
      expect(Array.isArray(industryData.keyCompanies)).toBe(true);
    });

    it('should rank industries by skill demand', async () => {
      const industryRanking = await service.rankIndustriesBySkillDemand('javascript');

      expect(industryRanking).toBeDefined();
      expect(industryRanking.skill).toBe('javascript');
      expect(industryRanking.industries).toBeDefined();
      expect(Array.isArray(industryRanking.industries)).toBe(true);
      
      if (industryRanking.industries.length > 1) {
        const first = industryRanking.industries[0];
        const second = industryRanking.industries[1];
        expect(first.demandScore).toBeGreaterThanOrEqual(second.demandScore);
      }
    });
  });

  describe('Learning Path Recommendations', () => {
    it('should recommend skills based on market data', async () => {
      const recommendations = await service.getMarketBasedRecommendations({
        currentSkills: ['javascript'],
        careerGoal: 'Full Stack Developer',
        location: 'San Francisco',
        experienceLevel: 'intermediate',
      });

      expect(recommendations).toBeDefined();
      expect(recommendations.recommendedSkills).toBeDefined();
      expect(Array.isArray(recommendations.recommendedSkills)).toBe(true);
      expect(recommendations.learningPriority).toBeDefined();
      expect(recommendations.marketOpportunities).toBeDefined();
      expect(recommendations.salaryImpact).toBeDefined();
    });

    it('should prioritize skills by market opportunity', async () => {
      const recommendations = await service.getMarketBasedRecommendations({
        currentSkills: ['html', 'css'],
        careerGoal: 'Frontend Developer',
        location: 'Remote',
        experienceLevel: 'beginner',
      });

      expect(recommendations.learningPriority).toBeDefined();
      expect(Array.isArray(recommendations.learningPriority)).toBe(true);
      
      if (recommendations.learningPriority.length > 1) {
        const first = recommendations.learningPriority[0];
        const second = recommendations.learningPriority[1];
        expect(first.priorityScore).toBeGreaterThanOrEqual(second.priorityScore);
      }
    });
  });

  describe('Data Freshness & Updates', () => {
    it('should track data freshness and update when stale', async () => {
      const marketData = await service.getSkillMarketData('javascript');
      const dataAge = Date.now() - marketData.lastUpdated.getTime();
      
      expect(dataAge).toBeLessThan(24 * 60 * 60 * 1000); // Less than 24 hours old
    });

    it('should force refresh market data when requested', async () => {
      const oldData = await service.getSkillMarketData('javascript');
      const newData = await service.getSkillMarketData('javascript', { forceRefresh: true });

      expect(newData.lastUpdated.getTime()).toBeGreaterThanOrEqual(oldData.lastUpdated.getTime());
    });

    it('should batch update multiple skills efficiently', async () => {
      const skills = ['javascript', 'react', 'nodejs', 'python', 'typescript'];
      const startTime = Date.now();
      
      await service.batchUpdateMarketData(skills);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Batch update should be more efficient than individual calls
      expect(duration).toBeLessThan(skills.length * 1000); // Less than 1 second per skill
    });
  });

  describe('Error Handling', () => {
    it('should handle API failures gracefully', async () => {
      // Mock API failure
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('API Error'));

      const marketData = await service.getSkillMarketData('javascript');

      expect(marketData).toBeDefined();
      expect(marketData.skill).toBe('javascript');
      expect(marketData.isStale).toBe(true);

      global.fetch = originalFetch;
    });

    it('should validate input parameters', async () => {
      await expect(service.getSkillMarketData('')).rejects.toThrow('Invalid skill name');
      await expect(service.getMultipleSkillsMarketData([])).rejects.toThrow('No skills provided');
    });

    it('should handle rate limiting gracefully', async () => {
      // Mock rate limiting
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 429,
        json: () => Promise.resolve({ error: 'Rate limited' }),
      });

      const marketData = await service.getSkillMarketData('javascript');

      expect(marketData).toBeDefined();
      expect(marketData.isStale).toBe(true);

      global.fetch = originalFetch;
    });
  });

  describe('Performance & Caching', () => {
    it('should implement intelligent caching strategy', async () => {
      const cacheStats = await service.getCacheStatistics();

      expect(cacheStats).toBeDefined();
      expect(cacheStats.hitRate).toBeGreaterThanOrEqual(0);
      expect(cacheStats.missRate).toBeGreaterThanOrEqual(0);
      expect(cacheStats.totalRequests).toBeGreaterThanOrEqual(0);
    });

    it('should clear cache when requested', async () => {
      await service.getSkillMarketData('javascript');
      await service.clearCache();
      
      const cacheStats = await service.getCacheStatistics();
      expect(cacheStats.totalRequests).toBe(0);
    });
  });
});
