/**
 * Edge Case Handler Tests
 * 
 * Tests Edge Case Handler functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

// Performance optimization: Use shared mock instances
const createMockAssessmentEngine = (): jest.Mocked<SkillAssessmentEngine> => ({
  createAssessment: jest.fn(),
  generateQuestions: jest.fn(),
  submitResponse: jest.fn(),
  calculateResults: jest.fn(),
  getAssessment: jest.fn(),
  getAssessmentsByUser: jest.fn(),
} as any);

const createMockMarketDataService = (): jest.Mocked<SkillMarketDataService> => ({
  getSkillMarketData: jest.fn(),
  getMultipleSkillsMarketData: jest.fn(),
  analyzeMarketTrends: jest.fn(),
  getSalaryInsights: jest.fn(),
  getLocationBasedMarketData: jest.fn(),
  getMarketBasedRecommendations: jest.fn(),
} as any);

const createMockLearningPathService = (): jest.Mocked<PersonalizedLearningPathService> => ({
  generateLearningPath: jest.fn(),
  updateProgress: jest.fn(),
  completeMilestone: jest.fn(),
} as any);

describe('EdgeCaseHandler - Core Validation', () => {
  let edgeCaseHandler: EdgeCaseHandler;
  let mockAssessmentEngine: jest.Mocked<SkillAssessmentEngine>;
  let mockMarketDataService: jest.Mocked<SkillMarketDataService>;
  let mockLearningPathService: jest.Mocked<PersonalizedLearningPathService>;

  beforeEach(() => {
    // Reset mocks efficiently
    mockAssessmentEngine = createMockAssessmentEngine();
    mockMarketDataService = createMockMarketDataService();
    mockLearningPathService = createMockLearningPathService();

    edgeCaseHandler = new EdgeCaseHandler(
      mockAssessmentEngine,
      mockMarketDataService,
      mockLearningPathService
    );
  });

  afterEach(() => {
    // Cleanup for performance
    jest.clearAllMocks();
  });

  describe('Core Edge Case Functionality', () => {
    it('should handle basic validation errors', async () => {
      const result = await edgeCaseHandler.handleSkillAssessment(null as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input');
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.fallbackData).toBeDefined();
    }, 3000);

    it('should handle successful assessment creation', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mockAssessmentEngine.createAssessment.mockResolvedValue({
        id: 'assessment-123',
        userId: request.userId,
      } as any);

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    }, 3000);

    it('should handle service failures gracefully', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Service unavailable');
      expect(result.fallbackData).toBeDefined();
    }, 3000);
  });

  // Note: Comprehensive edge case tests have been moved to separate files:
  // - EdgeCaseHandler.validation.test.ts
  // - EdgeCaseHandler.business-logic.test.ts
  // - EdgeCaseHandler.system-failures.test.ts
  // This improves test performance and maintainability


});
