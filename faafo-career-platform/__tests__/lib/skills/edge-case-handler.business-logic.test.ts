/**
 * EdgeCaseHandler Business Logic Tests
 * Focused on business logic edge cases and data consistency
 * Optimized for fast execution
 */

import { EdgeCaseHandler } from '@/lib/skills/EdgeCaseHandler';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';

// Shared mock factories for performance
const createMockServices = () => ({
  mockAssessmentEngine: {
    createAssessment: jest.fn(),
    generateQuestions: jest.fn(),
    submitResponse: jest.fn(),
    calculateResults: jest.fn(),
    getAssessment: jest.fn(),
    getAssessmentsByUser: jest.fn(),
  } as any,
  mockMarketDataService: {
    getSkillMarketData: jest.fn(),
    getMultipleSkillsMarketData: jest.fn(),
    analyzeMarketTrends: jest.fn(),
    getSalaryInsights: jest.fn(),
    getLocationBasedMarketData: jest.fn(),
    getMarketBasedRecommendations: jest.fn(),
  } as any,
  mockLearningPathService: {
    generateLearningPath: jest.fn(),
    updateProgress: jest.fn(),
    completeMilestone: jest.fn(),
  } as any,
});

describe('EdgeCaseHandler - Business Logic', () => {
  let edgeCaseHandler: EdgeCaseHandler;
  let mocks: ReturnType<typeof createMockServices>;

  beforeEach(() => {
    mocks = createMockServices();
    edgeCaseHandler = new EdgeCaseHandler(
      mocks.mockAssessmentEngine,
      mocks.mockMarketDataService,
      mocks.mockLearningPathService
    );
  });

  describe('Non-existent Data', () => {
    it('should handle non-existent skills gracefully', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['non-existent-skill', 'another-fake-skill'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Skill not found');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('One or more skills not found');
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.fallbackData).toBeDefined();
      expect(result.suggestedAlternatives).toBeDefined();
    }, 5000);

    it('should handle non-existent career paths', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Quantum Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {
        throw new Error('Career path not found');
      });

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Career path not found');
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.suggestedAlternatives).toBeDefined();
    }, 5000);

    it('should handle users with no existing data', async () => {
      const request = {
        userId: 'new-user-with-no-data',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.getAssessmentsByUser.mockReturnValue([]);
      mocks.mockAssessmentEngine.createAssessment.mockResolvedValue({
        id: 'new-user-assessment',
        userId: request.userId,
      } as any);

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.isNewUser).toBe(true);
      expect(result.onboardingRecommendations).toBeDefined();
    }, 3000);
  });

  describe('Conflicting Requirements', () => {
    it('should handle circular dependencies in learning paths', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],
        targetRole: 'Full Stack Developer',
        timeframe: 6,
        learningStyle: 'structured' as const,
        availability: 10,
        budget: 500,
      };

      mocks.mockLearningPathService.generateLearningPath.mockImplementation(() => {
        throw new Error('Circular dependency detected');
      });

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Circular dependency');
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.fallbackData).toBeDefined();
    }, 5000);

    it('should handle impossible time/budget constraints', async () => {
      const request = {
        userId: 'user-123',
        currentSkills: [{ skill: 'html', level: 1, confidence: 2 }],
        targetRole: 'Senior Full Stack Developer',
        timeframe: 1,
        learningStyle: 'casual' as const,
        availability: 1,
        budget: 1,
      };

      const result = await edgeCaseHandler.handleLearningPathGeneration(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Impossible constraints');
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.feasibilityAnalysis).toBeDefined();
      expect(result.suggestedAdjustments).toBeDefined();
    }, 5000);
  });

  describe('Data Consistency Issues', () => {
    it('should handle inconsistent skill ratings', async () => {
      const request = {
        userId: 'user-123',
        skillAssessments: [
          { skill: 'javascript', selfRating: 9, confidenceLevel: 2 },
          { skill: 'react', selfRating: 3, confidenceLevel: 9 },
        ],
      };

      const result = await edgeCaseHandler.validateSkillConsistency(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Inconsistent skill ratings');
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.inconsistencies).toBeDefined();
      expect(result.suggestedCorrections).toBeDefined();
    }, 3000);

    it('should handle conflicting market data', async () => {
      const skill = 'javascript';

      mocks.mockMarketDataService.getSkillMarketData.mockResolvedValue({
        skill,
        demand: 95,
        supply: 98,
        averageSalary: -50000,
        growth: 150,
        difficulty: 15,
        timeToLearn: -5,
        category: '',
        lastUpdated: new Date(),
      });

      const result = await edgeCaseHandler.handleMarketDataRequest({ skill });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Inconsistent market data');
      expect(result.errorType).toBe('DATA_CONSISTENCY_ERROR');
      expect(result.correctedData).toBeDefined();
    }, 3000);
  });

  describe('Concurrency Edge Cases', () => {
    it('should handle concurrent assessment submissions', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      // Simulate concurrent requests with reduced load for performance
      const promises = Array(5).fill(null).map(() => 
        edgeCaseHandler.handleSkillAssessment(request)
      );

      const results = await Promise.all(promises);

      expect(results.every(r => r.success || r.errorType === 'CONCURRENCY_ERROR')).toBe(true);
      expect(results.filter(r => r.success).length).toBeGreaterThan(0);
    }, 10000);

    it('should handle resource contention', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Resource locked');
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Resource locked');
      expect(result.errorType).toBe('CONCURRENCY_ERROR');
      expect(result.retryable).toBe(true);
    }, 3000);
  });

  describe('Recovery and Fallback Mechanisms', () => {
    it('should implement automatic retry for transient failures', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      let callCount = 0;
      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        callCount++;
        if (callCount < 3) {
          throw new Error('Transient error');
        }
        return { id: 'assessment-123' } as any;
      });

      const result = await edgeCaseHandler.handleSkillAssessment(request, { maxRetries: 3 });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(2);
      expect(callCount).toBe(3);
    }, 8000);

    it('should provide fallback data when services fail', async () => {
      const request = {
        skill: 'javascript',
      };

      mocks.mockMarketDataService.getSkillMarketData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      const result = await edgeCaseHandler.handleMarketDataRequest(request);

      expect(result.success).toBe(false);
      expect(result.fallbackData).toBeDefined();
      expect(result.fallbackData.skill).toBe('javascript');
      expect(result.fallbackData.isStale).toBe(true);
      expect(result.fallbackData.source).toBe('cache_or_default');
    }, 3000);

    it('should implement circuit breaker pattern', async () => {
      const request = {
        userId: 'user-123',
        skillIds: ['javascript'],
        careerPathId: 'path-456',
      };

      mocks.mockAssessmentEngine.createAssessment.mockImplementation(() => {
        throw new Error('Service down');
      });

      // Reduced iterations for performance
      for (let i = 0; i < 3; i++) {
        await edgeCaseHandler.handleSkillAssessment(request);
      }

      const result = await edgeCaseHandler.handleSkillAssessment(request);

      expect(result.success).toBe(false);
      expect(result.errorType).toBe('CIRCUIT_BREAKER_OPEN');
      expect(result.fallbackData).toBeDefined();
    }, 8000);
  });
});
