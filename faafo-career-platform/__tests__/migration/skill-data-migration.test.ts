/**
 * Skill Data Migration Tests
 * 
 * Tests Skill Data Migration functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { PrismaClient } from '@prisma/client';
import { SkillDataMigrator } from '@/lib/migration/skill-data-migrator';

// Mock Prisma
const mockPrisma = {
  userSkillProgress: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  skillAssessment: {
    findMany: jest.fn(),
    create: jest.fn(),
    createMany: jest.fn(),
    upsert: jest.fn(),
  },
  skill: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    upsert: jest.fn(),
  },
  assessment: {
    findMany: jest.fn(),
  },
  assessmentResponse: {
    findMany: jest.fn(),
  },
  learningAnalytics: {
    findMany: jest.fn(),
  },
  interviewProgress: {
    findMany: jest.fn(),
  },
  $transaction: jest.fn(),
  $disconnect: jest.fn(),
};

jest.mock('@/lib/prisma', () => ({
  prisma: mockPrisma,
}));

describe('Skill Data Migration - TDD', () => {
  let migrator: SkillDataMigrator;

  beforeEach(() => {
    jest.clearAllMocks();
    migrator = new SkillDataMigrator(mockPrisma);
  });

  describe('UserSkillProgress Migration', () => {
    it('should migrate UserSkillProgress to SkillAssessment format', async () => {
      // Test: Existing UserSkillProgress should be converted to SkillAssessment
      const mockUserSkillProgress = [
        {
          id: 'progress-1',
          userId: 'user-123',
          skillId: 'skill-456',
          currentLevel: 'INTERMEDIATE',
          progressPoints: 75,
          selfAssessment: 7,
          practiceHours: 40,
          lastPracticed: new Date('2024-01-15'),
          createdAt: new Date('2024-01-01'),
          skill: {
            id: 'skill-456',
            name: 'JavaScript',
            category: 'Programming',
          },
        },
      ];

      mockPrisma.userSkillProgress.findMany.mockResolvedValue(mockUserSkillProgress);
      mockPrisma.skillAssessment.findMany.mockResolvedValue([]); // No existing assessments
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 1 });

      const result = await migrator.migrateUserSkillProgress('user-123');

      expect(mockPrisma.userSkillProgress.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-123' },
        include: { skill: true },
      });

      expect(mockPrisma.skillAssessment.createMany).toHaveBeenCalledWith({
        data: [
          {
            userId: 'user-123',
            skillId: 'skill-456',
            selfRating: 7,
            confidenceLevel: 6, // 7 * 0.8 (INTERMEDIATE multiplier) rounded
            assessmentType: 'SELF_ASSESSMENT',
            assessmentDate: new Date('2024-01-15'),
            notes: 'Migrated from UserSkillProgress. Practice hours: 40, Progress points: 75',
            isActive: true,
          },
        ],
        skipDuplicates: true,
      });

      expect(result.migratedCount).toBe(1);
      expect(result.skippedCount).toBe(0);
    });

    it('should handle missing selfAssessment in UserSkillProgress', async () => {
      // Test: UserSkillProgress without selfAssessment should use derived rating
      const mockUserSkillProgress = [
        {
          id: 'progress-2',
          userId: 'user-123',
          skillId: 'skill-789',
          currentLevel: 'ADVANCED',
          progressPoints: 90,
          selfAssessment: null, // Missing self assessment
          practiceHours: 60,
          skill: {
            id: 'skill-789',
            name: 'React',
            category: 'Frontend',
          },
        },
      ];

      mockPrisma.userSkillProgress.findMany.mockResolvedValue(mockUserSkillProgress);
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 1 });

      const result = await migrator.migrateUserSkillProgress('user-123');

      expect(mockPrisma.skillAssessment.createMany).toHaveBeenCalledWith({
        data: [
          expect.objectContaining({
            selfRating: 8, // Derived from ADVANCED level
            confidenceLevel: 7, // 8 * 0.9 (ADVANCED multiplier) rounded
            notes: expect.stringContaining('Derived from skill level: ADVANCED'),
          }),
        ],
        skipDuplicates: true,
      });

      expect(result.migratedCount).toBe(1);
    });

    it('should skip migration if SkillAssessment already exists', async () => {
      // Test: Should not duplicate existing assessments
      const mockUserSkillProgress = [
        {
          id: 'progress-3',
          userId: 'user-123',
          skillId: 'skill-999',
          currentLevel: 'BEGINNER',
          selfAssessment: 3,
          skill: { id: 'skill-999', name: 'Python' },
        },
      ];

      const mockExistingAssessments = [
        {
          userId: 'user-123',
          skillId: 'skill-999',
          assessmentType: 'SELF_ASSESSMENT',
        },
      ];

      mockPrisma.userSkillProgress.findMany.mockResolvedValue(mockUserSkillProgress);
      mockPrisma.skillAssessment.findMany.mockResolvedValue(mockExistingAssessments);
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 0 });

      const result = await migrator.migrateUserSkillProgress('user-123');

      expect(result.migratedCount).toBe(0);
      expect(result.skippedCount).toBe(1);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Assessment Data Migration', () => {
    it('should migrate legacy Assessment responses to SkillAssessment', async () => {
      // Test: Legacy assessment responses should be converted to skill assessments
      const mockAssessments = [
        {
          id: 'assessment-1',
          userId: 'user-456',
          status: 'COMPLETED',
          completedAt: new Date('2024-02-01'),
          responses: [
            {
              id: 'response-1',
              questionKey: 'skill_javascript',
              answer: { rating: 6, confidence: 7 },
              createdAt: new Date('2024-02-01'),
            },
            {
              id: 'response-2',
              questionKey: 'skill_react',
              answer: { rating: 5, confidence: 6 },
              createdAt: new Date('2024-02-01'),
            },
          ],
        },
      ];

      mockPrisma.assessment.findMany.mockResolvedValue(mockAssessments);
      mockPrisma.skill.findUnique
        .mockResolvedValueOnce({ id: 'skill-js', name: 'JavaScript' })
        .mockResolvedValueOnce({ id: 'skill-react', name: 'React' });
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 2 });

      const result = await migrator.migrateLegacyAssessments('user-456');

      expect(mockPrisma.skillAssessment.createMany).toHaveBeenCalledWith({
        data: [
          expect.objectContaining({
            userId: 'user-456',
            skillId: 'skill-js',
            selfRating: 6,
            confidenceLevel: 7,
            assessmentType: 'SELF_ASSESSMENT',
            notes: expect.stringContaining('Migrated from legacy assessment'),
          }),
          expect.objectContaining({
            userId: 'user-456',
            skillId: 'skill-react',
            selfRating: 5,
            confidenceLevel: 6,
          }),
        ],
        skipDuplicates: true,
      });

      expect(result.migratedCount).toBe(2);
    });

    it('should handle missing skills during assessment migration', async () => {
      // Test: Should handle cases where skill doesn't exist in database
      const mockAssessments = [
        {
          id: 'assessment-2',
          userId: 'user-789',
          status: 'COMPLETED',
          responses: [
            {
              questionKey: 'skill_nonexistent',
              answer: { rating: 8 },
            },
          ],
        },
      ];

      mockPrisma.assessment.findMany.mockResolvedValue(mockAssessments);
      mockPrisma.skill.findUnique.mockResolvedValue(null); // Skill not found
      mockPrisma.skill.create.mockResolvedValue({
        id: 'skill-new',
        name: 'Nonexistent',
        category: 'Unknown',
      });
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 1 });

      const result = await migrator.migrateLegacyAssessments('user-789');

      expect(mockPrisma.skill.create).toHaveBeenCalledWith({
        data: {
          name: 'Nonexistent',
          category: 'Unknown',
          description: 'Skill created during migration',
        },
      });

      expect(result.migratedCount).toBe(1);
      expect(result.createdSkills).toBe(1);
    });
  });

  describe('Learning Analytics Migration', () => {
    it('should extract skill insights from learning analytics', async () => {
      // Test: Learning analytics should provide insights for skill assessments
      const mockAnalytics = [
        {
          id: 'analytics-1',
          userId: 'user-999',
          date: new Date('2024-03-01'),
          skillsImproved: 3,
          timeSpent: 120, // 2 hours
          learningVelocity: 0.8,
          avgCompletionTime: 45.5,
        },
      ];

      mockPrisma.learningAnalytics.findMany.mockResolvedValue(mockAnalytics);

      const insights = await migrator.extractLearningInsights('user-999');

      expect(insights).toEqual({
        totalLearningTime: 120,
        averageLearningVelocity: 0.8,
        skillImprovementRate: 3,
        averageCompletionTime: 45.5,
        learningConsistency: expect.any(Number),
        recommendedConfidenceAdjustment: expect.any(Number),
      });
    });

    it('should apply learning insights to skill assessment confidence', async () => {
      // Test: Learning insights should adjust confidence levels in assessments
      const mockInsights = {
        totalLearningTime: 200,
        averageLearningVelocity: 0.9,
        skillImprovementRate: 4,
        learningConsistency: 0.85,
        recommendedConfidenceAdjustment: 1.2,
      };

      const baseAssessment = {
        userId: 'user-999',
        skillId: 'skill-test',
        selfRating: 6,
        confidenceLevel: 5,
      };

      const adjustedAssessment = migrator.applyLearningInsights(baseAssessment, mockInsights);

      expect(adjustedAssessment.confidenceLevel).toBeGreaterThan(5);
      expect(adjustedAssessment.confidenceLevel).toBeLessThanOrEqual(10);
      expect(adjustedAssessment.notes).toContain('confidence adjusted based on learning analytics');
    });
  });

  describe('Interview Progress Migration', () => {
    it('should migrate interview progress to skill assessments', async () => {
      // Test: Interview progress should be converted to skill assessments
      const mockInterviewProgress = [
        {
          id: 'interview-1',
          userId: 'user-interview',
          skillArea: 'TECHNICAL',
          competencyLevel: 'INTERMEDIATE',
          averageScore: 75.5,
          totalSessions: 10,
          strengthAreas: ['algorithms', 'data-structures'],
          improvementAreas: ['system-design'],
        },
      ];

      mockPrisma.interviewProgress.findMany.mockResolvedValue(mockInterviewProgress);
      mockPrisma.skill.upsert
        .mockResolvedValueOnce({ id: 'skill-algo', name: 'Algorithms' })
        .mockResolvedValueOnce({ id: 'skill-ds', name: 'Data Structures' })
        .mockResolvedValueOnce({ id: 'skill-sys', name: 'System Design' });
      mockPrisma.skillAssessment.createMany.mockResolvedValue({ count: 3 });

      const result = await migrator.migrateInterviewProgress('user-interview');

      expect(mockPrisma.skillAssessment.createMany).toHaveBeenCalledWith({
        data: [
          expect.objectContaining({
            skillId: 'skill-algo',
            selfRating: 8, // High rating for strength area
            confidenceLevel: 8,
            assessmentType: 'PERFORMANCE_BASED',
            notes: expect.stringContaining('interview practice'),
          }),
          expect.objectContaining({
            skillId: 'skill-ds',
            selfRating: 8,
          }),
          expect.objectContaining({
            skillId: 'skill-sys',
            selfRating: 5, // Lower rating for improvement area
            confidenceLevel: 5,
          }),
        ],
        skipDuplicates: true,
      });

      expect(result.migratedCount).toBe(3);
    });
  });

  describe('Comprehensive Migration', () => {
    it('should perform complete migration for a user', async () => {
      // Test: Should migrate all data sources for a user
      mockPrisma.userSkillProgress.findMany.mockResolvedValue([]);
      mockPrisma.assessment.findMany.mockResolvedValue([]);
      mockPrisma.learningAnalytics.findMany.mockResolvedValue([]);
      mockPrisma.interviewProgress.findMany.mockResolvedValue([]);
      mockPrisma.$transaction.mockImplementation((callback) => callback(mockPrisma));

      const result = await migrator.migrateUserData('user-complete');

      expect(mockPrisma.$transaction).toHaveBeenCalled();
      expect(result).toEqual({
        userId: 'user-complete',
        totalMigrated: 0,
        userSkillProgress: { migratedCount: 0, skippedCount: 0, errors: [] },
        legacyAssessments: { migratedCount: 0, skippedCount: 0, errors: [] },
        interviewProgress: { migratedCount: 0, skippedCount: 0, errors: [] },
        learningInsights: expect.any(Object),
        migrationDate: expect.any(Date),
        success: true,
      });
    });

    it('should handle migration errors gracefully', async () => {
      // Test: Should handle and report migration errors
      mockPrisma.userSkillProgress.findMany.mockRejectedValue(new Error('Database error'));
      mockPrisma.$transaction.mockImplementation((callback) => callback(mockPrisma));

      const result = await migrator.migrateUserData('user-error');

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Database error');
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain backward compatibility with existing APIs', async () => {
      // Test: Existing UserSkillProgress queries should still work
      const compatibilityLayer = migrator.getBackwardCompatibilityLayer();

      mockPrisma.userSkillProgress.findMany.mockResolvedValue([
        {
          id: 'progress-compat',
          userId: 'user-compat',
          skillId: 'skill-compat',
          currentLevel: 'INTERMEDIATE',
          selfAssessment: 7,
        },
      ]);

      const result = await compatibilityLayer.getUserSkillProgress('user-compat');

      expect(result).toEqual([
        expect.objectContaining({
          id: 'progress-compat',
          userId: 'user-compat',
          skillId: 'skill-compat',
          currentLevel: 'INTERMEDIATE',
          selfAssessment: 7,
        }),
      ]);
    });

    it('should provide migration status for each user', async () => {
      // Test: Should track migration status to avoid duplicate migrations
      const migrationStatus = await migrator.getMigrationStatus('user-status');

      expect(migrationStatus).toEqual({
        userId: 'user-status',
        isMigrated: expect.any(Boolean),
        migrationDate: expect.any(Date),
        dataSourcesMigrated: expect.any(Array),
        totalRecordsMigrated: expect.any(Number),
        lastChecked: expect.any(Date),
      });
    });
  });

  describe('Data Validation', () => {
    it('should validate migrated data integrity', async () => {
      // Test: Should validate that migrated data is consistent and complete
      const validationResult = await migrator.validateMigratedData('user-validate');

      expect(validationResult).toEqual({
        userId: 'user-validate',
        isValid: expect.any(Boolean),
        validationErrors: expect.any(Array),
        dataConsistencyScore: expect.any(Number),
        recommendedActions: expect.any(Array),
        validatedAt: expect.any(Date),
      });
    });

    it('should detect and report data inconsistencies', async () => {
      // Test: Should identify inconsistencies between old and new data
      mockPrisma.userSkillProgress.findMany.mockResolvedValue([
        { skillId: 'skill-1', selfAssessment: 8 },
      ]);
      mockPrisma.skillAssessment.findMany.mockResolvedValue([
        { skillId: 'skill-1', selfRating: 6 }, // Inconsistent rating
      ]);

      const inconsistencies = await migrator.detectDataInconsistencies('user-inconsistent');

      expect(inconsistencies).toEqual([
        expect.objectContaining({
          type: 'RATING_MISMATCH',
          skillId: 'skill-1',
          oldValue: 8,
          newValue: 6,
          severity: 'MEDIUM',
        }),
      ]);
    });
  });
});
