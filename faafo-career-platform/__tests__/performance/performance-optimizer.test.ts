/**
 * Performance Optimizer Tests
 * 
 * Performance tests for Performance Optimizer measuring response times, throughput, and resource usage.
 * 
 * @category performance
 * @requires Performance monitoring, load testing
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Mock environment variables for testing
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-performance-testing';
process.env.NODE_ENV = 'test';

// Mock Prisma with proper implementations
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn().mockImplementation((params) => {
        if (params.where.id.includes('test')) {
          return Promise.resolve({ 
            id: params.where.id, 
            email: `${params.where.id}@example.com`,
            name: `Test User ${params.where.id}`,
            createdAt: new Date()
          });
        }
        return Promise.resolve(null);
      }),
    },
    skill: {
      findMany: jest.fn().mockImplementation((params) => {
        const skillMap = {
          'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming' },
          'react': { id: 'react', name: 'React', category: 'Frontend' },
          'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend' }
        };
        
        if (params.where?.id?.in) {
          return Promise.resolve(
            params.where.id.in.map((id: string) => skillMap[id as keyof typeof skillMap]).filter(Boolean)
          );
        }
        return Promise.resolve(Object.values(skillMap));
      }),
      findFirst: jest.fn().mockImplementation((params) => {
        const skillName = params.where?.name?.equals?.toLowerCase() || params.where?.name?.equals;
        const skillMap = {
          'javascript': { id: 'javascript', name: 'JavaScript', category: 'Programming' },
          'react': { id: 'react', name: 'React', category: 'Frontend' },
          'nodejs': { id: 'nodejs', name: 'Node.js', category: 'Backend' }
        };
        return Promise.resolve(skillMap[skillName as keyof typeof skillMap] || null);
      }),
    },
    skillMarketData: {
      findFirst: jest.fn().mockResolvedValue({
        demand: 80,
        averageSalary: 95000,
        growth: 15,
        dataDate: new Date(),
        metadata: {}
      }),
    },
  },
}));

import { skillGapPerformanceOptimizer } from '@/lib/performance/SkillGapPerformanceOptimizer';

describe('Performance Optimizer Tests', () => {
  beforeEach(() => {
    // Clear caches before each test
    skillGapPerformanceOptimizer.clearCaches();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('User Caching', () => {
    it('should cache user lookups effectively', async () => {
      const userId = 'test-user-123';

      // First call - should hit database
      const user1 = await skillGapPerformanceOptimizer.getUser(userId);
      expect(user1).toBeTruthy();
      expect(user1.id).toBe(userId);

      // Second call - should hit cache
      const user2 = await skillGapPerformanceOptimizer.getUser(userId);
      expect(user2).toBeTruthy();
      expect(user2.id).toBe(userId);

      // Check cache stats
      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.user.hits).toBeGreaterThan(0);
      expect(stats.user.size).toBeGreaterThan(0);
    });

    it('should handle cache misses for non-existent users', async () => {
      const userId = 'non-existent-user';

      const user = await skillGapPerformanceOptimizer.getUser(userId);
      expect(user).toBeNull();

      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.user.misses).toBeGreaterThan(0);
    });
  });

  describe('Skill Caching', () => {
    it('should cache skill lookups effectively', async () => {
      const skillIds = ['javascript', 'react', 'nodejs'];

      // First call - should hit database
      const skills1 = await skillGapPerformanceOptimizer.getSkills(skillIds);
      expect(skills1).toHaveLength(3);
      expect(skills1.map(s => s.id)).toEqual(expect.arrayContaining(skillIds));

      // Second call - should hit cache
      const skills2 = await skillGapPerformanceOptimizer.getSkills(skillIds);
      expect(skills2).toHaveLength(3);
      expect(skills2.map(s => s.id)).toEqual(expect.arrayContaining(skillIds));

      // Check cache stats
      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.skill.hits).toBeGreaterThan(0);
      expect(stats.skill.size).toBeGreaterThan(0);
    });

    it('should handle partial cache hits for mixed skill requests', async () => {
      // Cache some skills first
      await skillGapPerformanceOptimizer.getSkills(['javascript']);

      // Request mix of cached and uncached skills
      const skills = await skillGapPerformanceOptimizer.getSkills(['javascript', 'react']);
      expect(skills).toHaveLength(2);

      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.skill.hits).toBeGreaterThan(0);
      expect(stats.skill.misses).toBeGreaterThan(0);
    });
  });

  describe('Market Data Caching', () => {
    it('should cache market data lookups effectively', async () => {
      const skill = 'JavaScript';

      // First call - should hit database
      const marketData1 = await skillGapPerformanceOptimizer.getMarketData(skill);
      expect(marketData1).toBeTruthy();
      expect(marketData1.skill).toBe(skill.toLowerCase());

      // Second call - should hit cache
      const marketData2 = await skillGapPerformanceOptimizer.getMarketData(skill);
      expect(marketData2).toBeTruthy();
      expect(marketData2.skill).toBe(skill.toLowerCase());

      // Check cache stats
      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.marketData.hits).toBeGreaterThan(0);
      expect(stats.marketData.size).toBeGreaterThan(0);
    });

    it('should handle location-specific caching', async () => {
      const skill = 'JavaScript';
      const location1 = 'San Francisco';
      const location2 = 'New York';

      // Different locations should be cached separately
      const marketData1 = await skillGapPerformanceOptimizer.getMarketData(skill, location1);
      const marketData2 = await skillGapPerformanceOptimizer.getMarketData(skill, location2);

      expect(marketData1).toBeTruthy();
      expect(marketData2).toBeTruthy();

      const stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.marketData.size).toBe(2); // Two different cache entries
    });

    it('should provide default data for unknown skills', async () => {
      const unknownSkill = 'UnknownSkill123';

      const marketData = await skillGapPerformanceOptimizer.getMarketData(unknownSkill);
      expect(marketData).toBeTruthy();
      expect(marketData.skill).toBe(unknownSkill.toLowerCase());
      expect(marketData.isStale).toBe(true);
      expect(marketData.category).toBe('Unknown');
    });
  });

  describe('Performance Metrics', () => {
    it('should track performance metrics for operations', async () => {
      // Perform some operations
      await skillGapPerformanceOptimizer.getUser('test-user-123');
      await skillGapPerformanceOptimizer.getSkills(['javascript']);
      await skillGapPerformanceOptimizer.getMarketData('JavaScript');

      const metrics = skillGapPerformanceOptimizer.getPerformanceMetrics();
      expect(metrics.size).toBeGreaterThan(0);

      // Check that metrics contain expected operations
      const metricKeys = Array.from(metrics.keys());
      expect(metricKeys).toEqual(expect.arrayContaining(['getUser', 'getSkills', 'getMarketData']));

      // Check metric structure
      const userMetrics = metrics.get('getUser');
      if (userMetrics) {
        expect(userMetrics).toHaveProperty('queryTime');
        expect(userMetrics).toHaveProperty('cacheHitRate');
        expect(userMetrics).toHaveProperty('memoryUsage');
        expect(userMetrics).toHaveProperty('operationsPerSecond');
      }
    });

    it('should calculate cache hit rates correctly', async () => {
      const userId = 'test-user-cache-rate';

      // First call (cache miss)
      await skillGapPerformanceOptimizer.getUser(userId);
      
      // Second call (cache hit)
      await skillGapPerformanceOptimizer.getUser(userId);

      const metrics = skillGapPerformanceOptimizer.getPerformanceMetrics();
      const userMetrics = metrics.get('getUser');
      
      if (userMetrics) {
        expect(userMetrics.cacheHitRate).toBeGreaterThan(0);
        expect(userMetrics.cacheHitRate).toBeLessThanOrEqual(1);
      }
    });
  });

  describe('Cache Management', () => {
    it('should clear all caches when requested', async () => {
      // Populate caches
      await skillGapPerformanceOptimizer.getUser('test-user-123');
      await skillGapPerformanceOptimizer.getSkills(['javascript']);
      await skillGapPerformanceOptimizer.getMarketData('JavaScript');

      let stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.user.size).toBeGreaterThan(0);
      expect(stats.skill.size).toBeGreaterThan(0);
      expect(stats.marketData.size).toBeGreaterThan(0);

      // Clear caches
      skillGapPerformanceOptimizer.clearCaches();

      stats = skillGapPerformanceOptimizer.getCacheStats();
      expect(stats.user.size).toBe(0);
      expect(stats.skill.size).toBe(0);
      expect(stats.marketData.size).toBe(0);
    });

    it('should provide detailed cache statistics', async () => {
      // Perform operations to generate stats
      await skillGapPerformanceOptimizer.getUser('test-user-123');
      await skillGapPerformanceOptimizer.getUser('test-user-123'); // Cache hit
      await skillGapPerformanceOptimizer.getUser('non-existent'); // Cache miss

      const stats = skillGapPerformanceOptimizer.getCacheStats();
      
      expect(stats).toHaveProperty('user');
      expect(stats).toHaveProperty('skill');
      expect(stats).toHaveProperty('marketData');
      expect(stats).toHaveProperty('assessment');

      expect(stats.user).toHaveProperty('size');
      expect(stats.user).toHaveProperty('hits');
      expect(stats.user).toHaveProperty('misses');

      expect(stats.user.hits).toBeGreaterThan(0);
      expect(stats.user.misses).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock a database error
      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.user.findUnique.mockRejectedValueOnce(new Error('Database connection failed'));

      const user = await skillGapPerformanceOptimizer.getUser('test-user-error');
      expect(user).toBeNull(); // Should handle error gracefully
    });

    it('should continue working after errors', async () => {
      // Mock a database error for one call
      const mockPrisma = require('@/lib/prisma').prisma;
      mockPrisma.user.findUnique.mockRejectedValueOnce(new Error('Temporary error'));

      // First call should fail gracefully
      const user1 = await skillGapPerformanceOptimizer.getUser('test-user-recovery');
      expect(user1).toBeNull();

      // Second call should work normally
      const user2 = await skillGapPerformanceOptimizer.getUser('test-user-recovery');
      expect(user2).toBeTruthy();
    });
  });
});
