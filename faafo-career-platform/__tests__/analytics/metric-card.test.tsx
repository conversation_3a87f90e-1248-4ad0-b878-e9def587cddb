/**
 * Metric Card Tests
 * 
 * Tests Metric Card component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MetricCard, UserMetricCard, LearningMetricCard } from '@/components/analytics/MetricCard';
import { TrendingUp } from 'lucide-react';

describe('MetricCard', () => {
  it('should render basic metric card correctly', () => {
    render(
      <MetricCard
        title="Total Users"
        value={1000}
        icon={TrendingUp}
        iconColor="text-blue-600"
      />
    );

    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('1.0K')).toBeInTheDocument();
  });

  it('should render metric card with change indicator', () => {
    render(
      <MetricCard
        title="Active Users"
        value={500}
        change={{
          value: 12.5,
          period: 'last month',
          type: 'increase',
        }}
        icon={TrendingUp}
      />
    );

    expect(screen.getByText('Active Users')).toBeInTheDocument();
    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('12.5%')).toBeInTheDocument();
    expect(screen.getByText('+12.5% from last month')).toBeInTheDocument();
  });

  it('should render metric card with decrease indicator', () => {
    render(
      <MetricCard
        title="Bounce Rate"
        value="25.3%"
        change={{
          value: 5.2,
          period: 'last week',
          type: 'decrease',
        }}
      />
    );

    expect(screen.getByText('Bounce Rate')).toBeInTheDocument();
    expect(screen.getByText('25.3%')).toBeInTheDocument();
    expect(screen.getByText('5.2%')).toBeInTheDocument();
    expect(screen.getByText('-5.2% from last week')).toBeInTheDocument();
  });

  it('should render loading state correctly', () => {
    render(
      <MetricCard
        title="Loading Metric"
        value={0}
        loading={true}
      />
    );

    // Should show skeleton loading elements
    const skeletonElements = document.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('should format large numbers correctly', () => {
    render(
      <MetricCard
        title="Large Number"
        value={1500000}
      />
    );

    expect(screen.getByText('1.5M')).toBeInTheDocument();
  });

  it('should use custom format function when provided', () => {
    const customFormat = (value: string | number) => `$${value}`;
    
    render(
      <MetricCard
        title="Revenue"
        value={1000}
        formatValue={customFormat}
      />
    );

    expect(screen.getByText('$1000')).toBeInTheDocument();
  });

  it('should render description when provided', () => {
    render(
      <MetricCard
        title="Test Metric"
        value={100}
        description="This is a test description"
      />
    );

    expect(screen.getByText('This is a test description')).toBeInTheDocument();
  });
});

describe('UserMetricCard', () => {
  it('should render user metrics correctly', () => {
    render(
      <UserMetricCard
        totalUsers={1000}
        activeUsers={500}
        newUsers={50}
      />
    );

    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('Active Users')).toBeInTheDocument();
    expect(screen.getByText('New Users')).toBeInTheDocument();
    expect(screen.getByText('1.0K')).toBeInTheDocument();
    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('50')).toBeInTheDocument();
  });

  it('should render loading state for all cards', () => {
    render(
      <UserMetricCard
        totalUsers={0}
        activeUsers={0}
        newUsers={0}
        loading={true}
      />
    );

    const skeletonElements = document.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });
});

describe('LearningMetricCard', () => {
  it('should render learning metrics correctly', () => {
    render(
      <LearningMetricCard
        totalResources={500}
        completedResources={150}
        completionRate={30}
      />
    );

    expect(screen.getByText('Total Resources')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('Completion Rate')).toBeInTheDocument();
    expect(screen.getByText('500')).toBeInTheDocument();
    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('30.0%')).toBeInTheDocument();
  });

  it('should render loading state for all cards', () => {
    render(
      <LearningMetricCard
        totalResources={0}
        completedResources={0}
        completionRate={0}
        loading={true}
      />
    );

    const skeletonElements = document.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });
});
