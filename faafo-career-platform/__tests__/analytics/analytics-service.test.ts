/**
 * Analytics Service Tests
 * 
 * Tests Analytics Service functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

// Mock the analytics service
const mockAnalyticsService = {
  getUserEngagementMetrics: jest.fn(),
  getLearningProgressMetrics: jest.fn(),
  getCareerPathMetrics: jest.fn(),
  getCommunityMetrics: jest.fn(),
  getComprehensiveAnalytics: jest.fn(),
};

jest.mock('@/lib/analytics-service', () => ({
  analyticsService: mockAnalyticsService,
}));

import { analyticsService } from '@/lib/analytics-service';

describe('AnalyticsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserEngagementMetrics', () => {
    it('should return user engagement metrics', async () => {
      const mockResult = {
        totalUsers: 1000,
        activeUsers: {
          daily: 50,
          weekly: 200,
          monthly: 500,
        },
        newUsers: {
          today: 10,
          thisWeek: 75,
          thisMonth: 150,
        },
        userRetention: {
          day1: 0,
          day7: 0,
          day30: 80,
        },
        sessionMetrics: {
          averageSessionDuration: 0,
          totalSessions: 0,
          bounceRate: 0,
        },
        engagementTrends: [],
      };

      mockAnalyticsService.getUserEngagementMetrics.mockResolvedValue(mockResult);

      const result = await analyticsService.getUserEngagementMetrics(30);

      expect(result).toEqual(mockResult);
      expect(mockAnalyticsService.getUserEngagementMetrics).toHaveBeenCalledWith(30);
    });

    it('should handle errors gracefully', async () => {
      mockAnalyticsService.getUserEngagementMetrics.mockRejectedValue(new Error('Database error'));

      await expect(analyticsService.getUserEngagementMetrics(30))
        .rejects.toThrow('Database error');
    });
  });

  describe('getLearningProgressMetrics', () => {
    it('should return learning progress metrics', async () => {
      const mockResult = {
        totalResources: 500,
        completedResources: 150,
        inProgressResources: 75,
        averageCompletionTime: 0,
        completionRate: 30,
        popularResources: [
          {
            id: 'resource-1',
            title: 'Test Resource 1',
            completions: 2,
            averageRating: 4.5,
          },
          {
            id: 'resource-2',
            title: 'Test Resource 2',
            completions: 1,
            averageRating: 3,
          },
        ],
        learningTrends: [],
        categoryBreakdown: [
          { category: 'CYBERSECURITY', totalResources: 100, completedResources: 30, completionRate: 30 },
          { category: 'DATA_SCIENCE', totalResources: 80, completedResources: 25, completionRate: 31.25 },
        ],
      };

      mockAnalyticsService.getLearningProgressMetrics.mockResolvedValue(mockResult);

      const result = await analyticsService.getLearningProgressMetrics(30);

      expect(result).toEqual(mockResult);
      expect(mockAnalyticsService.getLearningProgressMetrics).toHaveBeenCalledWith(30);
    });
  });

  describe('getCareerPathMetrics', () => {
    it('should return career path metrics', async () => {
      const mockResult = {
        totalPaths: 25,
        activePaths: 15,
        completionRates: [
          {
            pathId: 'path-1',
            pathName: 'Cybersecurity Specialist',
            totalEnrolled: 3,
            completed: 2,
            completionRate: 66.67,
            averageTimeToComplete: 0,
          },
          {
            pathId: 'path-2',
            pathName: 'Data Scientist',
            totalEnrolled: 2,
            completed: 0,
            completionRate: 0,
            averageTimeToComplete: 0,
          },
        ],
        pathPopularity: [
          {
            pathId: 'path-1',
            pathName: 'Cybersecurity Specialist',
            bookmarks: 2,
            enrollments: 3,
            popularityScore: 8,
          },
          {
            pathId: 'path-2',
            pathName: 'Data Scientist',
            bookmarks: 1,
            enrollments: 2,
            popularityScore: 5,
          },
        ],
        progressDistribution: [
          { progressRange: '0-25%', userCount: 0, percentage: 0 },
          { progressRange: '26-50%', userCount: 0, percentage: 0 },
          { progressRange: '51-75%', userCount: 0, percentage: 0 },
          { progressRange: '76-100%', userCount: 0, percentage: 0 },
        ],
      };

      mockAnalyticsService.getCareerPathMetrics.mockResolvedValue(mockResult);

      const result = await analyticsService.getCareerPathMetrics(30);

      expect(result).toEqual(mockResult);
      expect(mockAnalyticsService.getCareerPathMetrics).toHaveBeenCalledWith(30);
    });
  });

  describe('getCommunityMetrics', () => {
    it('should return community metrics', async () => {
      const mockResult = {
        totalPosts: 150,
        totalReplies: 300,
        activePosters: 45,
        engagementRate: 200,
        topContributors: [
          {
            userId: 'user-1',
            userName: 'John Doe',
            postCount: 2,
            replyCount: 1,
            reputation: 100,
          },
          {
            userId: 'user-2',
            userName: 'Jane Smith',
            postCount: 1,
            replyCount: 2,
            reputation: 75,
          },
        ],
        categoryActivity: [
          {
            categoryId: 'cat-1',
            categoryName: 'General Discussion',
            postCount: 2,
            replyCount: 3,
            lastActivity: '2023-12-01T00:00:00.000Z',
          },
        ],
        communityTrends: [
          { date: '2023-12-01', newPosts: 5, newReplies: 12, activeUsers: 8 },
        ],
      };

      mockAnalyticsService.getCommunityMetrics.mockResolvedValue(mockResult);

      const result = await analyticsService.getCommunityMetrics(30);

      expect(result).toEqual(mockResult);
      expect(mockAnalyticsService.getCommunityMetrics).toHaveBeenCalledWith(30);
    });
  });

  describe('getComprehensiveAnalytics', () => {
    it('should return all analytics combined', async () => {
      const mockResult = {
        userEngagement: {
          totalUsers: 1000,
          activeUsers: { daily: 50, weekly: 200, monthly: 500 },
          newUsers: { today: 10, thisWeek: 75, thisMonth: 150 },
          userRetention: { day1: 0, day7: 0, day30: 80 },
          sessionMetrics: { averageSessionDuration: 0, totalSessions: 0, bounceRate: 0 },
          engagementTrends: [],
        },
        learningProgress: {
          totalResources: 500,
          completedResources: 150,
          inProgressResources: 75,
          averageCompletionTime: 0,
          completionRate: 30,
          popularResources: [],
          learningTrends: [],
          categoryBreakdown: [],
        },
        careerPaths: {
          totalPaths: 25,
          activePaths: 15,
          completionRates: [],
          pathPopularity: [],
          progressDistribution: [],
        },
        community: {
          totalPosts: 150,
          totalReplies: 300,
          activePosters: 45,
          engagementRate: 200,
          topContributors: [],
          categoryActivity: [],
          communityTrends: [],
        },
        generatedAt: '2023-12-01T00:00:00.000Z',
        timeRange: '30 days',
      };

      mockAnalyticsService.getComprehensiveAnalytics.mockResolvedValue(mockResult);

      const result = await analyticsService.getComprehensiveAnalytics(30);

      expect(result).toEqual(mockResult);
      expect(mockAnalyticsService.getComprehensiveAnalytics).toHaveBeenCalledWith(30);
    });
  });
});
