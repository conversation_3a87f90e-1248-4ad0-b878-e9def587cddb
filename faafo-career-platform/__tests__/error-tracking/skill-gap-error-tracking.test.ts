/**
 * Skill Gap Error Tracking Tests
 * 
 * Tests Skill Gap Error Tracking functionality, business logic, and edge cases.
 * 
 * @category unit
 * @requires Unit testing utilities, mocking
 */

import { SkillGapErrorTracker } from '@/lib/error-tracking/skill-gap-error-tracker';
import { errorTracker, trackError } from '@/lib/errorTracking';
import { ErrorReporter } from '@/lib/errorReporting';

// Mock the error tracking dependencies
jest.mock('@/lib/errorTracking');
jest.mock('@/lib/errorReporting');

const mockErrorTracker = {
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  setUser: jest.fn(),
  addBreadcrumb: jest.fn(),
};

const mockTrackError = {
  api: jest.fn(),
  auth: jest.fn(),
  database: jest.fn(),
  validation: jest.fn(),
  ui: jest.fn(),
  performance: jest.fn(),
};

const mockErrorReporter = {
  captureError: jest.fn(),
  captureMessage: jest.fn(),
  addBreadcrumb: jest.fn(),
  setUser: jest.fn(),
  measureAsync: jest.fn(),
};

// Mock the actual modules
(errorTracker as jest.Mocked<typeof errorTracker>) = mockErrorTracker as any;
(trackError as jest.Mocked<typeof trackError>) = mockTrackError as any;
(ErrorReporter as jest.MockedClass<typeof ErrorReporter>) = mockErrorReporter as any;

describe('Skill Gap Analyzer Error Tracking - TDD', () => {
  let skillGapErrorTracker: SkillGapErrorTracker;

  beforeEach(() => {
    jest.clearAllMocks();
    skillGapErrorTracker = new SkillGapErrorTracker();
  });

  describe('Skill Search Error Tracking', () => {
    it('should track skill search API errors with context', async () => {
      // Test: Skill search errors should be tracked with relevant context
      const error = new Error('Skill search failed');
      const context = {
        userId: 'user-123',
        searchQuery: 'javascript',
        searchFilters: { category: 'Programming' },
        responseTime: 5000,
      };

      await skillGapErrorTracker.trackSkillSearchError(error, context);

      expect(mockTrackError.api).toHaveBeenCalledWith(
        error,
        '/api/skills/search',
        'GET',
        500
      );

      expect(mockErrorTracker.captureException).toHaveBeenCalledWith(error, {
        tags: {
          errorType: 'skill_search',
          searchQuery: 'javascript',
          category: 'Programming',
          slowResponse: 'true',
        },
        extra: {
          userId: 'user-123',
          searchQuery: 'javascript',
          searchFilters: { category: 'Programming' },
          responseTime: 5000,
          timestamp: expect.any(String),
        },
        user: {
          id: 'user-123',
        },
      });
    });

    it('should track skill search timeout errors', async () => {
      // Test: Timeout errors should be tracked with specific timeout context
      const timeoutError = new Error('Request timeout');
      const context = {
        userId: 'user-456',
        searchQuery: 'react',
        timeoutDuration: 10000,
        retryAttempt: 2,
      };

      await skillGapErrorTracker.trackSkillSearchTimeout(timeoutError, context);

      expect(mockErrorTracker.captureException).toHaveBeenCalledWith(timeoutError, {
        tags: {
          errorType: 'skill_search_timeout',
          searchQuery: 'react',
          retryAttempt: '2',
          severity: 'high',
        },
        extra: {
          userId: 'user-456',
          searchQuery: 'react',
          timeoutDuration: 10000,
          retryAttempt: 2,
          recommendedAction: 'retry_with_simpler_query',
        },
        user: {
          id: 'user-456',
        },
      });
    });

    it('should track skill search cache miss patterns', async () => {
      // Test: Cache miss patterns should be tracked for optimization
      const cacheMissData = {
        searchQuery: 'python',
        userId: 'user-789',
        cacheKey: 'skill_search:python',
        missReason: 'expired',
        searchFrequency: 'high',
      };

      await skillGapErrorTracker.trackSkillSearchCacheMiss(cacheMissData);

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'Skill search cache miss for high-frequency query',
        {
          level: 'warning',
          tags: {
            errorType: 'cache_miss',
            searchQuery: 'python',
            missReason: 'expired',
            frequency: 'high',
          },
          extra: {
            userId: 'user-789',
            cacheKey: 'skill_search:python',
            searchFrequency: 'high',
            optimizationSuggestion: 'increase_cache_ttl',
          },
        }
      );
    });
  });

  describe('Skill Assessment Error Tracking', () => {
    it('should track skill assessment submission errors', async () => {
      // Test: Assessment submission errors should be tracked with assessment context
      const error = new Error('Assessment submission failed');
      const context = {
        userId: 'user-assessment',
        assessmentData: {
          skillCount: 5,
          assessmentType: 'SELF_ASSESSMENT',
          totalTime: 300000, // 5 minutes
        },
        validationErrors: ['Invalid skill rating'],
        submissionAttempt: 1,
      };

      await skillGapErrorTracker.trackAssessmentSubmissionError(error, context);

      expect(mockTrackError.api).toHaveBeenCalledWith(
        error,
        '/api/skills/assessment',
        'POST',
        400
      );

      expect(mockErrorTracker.captureException).toHaveBeenCalledWith(error, {
        tags: {
          errorType: 'assessment_submission',
          assessmentType: 'SELF_ASSESSMENT',
          skillCount: '5',
          hasValidationErrors: 'true',
        },
        extra: {
          userId: 'user-assessment',
          assessmentData: {
            skillCount: 5,
            assessmentType: 'SELF_ASSESSMENT',
            totalTime: 300000,
          },
          validationErrors: ['Invalid skill rating'],
          submissionAttempt: 1,
          userExperience: 'poor',
        },
        user: {
          id: 'user-assessment',
        },
      });
    });

    it('should track assessment validation errors', async () => {
      // Test: Validation errors should be tracked for data quality monitoring
      const validationError = new Error('Invalid skill rating: must be between 1-10');
      const context = {
        userId: 'user-validation',
        field: 'selfRating',
        value: 15,
        skillName: 'JavaScript',
        expectedRange: [1, 10],
      };

      await skillGapErrorTracker.trackAssessmentValidationError(validationError, context);

      expect(mockTrackError.validation).toHaveBeenCalledWith(
        validationError,
        'selfRating',
        15
      );

      expect(mockErrorTracker.captureException).toHaveBeenCalledWith(validationError, {
        tags: {
          errorType: 'assessment_validation',
          field: 'selfRating',
          skillName: 'JavaScript',
          validationType: 'range_check',
        },
        extra: {
          userId: 'user-validation',
          field: 'selfRating',
          value: 15,
          expectedRange: [1, 10],
          skillName: 'JavaScript',
          dataQualityImpact: 'high',
        },
        user: {
          id: 'user-validation',
        },
      });
    });
  });

  describe('AI Service Error Tracking', () => {
    it('should track comprehensive skill analysis AI errors', async () => {
      // Test: AI service errors should be tracked with AI-specific context
      const aiError = new Error('AI service rate limit exceeded');
      const context = {
        userId: 'user-ai',
        analysisRequest: {
          skillCount: 10,
          targetCareerPath: 'Full Stack Developer',
          analysisType: 'comprehensive',
        },
        aiProvider: 'gemini',
        requestId: 'req-12345',
        rateLimitInfo: {
          limit: 100,
          remaining: 0,
          resetTime: Date.now() + 3600000,
        },
      };

      await skillGapErrorTracker.trackAIServiceError(aiError, context);

      expect(mockErrorTracker.captureException).toHaveBeenCalledWith(aiError, {
        tags: {
          errorType: 'ai_service',
          aiProvider: 'gemini',
          analysisType: 'comprehensive',
          errorCategory: 'rate_limit',
          severity: 'high',
        },
        extra: {
          userId: 'user-ai',
          analysisRequest: {
            skillCount: 10,
            targetCareerPath: 'Full Stack Developer',
            analysisType: 'comprehensive',
          },
          requestId: 'req-12345',
          rateLimitInfo: {
            limit: 100,
            remaining: 0,
            resetTime: expect.any(Number),
          },
          retryStrategy: 'exponential_backoff',
          estimatedRetryTime: expect.any(Number),
        },
        user: {
          id: 'user-ai',
        },
      });
    });

    it('should track AI response quality issues', async () => {
      // Test: Poor AI response quality should be tracked for model improvement
      const qualityIssue = {
        userId: 'user-quality',
        requestId: 'req-quality-123',
        analysisType: 'skill_gap',
        qualityMetrics: {
          responseCompleteness: 0.3, // 30% complete
          responseRelevance: 0.5,    // 50% relevant
          responseAccuracy: 0.4,     // 40% accurate
        },
        expectedQuality: {
          completeness: 0.9,
          relevance: 0.9,
          accuracy: 0.8,
        },
        responseData: {
          skillGapsFound: 2,
          expectedSkillGaps: 8,
          learningPlanQuality: 'poor',
        },
      };

      await skillGapErrorTracker.trackAIResponseQuality(qualityIssue);

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'AI response quality below threshold',
        {
          level: 'warning',
          tags: {
            errorType: 'ai_quality',
            analysisType: 'skill_gap',
            qualityIssue: 'low_completeness',
            severity: 'medium',
          },
          extra: {
            userId: 'user-quality',
            requestId: 'req-quality-123',
            qualityMetrics: {
              responseCompleteness: 0.3,
              responseRelevance: 0.5,
              responseAccuracy: 0.4,
            },
            qualityGaps: {
              completeness: 0.6, // 0.9 - 0.3
              relevance: 0.4,    // 0.9 - 0.5
              accuracy: 0.4,     // 0.8 - 0.4
            },
            improvementActions: [
              'review_prompt_engineering',
              'adjust_model_parameters',
              'enhance_context_data',
            ],
          },
        }
      );
    });
  });

  describe('Performance Error Tracking', () => {
    it('should track slow skill gap analysis performance', async () => {
      // Test: Performance issues should be tracked with timing context
      const performanceData = {
        operation: 'comprehensive_skill_analysis',
        userId: 'user-perf',
        responseTime: 15000, // 15 seconds
        threshold: 5000,     // 5 seconds
        skillCount: 20,
        analysisComplexity: 'high',
        cacheHit: false,
      };

      await skillGapErrorTracker.trackPerformanceIssue(performanceData);

      expect(mockTrackError.performance).toHaveBeenCalledWith(
        'Skill gap analysis exceeded performance threshold',
        'response_time',
        15000,
        5000
      );

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'Performance degradation in skill gap analysis',
        {
          level: 'warning',
          tags: {
            errorType: 'performance',
            operation: 'comprehensive_skill_analysis',
            severity: 'high',
            cacheStatus: 'miss',
          },
          extra: {
            userId: 'user-perf',
            responseTime: 15000,
            threshold: 5000,
            exceedanceRatio: 3, // 15000 / 5000
            skillCount: 20,
            analysisComplexity: 'high',
            optimizationSuggestions: [
              'implement_result_caching',
              'optimize_ai_prompt',
              'reduce_analysis_scope',
            ],
          },
        }
      );
    });

    it('should track memory usage issues', async () => {
      // Test: Memory usage spikes should be tracked
      const memoryData = {
        operation: 'bulk_skill_assessment',
        userId: 'user-memory',
        memoryUsage: 150 * 1024 * 1024, // 150MB
        memoryLimit: 100 * 1024 * 1024,  // 100MB
        assessmentCount: 50,
        processingTime: 8000,
      };

      await skillGapErrorTracker.trackMemoryUsage(memoryData);

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'Memory usage exceeded limit during skill assessment processing',
        {
          level: 'error',
          tags: {
            errorType: 'memory_usage',
            operation: 'bulk_skill_assessment',
            severity: 'high',
          },
          extra: {
            userId: 'user-memory',
            memoryUsage: 150 * 1024 * 1024,
            memoryLimit: 100 * 1024 * 1024,
            memoryExceedance: 50 * 1024 * 1024,
            assessmentCount: 50,
            processingTime: 8000,
            memoryPerAssessment: 3 * 1024 * 1024, // ~3MB per assessment
            recommendations: [
              'implement_streaming_processing',
              'reduce_batch_size',
              'optimize_data_structures',
            ],
          },
        }
      );
    });
  });

  describe('User Experience Error Tracking', () => {
    it('should track user workflow abandonment', async () => {
      // Test: User abandonment patterns should be tracked for UX improvement
      const abandonmentData = {
        userId: 'user-abandon',
        workflowType: 'skill_gap_analysis',
        abandonmentStage: 'skill_assessment',
        timeSpent: 180000, // 3 minutes
        completionPercentage: 40,
        lastAction: 'skill_rating_input',
        sessionData: {
          deviceType: 'mobile',
          browserType: 'chrome',
          connectionSpeed: 'slow',
        },
      };

      await skillGapErrorTracker.trackUserWorkflowAbandonment(abandonmentData);

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'User abandoned skill gap analysis workflow',
        {
          level: 'info',
          tags: {
            errorType: 'user_abandonment',
            workflowType: 'skill_gap_analysis',
            abandonmentStage: 'skill_assessment',
            deviceType: 'mobile',
            uxImpact: 'high',
          },
          extra: {
            userId: 'user-abandon',
            timeSpent: 180000,
            completionPercentage: 40,
            lastAction: 'skill_rating_input',
            sessionData: {
              deviceType: 'mobile',
              browserType: 'chrome',
              connectionSpeed: 'slow',
            },
            possibleCauses: [
              'slow_connection_on_mobile',
              'complex_assessment_form',
              'unclear_instructions',
            ],
            improvementSuggestions: [
              'optimize_mobile_experience',
              'add_progress_indicators',
              'implement_auto_save',
            ],
          },
        }
      );
    });

    it('should track form validation frustration', async () => {
      // Test: Repeated validation errors should be tracked as UX issues
      const frustrationData = {
        userId: 'user-frustration',
        formType: 'skill_assessment',
        validationErrors: [
          { field: 'selfRating', attempts: 3, errorType: 'out_of_range' },
          { field: 'skillName', attempts: 2, errorType: 'invalid_format' },
        ],
        totalAttempts: 5,
        timeSpent: 600000, // 10 minutes
        userAgent: 'Mozilla/5.0...',
      };

      await skillGapErrorTracker.trackFormValidationFrustration(frustrationData);

      expect(mockErrorTracker.captureMessage).toHaveBeenCalledWith(
        'User experiencing form validation frustration',
        {
          level: 'warning',
          tags: {
            errorType: 'ux_frustration',
            formType: 'skill_assessment',
            frustrationLevel: 'high',
            validationIssues: 'multiple',
          },
          extra: {
            userId: 'user-frustration',
            validationErrors: [
              { field: 'selfRating', attempts: 3, errorType: 'out_of_range' },
              { field: 'skillName', attempts: 2, errorType: 'invalid_format' },
            ],
            totalAttempts: 5,
            timeSpent: 600000,
            averageTimePerAttempt: 120000, // 2 minutes
            uxImprovements: [
              'add_inline_validation_hints',
              'improve_error_messages',
              'add_field_examples',
            ],
          },
        }
      );
    });
  });

  describe('Error Context and Breadcrumbs', () => {
    it('should add breadcrumbs for skill gap analysis workflow', async () => {
      // Test: Breadcrumbs should be added for workflow tracking
      const workflowStep = {
        userId: 'user-breadcrumb',
        step: 'skill_assessment_started',
        data: {
          skillCount: 5,
          assessmentType: 'SELF_ASSESSMENT',
          estimatedTime: 300,
        },
      };

      await skillGapErrorTracker.addWorkflowBreadcrumb(workflowStep);

      expect(mockErrorTracker.addBreadcrumb).toHaveBeenCalledWith(
        'Skill assessment started',
        'skill_gap_workflow',
        {
          userId: 'user-breadcrumb',
          step: 'skill_assessment_started',
          skillCount: 5,
          assessmentType: 'SELF_ASSESSMENT',
          estimatedTime: 300,
          timestamp: expect.any(String),
        }
      );
    });

    it('should set user context for skill gap analysis', async () => {
      // Test: User context should be set for better error attribution
      const userContext = {
        userId: 'user-context',
        email: '<EMAIL>',
        skillLevel: 'intermediate',
        careerPath: 'Full Stack Developer',
        analysisHistory: {
          totalAnalyses: 3,
          lastAnalysis: '2024-01-15',
          averageSkillCount: 8,
        },
      };

      await skillGapErrorTracker.setUserContext(userContext);

      expect(mockErrorTracker.setUser).toHaveBeenCalledWith({
        id: 'user-context',
        email: '<EMAIL>',
      });

      expect(mockErrorTracker.addBreadcrumb).toHaveBeenCalledWith(
        'User context set for skill gap analysis',
        'user_context',
        {
          skillLevel: 'intermediate',
          careerPath: 'Full Stack Developer',
          analysisHistory: {
            totalAnalyses: 3,
            lastAnalysis: '2024-01-15',
            averageSkillCount: 8,
          },
        }
      );
    });
  });

  describe('Error Aggregation and Reporting', () => {
    it('should generate error summary reports', async () => {
      // Test: Error summaries should be generated for monitoring
      const timeRange = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
      };

      const errorSummary = await skillGapErrorTracker.generateErrorSummary(timeRange);

      expect(errorSummary).toEqual({
        timeRange,
        totalErrors: expect.any(Number),
        errorsByType: expect.any(Object),
        topErrors: expect.any(Array),
        affectedUsers: expect.any(Number),
        performanceIssues: expect.any(Number),
        qualityIssues: expect.any(Number),
        recommendations: expect.any(Array),
        trends: expect.any(Object),
      });
    });

    it('should identify error patterns and trends', async () => {
      // Test: Error patterns should be identified for proactive fixes
      const patternAnalysis = await skillGapErrorTracker.analyzeErrorPatterns();

      expect(patternAnalysis).toEqual({
        commonErrorPatterns: expect.any(Array),
        userSegmentImpacts: expect.any(Object),
        timeBasedTrends: expect.any(Object),
        correlationInsights: expect.any(Array),
        preventionStrategies: expect.any(Array),
        priorityActions: expect.any(Array),
      });
    });
  });
});
