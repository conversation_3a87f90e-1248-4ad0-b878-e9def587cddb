/**
 * Authentication Security Issues Test
 * Tests for real authentication vulnerabilities and edge cases
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { signIn } from 'next-auth/react';

// Mock NextAuth signIn function
jest.mock('next-auth/react', () => ({
  ...jest.requireActual('next-auth/react'),
  signIn: jest.fn(),
}));

const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;

// Mock components for testing
const MockLoginPage = () => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [error, setError] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Use NextAuth signIn function
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
      }
    } catch (err) {
      setError('Network error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} data-testid="login-form">
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
        data-testid="email-input"
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
        data-testid="password-input"
      />
      <button type="submit" disabled={isLoading} data-testid="submit-button">
        {isLoading ? 'Signing in...' : 'Sign In'}
      </button>
      {error && <div data-testid="error-message">{error}</div>}
    </form>
  );
};

describe('Authentication Security Issues', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock session as unauthenticated by default
    global.mockUseSession.mockReturnValue({
      data: null,
      status: 'unauthenticated'
    });
  });

  test('should reject empty credentials', async () => {
    // Mock signIn to return error for empty credentials
    mockSignIn.mockResolvedValue({
      error: 'Email and password are required',
      status: 400,
      ok: false,
      url: null
    });

    render(<MockLoginPage />);

    const submitButton = screen.getByTestId('submit-button');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });

    expect(mockSignIn).toHaveBeenCalledWith('credentials', {
      email: '',
      password: '',
      redirect: false,
    });
  });

  test('should reject invalid email format', async () => {
    // Mock signIn to return error for invalid email
    mockSignIn.mockResolvedValue({
      error: 'Invalid email format',
      status: 400,
      ok: false,
      url: null
    });

    render(<MockLoginPage />);

    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');
    const submitButton = screen.getByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid email format');
    });

    expect(mockSignIn).toHaveBeenCalledWith('credentials', {
      email: 'invalid-email',
      password: 'password123',
      redirect: false,
    });
  });

  test('should handle SQL injection attempts', async () => {
    // Mock signIn to properly handle SQL injection attempts
    mockSignIn.mockResolvedValue({
      error: 'Invalid credentials',
      status: 400,
      ok: false,
      url: null
    });

    render(<MockLoginPage />);

    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');
    const submitButton = screen.getByTestId('submit-button');

    // Try SQL injection
    fireEvent.change(emailInput, { target: { value: "admin'; DROP TABLE users; --" } });
    fireEvent.change(passwordInput, { target: { value: "' OR '1'='1" } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });

    // Should reject the malicious input
    expect(mockSignIn).toHaveBeenCalledWith('credentials', {
      email: "admin'; DROP TABLE users; --",
      password: "' OR '1'='1",
      redirect: false,
    });
  });

  test('should handle rate limiting', async () => {
    // Mock signIn to return rate limit error
    mockSignIn.mockResolvedValue({
      error: 'Too many login attempts. Please try again later.',
      status: 429,
      ok: false,
      url: null
    });

    render(<MockLoginPage />);

    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');
    const submitButton = screen.getByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Too many login attempts');
    });
  });

  test('should handle network errors gracefully', async () => {
    // Mock network error
    mockSignIn.mockRejectedValue(new Error('Network error'));

    render(<MockLoginPage />);

    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');
    const submitButton = screen.getByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('Network error');
    });
  });

  test('should prevent multiple simultaneous submissions', async () => {
    // Mock slow signIn response
    mockSignIn.mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({
        error: null,
        status: 200,
        ok: true,
        url: '/dashboard'
      }), 1000))
    );

    render(<MockLoginPage />);

    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');
    const submitButton = screen.getByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });

    // Click submit button
    fireEvent.click(submitButton);

    // Button should be disabled during loading
    expect(submitButton).toBeDisabled();
    expect(submitButton).toHaveTextContent('Signing in...');

    // Try to click again - should not trigger another request
    fireEvent.click(submitButton);

    // Should only have been called once
    expect(mockSignIn).toHaveBeenCalledTimes(1);
  });
});
