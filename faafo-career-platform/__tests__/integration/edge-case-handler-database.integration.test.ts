/**
 * EdgeCaseHandler Database Integration Tests
 *
 * Tests EdgeCaseHandler with database integration, focusing on the actual
 * methods available in EdgeCaseHandlerService and their database interactions.
 */

import { EdgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';

// Mock the dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    skill: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
    },
    skillAssessment: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
    careerPath: {
      findFirst: jest.fn(),
    },
    learningPath: {
      create: jest.fn(),
    },
    skillMarketData: {
      create: jest.fn(),
    },
  },
}));

jest.mock('@/lib/performance/SkillGapPerformanceOptimizer', () => ({
  skillGapPerformanceOptimizer: {
    getUser: jest.fn(),
    getSkills: jest.fn(),
    getMarketData: jest.fn(),
    getSkillByName: jest.fn(),
    getPerformanceMetrics: jest.fn(() => ({
      totalRequests: 100,
      averageResponseTime: 250,
      errorRate: 0.02
    })),
    getCacheStats: jest.fn(() => ({
      hitRate: 0.85,
      totalHits: 850,
      totalMisses: 150
    })),
    clearCaches: jest.fn(),
  },
}));

describe('EdgeCaseHandler Database Integration', () => {
  let edgeCaseHandlerService: EdgeCaseHandlerService;
  let mockPrisma: any;
  let mockPerformanceOptimizer: any;

  const testUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User'
  };

  const testSkills = [
    {
      id: 'skill-js-123',
      name: 'JavaScript',
      category: 'PROGRAMMING',
      description: 'JavaScript programming language'
    },
    {
      id: 'skill-react-456',
      name: 'React',
      category: 'FRONTEND',
      description: 'React framework'
    }
  ];

  beforeAll(async () => {
    // Initialize EdgeCaseHandler service
    edgeCaseHandlerService = EdgeCaseHandlerService.getInstance();

    // Get mock instances
    mockPrisma = require('@/lib/prisma').prisma;
    mockPerformanceOptimizer = require('@/lib/performance/SkillGapPerformanceOptimizer').skillGapPerformanceOptimizer;
  });

  beforeEach(async () => {
    // Reset mocks for clean test state
    jest.clearAllMocks();
  });

  describe('Skill Assessment Database Integration', () => {
    it('should create skill assessment with database integration', async () => {
      // Arrange
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [testSkills[0].id],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: 'career-123'
      };

      // Mock successful user lookup
      mockPerformanceOptimizer.getUser.mockResolvedValue(testUser);

      // Mock successful skill lookup
      mockPerformanceOptimizer.getSkills.mockResolvedValue([testSkills[0]]);

      // Mock successful assessment creation
      const mockAssessment = {
        id: 'assessment-123',
        userId: testUser.id,
        skillId: testSkills[0].id,
        rating: 7,
        confidence: 8,
        assessmentDate: new Date(),
        isActive: true
      };
      mockPrisma.skillAssessment.create.mockResolvedValue(mockAssessment);

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Verify the service called the performance optimizer methods
      expect(mockPerformanceOptimizer.getUser).toHaveBeenCalledWith(testUser.id);
      expect(mockPerformanceOptimizer.getSkills).toHaveBeenCalledWith([testSkills[0].id]);
    });

    it('should handle invalid skill IDs with database validation', async () => {
      // Arrange
      const invalidSkillId = 'invalid-skill-id-12345';
      const assessmentRequest = {
        userId: testUser.id,
        skillIds: [invalidSkillId],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: 'career-123'
      };

      // Mock successful user lookup
      mockPerformanceOptimizer.getUser.mockResolvedValue(testUser);

      // Mock empty skill lookup (skill not found)
      mockPerformanceOptimizer.getSkills.mockResolvedValue([]);

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('BUSINESS_LOGIC_ERROR');
      expect(result.error).toContain('Skills not found');
      expect(result.suggestedAlternatives).toBeDefined();

      // Verify skill lookup was attempted
      expect(mockPerformanceOptimizer.getSkills).toHaveBeenCalledWith([invalidSkillId]);
    });

    it('should handle user not found error', async () => {
      // Arrange
      const assessmentRequest = {
        userId: 'non-existent-user',
        skillIds: [testSkills[0].id],
        assessmentType: 'SELF_ASSESSMENT',
        careerPathId: 'career-123'
      };

      // Mock user not found
      mockPerformanceOptimizer.getUser.mockResolvedValue(null);

      // Act
      const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase(assessmentRequest);

      // Assert
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('VALIDATION_ERROR');
      expect(result.error).toBe('User not found');
    });

  });

  describe('Learning Path Database Integration', () => {
    it('should generate learning path with database integration', async () => {
      // Arrange
      const learningPathRequest = {
        userId: testUser.id,
        targetRole: 'Full Stack Developer',
        currentSkills: [
          { skill: 'JavaScript', level: 7 },
          { skill: 'HTML', level: 8 }
        ],
        timeframe: 6,
        budget: 1000,
        availability: 10
      };

      // Mock successful user lookup
      mockPerformanceOptimizer.getUser.mockResolvedValue(testUser);

      // Mock career path lookup
      const mockCareerPath = {
        id: 'career-123',
        title: 'Full Stack Developer',
        skills: testSkills
      };
      mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPath);

      // Mock learning path creation
      const mockLearningPath = {
        id: 'learning-path-123',
        userId: testUser.id,
        title: 'Learning Path for Full Stack Developer',
        estimatedDuration: 6
      };
      mockPrisma.learningPath.create.mockResolvedValue(mockLearningPath);

      // Act
      const result = await edgeCaseHandlerService.generateLearningPathWithDatabase(learningPathRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Verify database calls
      expect(mockPerformanceOptimizer.getUser).toHaveBeenCalledWith(testUser.id);
      expect(mockPrisma.careerPath.findFirst).toHaveBeenCalled();
    });
  });

  describe('Market Data Database Integration', () => {
    it('should retrieve market data with database integration', async () => {
      // Arrange
      const marketDataRequest = {
        skill: 'JavaScript',
        location: 'San Francisco',
        forceRefresh: false
      };

      // Mock market data retrieval
      const mockMarketData = {
        demand: 85,
        averageSalary: 120000,
        growth: 15
      };
      mockPerformanceOptimizer.getMarketData.mockResolvedValue(mockMarketData);

      // Mock skill lookup
      const mockSkill = { id: 'skill-js-123', name: 'JavaScript' };
      mockPerformanceOptimizer.getSkillByName.mockResolvedValue(mockSkill);

      // Mock market data creation
      const mockMarketDataRecord = {
        id: 'market-data-123',
        skillId: mockSkill.id,
        demand: 85,
        averageSalary: 120000,
        growth: 15
      };
      mockPrisma.skillMarketData.create.mockResolvedValue(mockMarketDataRecord);

      // Act
      const result = await edgeCaseHandlerService.getMarketDataWithDatabase(marketDataRequest);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Verify database calls
      expect(mockPerformanceOptimizer.getMarketData).toHaveBeenCalledWith('JavaScript', 'San Francisco');
    });
  });

  describe('Service Health and Performance', () => {
    it('should provide health status', async () => {
      // Act
      const healthStatus = await edgeCaseHandlerService.getHealthStatus();

      // Assert
      expect(healthStatus).toBeDefined();
      expect(typeof healthStatus).toBe('object');
    });

    it('should provide error statistics', async () => {
      // Act
      const errorStats = await edgeCaseHandlerService.getErrorStatistics();

      // Assert
      expect(errorStats).toBeDefined();
      expect(typeof errorStats).toBe('object');
    });

    it('should provide performance metrics', () => {
      // Act
      const metrics = edgeCaseHandlerService.getPerformanceMetrics();

      // Assert
      expect(metrics).toBeDefined();
      expect(metrics.metrics).toBeDefined();
      expect(metrics.cacheStats).toBeDefined();
    });
  });
});
