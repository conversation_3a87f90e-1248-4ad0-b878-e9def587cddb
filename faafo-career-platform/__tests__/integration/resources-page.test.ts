/**
 * Resources Page Tests
 * 
 * Integration tests for Resources Page covering end-to-end workflows and system interactions.
 * 
 * @category integration
 * @requires Database setup, service mocking
 */

import { TestDatabase } from '../utils/testHelpers';
import { testLearningResources } from '../fixtures/testData';

describe('Resources Page Integration Tests', () => {
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('Category Icon Mapping', () => {
    it('should handle all database category values without errors', async () => {
      // Test all possible database category enum values
      const databaseCategories = [
        'CYBERSECURITY',
        'DATA_SCIENCE',
        'BLOCKCHAIN',
        'PROJECT_MANAGEMENT',
        'DIGITAL_MARKETING',
        'FINANCIAL_LITERACY',
        'LANGUAGE_LEARNING',
        'ARTIFICIAL_INTELLIGENCE',
        'WEB_DEVELOPMENT',
        'MOBILE_DEVELOPMENT',
        'CLOUD_COMPUTING',
        'ENTREPRENEURSHIP'
      ];

      // Create resources with each category
      for (const category of databaseCategories) {
        const resource = await testDb.createTestLearningResource({
          title: `Test Resource for ${category}`,
          description: `A test resource for ${category} category`,
          url: `https://example.com/${category.toLowerCase()}`,
          type: 'COURSE',
          category: category as any,
          skillLevel: 'BEGINNER',
          cost: 'FREE',
          format: 'SELF_PACED'
        });

        expect(resource.category).toBe(category);
      }
    });

    it('should map database categories to UI category keys correctly', () => {
      const getCategoryKey = (category: string) => {
        const categoryMap: { [key: string]: string } = {
          'CYBERSECURITY': 'cybersecurity',
          'DATA_SCIENCE': 'data-science',
          'BLOCKCHAIN': 'blockchain',
          'PROJECT_MANAGEMENT': 'project-management',
          'DIGITAL_MARKETING': 'digital-marketing',
          'ARTIFICIAL_INTELLIGENCE': 'ai',
          'WEB_DEVELOPMENT': 'web-development',
          'MOBILE_DEVELOPMENT': 'mobile-development',
          'CLOUD_COMPUTING': 'cloud-computing',
          'FINANCIAL_LITERACY': 'financial',
          'LANGUAGE_LEARNING': 'language-learning',
          'ENTREPRENEURSHIP': 'entrepreneurship'
        };
        return categoryMap[category] || category.toLowerCase().replace(/_/g, '-');
      };

      // Test mapping for all categories
      expect(getCategoryKey('CYBERSECURITY')).toBe('cybersecurity');
      expect(getCategoryKey('DATA_SCIENCE')).toBe('data-science');
      expect(getCategoryKey('ARTIFICIAL_INTELLIGENCE')).toBe('ai');
      expect(getCategoryKey('WEB_DEVELOPMENT')).toBe('web-development');
      expect(getCategoryKey('MOBILE_DEVELOPMENT')).toBe('mobile-development');
      expect(getCategoryKey('CLOUD_COMPUTING')).toBe('cloud-computing');
      expect(getCategoryKey('FINANCIAL_LITERACY')).toBe('financial');
      expect(getCategoryKey('LANGUAGE_LEARNING')).toBe('language-learning');
      expect(getCategoryKey('ENTREPRENEURSHIP')).toBe('entrepreneurship');
      
      // Test fallback for unknown category
      expect(getCategoryKey('UNKNOWN_CATEGORY')).toBe('unknown-category');
    });

    it('should provide fallback category data for unmapped categories', () => {
      const fallbackCategory = {
        title: 'UNKNOWN_CATEGORY'.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
        icon: 'BookOpen', // Would be the actual BookOpen component
        color: 'text-gray-600 dark:text-gray-400'
      };

      expect(fallbackCategory.title).toBe('Unknown Category');
      expect(fallbackCategory.color).toBe('text-gray-600 dark:text-gray-400');
    });
  });

  describe('Resource Filtering', () => {
    beforeEach(async () => {
      // Create test resources with different categories
      await testDb.createTestLearningResource({
        ...testLearningResources.cybersecurityCourse,
        category: 'CYBERSECURITY'
      });
      
      await testDb.createTestLearningResource({
        ...testLearningResources.dataScienceArticle,
        url: 'https://example.com/data-science-unique',
        category: 'DATA_SCIENCE'
      });
      
      await testDb.createTestLearningResource({
        ...testLearningResources.webDevVideo,
        url: 'https://example.com/web-dev-unique',
        category: 'WEB_DEVELOPMENT'
      });
    });

    it('should filter resources by category correctly', () => {
      // Mock filtering logic
      const mockResources = [
        { id: '1', category: 'CYBERSECURITY', title: 'Security Course' },
        { id: '2', category: 'DATA_SCIENCE', title: 'Data Course' },
        { id: '3', category: 'WEB_DEVELOPMENT', title: 'Web Course' }
      ];

      const filterByCategory = (resources: any[], category: string) => {
        if (category === 'all') return resources;
        
        const categoryMap: { [key: string]: string } = {
          'cybersecurity': 'CYBERSECURITY',
          'data-science': 'DATA_SCIENCE',
          'web-development': 'WEB_DEVELOPMENT'
        };
        
        const dbCategory = categoryMap[category];
        return resources.filter(r => r.category === dbCategory);
      };

      const cybersecurityResources = filterByCategory(mockResources, 'cybersecurity');
      const dataScienceResources = filterByCategory(mockResources, 'data-science');
      const allResources = filterByCategory(mockResources, 'all');

      expect(cybersecurityResources).toHaveLength(1);
      expect(cybersecurityResources[0].category).toBe('CYBERSECURITY');
      
      expect(dataScienceResources).toHaveLength(1);
      expect(dataScienceResources[0].category).toBe('DATA_SCIENCE');
      
      expect(allResources).toHaveLength(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle resources with null or undefined categories', () => {
      const getCategoryKey = (category: string | null | undefined) => {
        if (!category) return 'general';
        
        const categoryMap: { [key: string]: string } = {
          'CYBERSECURITY': 'cybersecurity',
          'DATA_SCIENCE': 'data-science'
        };
        return categoryMap[category] || category.toLowerCase().replace(/_/g, '-');
      };

      expect(getCategoryKey(null)).toBe('general');
      expect(getCategoryKey(undefined)).toBe('general');
      expect(getCategoryKey('')).toBe('general');
    });

    it('should handle malformed category names', () => {
      const getCategoryKey = (category: string) => {
        try {
          const categoryMap: { [key: string]: string } = {
            'CYBERSECURITY': 'cybersecurity'
          };
          return categoryMap[category] || category.toLowerCase().replace(/_/g, '-');
        } catch (error) {
          return 'general';
        }
      };

      // Test with various malformed inputs
      expect(getCategoryKey('VALID_CATEGORY')).toBe('valid-category');
      expect(getCategoryKey('123_INVALID')).toBe('123-invalid');
      expect(getCategoryKey('special!@#characters')).toBe('special!@#characters');
    });
  });

  describe('UI Category Information', () => {
    it('should have complete category information for all mapped categories', () => {
      const categoryInfo = {
        'cybersecurity': {
          title: 'Cybersecurity',
          icon: 'Shield',
          description: 'Protect digital assets and learn security practices.',
          color: 'text-red-600 dark:text-red-400'
        },
        'data-science': {
          title: 'Data Science',
          icon: 'BarChart3',
          description: 'Analyze data and build predictive models.',
          color: 'text-blue-600 dark:text-blue-400'
        },
        'ai': {
          title: 'Artificial Intelligence',
          icon: 'Brain',
          description: 'Build intelligent systems and machine learning models.',
          color: 'text-purple-600 dark:text-purple-400'
        },
        'web-development': {
          title: 'Web Development',
          icon: 'Code',
          description: 'Learn frontend and backend development skills.',
          color: 'text-indigo-600 dark:text-indigo-400'
        }
      };

      // Verify each category has required properties
      Object.values(categoryInfo).forEach(category => {
        expect(category).toHaveProperty('title');
        expect(category).toHaveProperty('icon');
        expect(category).toHaveProperty('description');
        expect(category).toHaveProperty('color');
        
        expect(typeof category.title).toBe('string');
        expect(typeof category.icon).toBe('string');
        expect(typeof category.description).toBe('string');
        expect(typeof category.color).toBe('string');
        
        expect(category.title.length).toBeGreaterThan(0);
        expect(category.description.length).toBeGreaterThan(0);
        expect(category.color).toMatch(/^text-\w+-\d+/);
      });
    });
  });
});
