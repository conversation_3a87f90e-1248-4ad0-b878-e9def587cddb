/**
 * User Flows Tests
 * 
 * End-to-end tests for User Flows covering complete user workflows and browser interactions.
 * 
 * @category e2e
 * @requires Play<PERSON>, browser automation
 */

import { TestDatabase, measureExecutionTime } from '../utils/testHelpers';
import { testUsers, testAssessments, testFormData } from '../fixtures/testData';

// Mock fetch for API calls
const createMockFetch = (responses: Array<{ url?: string; response: any; status?: number }>) => {
  return jest.fn().mockImplementation((url: string, options?: any) => {
    const mockResponse = responses.find(r => !r.url || url.includes(r.url)) || responses[0];
    
    return Promise.resolve({
      ok: (mockResponse.status || 200) < 400,
      status: mockResponse.status || 200,
      json: () => Promise.resolve(mockResponse.response),
      text: () => Promise.resolve(JSON.stringify(mockResponse.response)),
      headers: new Map([['content-type', 'application/json']])
    });
  });
};

describe('End-to-End User Flows', () => {
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('Complete User Registration and Onboarding Flow', () => {
    it('should complete full user registration to dashboard flow', async () => {
      const { result: flowResult, executionTime } = await measureExecutionTime(async () => {
        // Step 1: User Registration
        global.fetch = createMockFetch([
          {
            url: '/api/signup',
            response: { message: 'User created successfully' },
            status: 201
          }
        ]);

        const signupResponse = await fetch('/api/signup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testFormData.validSignupForm)
        });

        expect(signupResponse.status).toBe(201);

        // Step 2: User Login (simulated)
        const user = await testDb.createTestUser(testUsers.validUser);
        
        // Step 3: Assessment Creation
        global.fetch = createMockFetch([
          {
            url: '/api/assessment',
            response: {
              currentStep: 0,
              formData: {},
              status: 'IN_PROGRESS',
              id: 'new-assessment-id'
            }
          }
        ]);

        const assessmentResponse = await fetch('/api/assessment');
        const assessmentData = await assessmentResponse.json();
        
        expect(assessmentData.status).toBe('IN_PROGRESS');

        // Step 4: Complete Assessment
        global.fetch = createMockFetch([
          {
            url: '/api/assessment',
            response: {
              message: 'Assessment progress saved successfully.',
              assessmentId: 'assessment-id',
              status: 'COMPLETED'
            }
          }
        ]);

        const completeAssessmentResponse = await fetch('/api/assessment', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            assessmentId: 'assessment-id',
            formData: testAssessments.completeAssessment
          })
        });

        expect(completeAssessmentResponse.status).toBe(200);

        // Step 5: Get Personalized Resources
        global.fetch = createMockFetch([
          {
            url: '/api/personalized-resources',
            response: {
              success: true,
              data: [
                {
                  id: '1',
                  title: 'Recommended Course',
                  category: 'CYBERSECURITY',
                  skillLevel: 'BEGINNER'
                }
              ]
            }
          }
        ]);

        const resourcesResponse = await fetch('/api/personalized-resources?limit=6');
        const resourcesData = await resourcesResponse.json();
        
        expect(resourcesData.success).toBe(true);
        expect(resourcesData.data).toHaveLength(1);

        return {
          userCreated: true,
          assessmentCompleted: true,
          resourcesLoaded: true
        };
      });

      expect(flowResult.userCreated).toBe(true);
      expect(flowResult.assessmentCompleted).toBe(true);
      expect(flowResult.resourcesLoaded).toBe(true);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle registration errors gracefully', async () => {
      // Test duplicate email registration
      await testDb.createTestUser(testUsers.validUser);

      global.fetch = createMockFetch([
        {
          url: '/api/signup',
          response: { message: 'User already exists' },
          status: 409
        }
      ]);

      const signupResponse = await fetch('/api/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testUsers.validUser.email,
          password: 'NewPassword123!'
        })
      });

      expect(signupResponse.status).toBe(409);
      const data = await signupResponse.json();
      expect(data.message).toBe('User already exists');
    });
  });

  describe('Assessment Completion Flow', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await testDb.createTestUser(testUsers.validUser);
    });

    it('should complete step-by-step assessment flow', async () => {
      const steps = [
        { step: 1, data: { dissatisfaction_triggers: ['lack_of_growth'] } },
        { step: 2, data: { desired_outcomes_skill_a: 'high' } },
        { step: 3, data: { work_environment_preference: 'remote' } },
        { step: 4, data: { risk_tolerance: 'medium' } },
        { step: 5, data: { learning_style: 'hands_on' } },
        { step: 6, data: { time_commitment: '10_15_hours' } }
      ];

      let assessmentId = 'test-assessment-id';

      for (const stepData of steps) {
        global.fetch = createMockFetch([
          {
            url: '/api/assessment',
            response: {
              message: 'Assessment progress saved successfully.',
              assessmentId,
              status: stepData.step === 6 ? 'COMPLETED' : 'IN_PROGRESS'
            }
          }
        ]);

        const response = await fetch('/api/assessment', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            currentStep: stepData.step,
            formData: stepData.data,
            status: stepData.step === 6 ? 'COMPLETED' : 'IN_PROGRESS'
          })
        });

        expect(response.status).toBe(200);
        const data = await response.json();
        expect(data.assessmentId).toBe(assessmentId);
      }
    });

    it('should handle assessment interruption and resumption', async () => {
      // Start assessment
      global.fetch = createMockFetch([
        {
          url: '/api/assessment',
          response: {
            message: 'Assessment progress saved successfully.',
            assessmentId: 'interrupted-assessment',
            status: 'IN_PROGRESS'
          }
        }
      ]);

      const startResponse = await fetch('/api/assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          currentStep: 2,
          formData: testAssessments.partialAssessment
        })
      });

      expect(startResponse.status).toBe(200);

      // Resume assessment
      global.fetch = createMockFetch([
        {
          url: '/api/assessment',
          response: {
            currentStep: 2,
            formData: testAssessments.partialAssessment,
            status: 'IN_PROGRESS',
            id: 'interrupted-assessment'
          }
        }
      ]);

      const resumeResponse = await fetch('/api/assessment');
      const resumeData = await resumeResponse.json();

      expect(resumeData.currentStep).toBe(2);
      expect(resumeData.status).toBe('IN_PROGRESS');
    });
  });

  describe('Learning Resource Interaction Flow', () => {
    let testUser: any;
    let testResource: any;

    beforeEach(async () => {
      testUser = await testDb.createTestUser(testUsers.validUser);
      testResource = await testDb.createTestLearningResource({
        title: 'Test Learning Resource',
        description: 'A test resource for E2E testing',
        url: 'https://example.com/test-resource',
        type: 'COURSE',
        category: 'CYBERSECURITY',
        skillLevel: 'BEGINNER',
        cost: 'FREE',
        format: 'SELF_PACED'
      });
    });

    it('should complete resource discovery to completion flow', async () => {
      // Step 1: Discover resources
      global.fetch = createMockFetch([
        {
          url: '/api/learning-resources',
          response: {
            success: true,
            data: [testResource],
            count: 1
          }
        }
      ]);

      const discoveryResponse = await fetch('/api/learning-resources?category=CYBERSECURITY');
      const discoveryData = await discoveryResponse.json();

      expect(discoveryData.success).toBe(true);
      expect(discoveryData.data).toHaveLength(1);

      // Step 2: Start learning (bookmark/progress)
      global.fetch = createMockFetch([
        {
          url: '/api/learning-progress',
          response: {
            success: true,
            message: 'Progress updated successfully'
          }
        }
      ]);

      const startLearningResponse = await fetch('/api/learning-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resourceId: testResource.id,
          status: 'IN_PROGRESS',
          notes: 'Started learning this resource'
        })
      });

      expect(startLearningResponse.status).toBe(200);

      // Step 3: Complete and rate resource
      global.fetch = createMockFetch([
        {
          url: '/api/learning-progress',
          response: {
            success: true,
            message: 'Progress updated successfully'
          }
        }
      ]);

      const completeResponse = await fetch('/api/learning-progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resourceId: testResource.id,
          status: 'COMPLETED',
          rating: 5,
          review: 'Excellent resource, learned a lot!'
        })
      });

      expect(completeResponse.status).toBe(200);
    });

    it('should handle resource filtering and search', async () => {
      // Create multiple resources for filtering
      const resources = [
        { ...testResource, id: '1', category: 'CYBERSECURITY', skillLevel: 'BEGINNER' },
        { ...testResource, id: '2', category: 'DATA_SCIENCE', skillLevel: 'INTERMEDIATE' },
        { ...testResource, id: '3', category: 'WEB_DEVELOPMENT', skillLevel: 'ADVANCED' }
      ];

      // Test category filtering
      global.fetch = createMockFetch([
        {
          url: '/api/learning-resources',
          response: {
            success: true,
            data: resources.filter(r => r.category === 'CYBERSECURITY'),
            count: 1
          }
        }
      ]);

      const categoryResponse = await fetch('/api/learning-resources?category=CYBERSECURITY');
      const categoryData = await categoryResponse.json();

      expect(categoryData.data).toHaveLength(1);
      expect(categoryData.data[0].category).toBe('CYBERSECURITY');

      // Test skill level filtering
      global.fetch = createMockFetch([
        {
          url: '/api/learning-resources',
          response: {
            success: true,
            data: resources.filter(r => r.skillLevel === 'BEGINNER'),
            count: 1
          }
        }
      ]);

      const skillResponse = await fetch('/api/learning-resources?skillLevel=BEGINNER');
      const skillData = await skillResponse.json();

      expect(skillData.data).toHaveLength(1);
      expect(skillData.data[0].skillLevel).toBe('BEGINNER');
    });
  });

  describe('Progress Tracking Flow', () => {
    let testUser: any;
    let testResources: any[];

    beforeEach(async () => {
      testUser = await testDb.createTestUser(testUsers.validUser);
      testResources = await Promise.all([
        testDb.createTestLearningResource({ title: 'Resource 1', url: 'https://example.com/1' }),
        testDb.createTestLearningResource({ title: 'Resource 2', url: 'https://example.com/2' }),
        testDb.createTestLearningResource({ title: 'Resource 3', url: 'https://example.com/3' })
      ]);
    });

    it('should track comprehensive learning progress', async () => {
      // Create progress for multiple resources
      const progressUpdates = [
        { resourceId: testResources[0].id, status: 'COMPLETED', rating: 5 },
        { resourceId: testResources[1].id, status: 'IN_PROGRESS', rating: null },
        { resourceId: testResources[2].id, status: 'BOOKMARKED', rating: null }
      ];

      for (const update of progressUpdates) {
        global.fetch = createMockFetch([
          {
            url: '/api/learning-progress',
            response: { success: true, message: 'Progress updated' }
          }
        ]);

        const response = await fetch('/api/learning-progress', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(update)
        });

        expect(response.status).toBe(200);
      }

      // Get progress summary
      global.fetch = createMockFetch([
        {
          url: '/api/progress-tracker',
          response: {
            success: true,
            data: {
              totalResources: 3,
              completedResources: 1,
              inProgressResources: 1,
              bookmarkedResources: 1,
              averageRating: 5.0,
              totalRatings: 1,
              streakDays: 1,
              weeklyGoal: 5,
              weeklyProgress: 1,
              achievements: ['First Completion']
            }
          }
        }
      ]);

      const progressResponse = await fetch('/api/progress-tracker');
      const progressData = await progressResponse.json();

      expect(progressData.success).toBe(true);
      expect(progressData.data.completedResources).toBe(1);
      expect(progressData.data.inProgressResources).toBe(1);
      expect(progressData.data.bookmarkedResources).toBe(1);
    });
  });

  describe('Error Recovery Flows', () => {
    it('should handle network interruptions gracefully', async () => {
      // Simulate network failure
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      try {
        await fetch('/api/assessment');
      } catch (error) {
        expect(error.message).toBe('Network error');
      }

      // Simulate recovery
      global.fetch = createMockFetch([
        {
          response: { success: true, data: 'recovered' }
        }
      ]);

      const recoveryResponse = await fetch('/api/assessment');
      const recoveryData = await recoveryResponse.json();

      expect(recoveryData.success).toBe(true);
    });

    it('should handle server errors with retry logic', async () => {
      let attemptCount = 0;
      global.fetch = jest.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.resolve({
            ok: false,
            status: 500,
            json: () => Promise.resolve({ error: 'Server error' })
          });
        }
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({ success: true })
        });
      });

      // Simulate retry logic
      let response;
      for (let i = 0; i < 3; i++) {
        response = await fetch('/api/test');
        if (response.ok) break;
      }

      expect(response.ok).toBe(true);
      expect(attemptCount).toBe(3);
    });
  });
});
