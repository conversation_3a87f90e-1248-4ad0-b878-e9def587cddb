/**
 * Jest Configuration for Unit Tests
 * Optimized for fast unit test execution
 */

const baseConfig = require('./jest.config.js');

module.exports = {
  ...baseConfig,
  
  // Unit test specific settings - optimized for speed
  testTimeout: 10000, // 10 seconds for unit tests
  maxWorkers: '50%', // Use more workers for parallel execution
  workerIdleMemoryLimit: '256MB', // Lower memory limit for unit tests
  
  // Only run unit tests (exclude performance and architecture)
  testMatch: [
    '**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)',
    '**/*.(test|spec).(ts|tsx|js|jsx)'
  ],
  
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/__tests__/performance/',
    '/src/__tests__/architecture/',
    '/__tests__/integration/',
    '/e2e/',
    '/tests/',
    '/__tests__/e2e/',
    '<rootDir>/e2e/',
    '<rootDir>/tests/',
    '<rootDir>/__tests__/e2e/',
    '\\.spec\\.(ts|js)$',
    '-live-testing\\.spec\\.(ts|js)$',
    '-comprehensive\\.spec\\.(ts|js)$',
    '-edge-cases\\.spec\\.(ts|js)$',
    '-validation\\.spec\\.(ts|js)$',
    '-workflow\\.spec\\.(ts|js)$',
    'playwright',
    '.spec.ts',
    '.spec.js'
  ],
  
  // Enable coverage for unit tests
  collectCoverage: true,

  // Use production-ready coverage configuration
  ...require('./jest.coverage.config.js').getCoverageThreshold ? {
    collectCoverageFrom: require('./jest.coverage.config.js').getCollectCoverageFrom(),
    coverageReporters: require('./jest.coverage.config.js').getCoverageReporters(),
    coverageThreshold: require('./jest.coverage.config.js').getCoverageThreshold(),
  } : {
    // Fallback for unit tests
    collectCoverageFrom: [
      'src/**/*.{ts,tsx}',
      'components/**/*.{ts,tsx}',
      'lib/**/*.{ts,tsx}',
      'middleware.ts',
      '!src/**/*.d.ts',
      '!**/__tests__/**',
      '!**/node_modules/**',
      '!**/coverage/**',
      '!jest.config*.js',
      '!jest.setup.js',
      '!jest.polyfills.js',
      '!**/test-utils/**',
    ],
    coverageReporters: ['text-summary', 'lcov'],
    coverageThreshold: {
      global: {
        branches: 60,
        functions: 70,
        lines: 70,
        statements: 70
      }
    }
  },
  
  // Optimized for speed
  verbose: false,
  silent: false,
  
  // Fast reporters
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test-results/unit',
      outputName: 'unit-junit.xml',
    }]
  ],
  
  // Standard jsdom environment for React components
  testEnvironment: 'jsdom',

  // Use the optimized setup without aggressive DOM cleanup
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.ts'
  ],
  
  // Transform configuration (updated for ts-jest v29+)
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx',
      },
    }],
  },

  // Global test configuration
  globals: {
    UNIT_TEST_MODE: true,
    FAST_TEST_MODE: true,
  },
};
