# Execute Product Requirements Prompt (PRP)

## **Command Purpose**
Execute a comprehensive Product Requirements Prompt (PRP) to implement a feature systematically with validation gates and quality assurance. This command transforms a detailed PRP blueprint into working, tested code.

## **Usage**
```
/execute-prp PRPs/[feature-name].md
```

## **Execution Process**

### **1. Context Loading**
**Read Complete PRP:** Load and analyze the specified PRP file: `$ARGUMENTS`

**Validate Prerequisites:**
- Confirm all dependencies are available
- Verify database connectivity and schema state
- Check authentication and authorization systems
- Validate development environment setup

**Create Task Plan:**
- Break down implementation into 20-minute focused tasks
- Establish validation gates for each phase
- Set up progress tracking and rollback points
- Define success criteria for each milestone

### **2. Phase-by-Phase Implementation**

#### **Phase 1: Foundation Setup**
**Database Schema:**
```typescript
// Create Prisma schema updates
// Run migrations: npx prisma migrate dev
// Validate schema integrity
```

**Core Models:**
```typescript
// Implement TypeScript interfaces
// Create Zod validation schemas
// Set up error handling patterns
```

**Validation Gate 1:**
- [ ] Database migration successful
- [ ] Schema validation passes
- [ ] Core types compile without errors

#### **Phase 2: API Implementation**
**Backend Services:**
```typescript
// Implement API routes with unified error handling
// Add authentication and authorization
// Create service layer with business logic
```

**Testing:**
```typescript
// Unit tests for all service functions
// Integration tests for API endpoints
// Validation of error scenarios
```

**Validation Gate 2:**
- [ ] All API endpoints respond correctly
- [ ] Authentication/authorization working
- [ ] >95% test coverage achieved
- [ ] Performance targets met (<500ms)

#### **Phase 3: Frontend Components**
**UI Components:**
```typescript
// Create reusable components following project patterns
// Implement proper TypeScript interfaces
// Add accessibility features
```

**State Management:**
```typescript
// Implement data fetching and caching
// Add optimistic updates where appropriate
// Handle loading and error states
```

**Validation Gate 3:**
- [ ] Components render without errors
- [ ] Responsive design verified
- [ ] Accessibility standards met
- [ ] User interactions working

#### **Phase 4: Integration & Testing**
**End-to-End Flows:**
```typescript
// Implement complete user journeys
// Test integration points
// Validate data consistency
```

**Performance Optimization:**
```typescript
// Optimize database queries
// Implement caching strategies
// Minimize bundle size impact
```

**Validation Gate 4:**
- [ ] Complete user flows working
- [ ] Performance benchmarks met
- [ ] No console errors or warnings
- [ ] Cross-browser compatibility verified

#### **Phase 5: Quality Assurance**
**Comprehensive Testing:**
```bash
# Run full test suite
npm run test
npm run test:integration
npm run test:e2e

# Build verification
npm run build
npm run start
```

**Security Validation:**
```typescript
// Input validation and sanitization
// Authentication flow testing
// Authorization boundary testing
// Rate limiting verification
```

**Final Validation Gate:**
- [ ] All tests passing (>95% coverage)
- [ ] Build successful without warnings
- [ ] Security audit passed
- [ ] Performance targets achieved
- [ ] Documentation updated

### **3. Continuous Validation**
**After Each Phase:**
1. **Run Tests**: Execute relevant test suites
2. **Build Check**: Verify compilation and build success
3. **Performance Check**: Monitor response times and resource usage
4. **Security Check**: Validate input handling and access controls
5. **Documentation Update**: Keep docs current with implementation

**Error Recovery:**
- If validation fails, analyze root cause
- Implement fixes before proceeding
- Re-run validation to confirm resolution
- Update implementation approach if needed

### **4. Progress Tracking**
**Task Management:**
- Use task management tools for complex implementations
- Update task status after each validation gate
- Track time spent vs. estimates
- Document any scope changes or discoveries

**Quality Metrics:**
- Test coverage percentage
- Performance benchmark results
- Security scan results
- Code quality scores

### **5. Completion Verification**
**Final Checklist:**
- [ ] All PRP requirements implemented
- [ ] Success criteria met or exceeded
- [ ] No critical or high-severity issues
- [ ] User acceptance criteria satisfied
- [ ] Documentation complete and accurate
- [ ] Rollback plan tested and documented

**Delivery Package:**
- Working feature with all requirements met
- Comprehensive test suite with >95% coverage
- Updated documentation and API specs
- Performance and security validation reports
- User guide and troubleshooting documentation

## **Error Handling & Recovery**

### **Common Issues:**
1. **Dependency Conflicts**: Update package.json and resolve conflicts
2. **Database Migration Failures**: Rollback and fix schema issues
3. **Test Failures**: Debug and fix before proceeding
4. **Build Errors**: Resolve TypeScript and compilation issues
5. **Performance Issues**: Optimize queries and component rendering

### **Rollback Procedures:**
1. **Database**: Use Prisma migration rollback
2. **Code**: Git revert to last stable state
3. **Dependencies**: Restore previous package.json state
4. **Configuration**: Reset environment variables

## **Success Indicators**
- ✅ All validation gates passed
- ✅ Feature works as specified in PRP
- ✅ Performance targets achieved
- ✅ Security standards met
- ✅ User experience validated
- ✅ Documentation complete
- ✅ Ready for production deployment

## **Output Format**
```
🚀 Executing PRP: [feature-name]

📋 Phase 1: Foundation Setup
✅ Database schema updated
✅ Core models implemented
✅ Validation Gate 1 PASSED

📋 Phase 2: API Implementation
✅ API endpoints created
✅ Authentication integrated
✅ Tests passing (96% coverage)
✅ Validation Gate 2 PASSED

📋 Phase 3: Frontend Components
✅ UI components built
✅ State management implemented
✅ Accessibility verified
✅ Validation Gate 3 PASSED

📋 Phase 4: Integration & Testing
✅ End-to-end flows working
✅ Performance optimized
✅ Cross-browser tested
✅ Validation Gate 4 PASSED

📋 Phase 5: Quality Assurance
✅ Full test suite passing
✅ Build successful
✅ Security validated
✅ Final Validation Gate PASSED

🎯 Implementation Complete!
- Feature fully functional
- All requirements met
- Ready for production
- Documentation updated
```

---

**Remember**: The goal is systematic, validated implementation that meets all requirements with confidence. Each validation gate ensures quality and prevents technical debt accumulation.
