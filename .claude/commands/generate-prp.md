# Generate Product Requirements Prompt (PRP)

## **Command Purpose**
Generate a comprehensive Product Requirements Prompt (PRP) from an INITIAL.md feature request file. The PRP serves as a complete implementation blueprint that includes all context, patterns, and validation criteria needed for successful feature development.

## **Usage**
```
/generate-prp INITIAL_[feature-name].md
```

## **Process**

### **1. Research Phase**
Read and analyze the provided INITIAL.md file: `$ARGUMENTS`

**Codebase Analysis:**
- Use `codebase-retrieval` to understand existing patterns and architecture
- Identify similar implementations and reusable components
- Analyze current database schema and API patterns
- Review testing patterns and quality standards

**Documentation Research:**
- Gather relevant API documentation from the feature requirements
- Include library-specific patterns and best practices
- Research integration requirements and dependencies
- Identify potential gotchas and common pitfalls

### **2. Context Gathering**
**Project Context:**
- Read `CLAUDE.md` for project-specific rules and standards
- Review existing documentation in `docs/` for patterns
- Analyze `examples/` folder for implementation patterns
- Check current task lists and project status

**Technical Context:**
- Database schema analysis and migration requirements
- API endpoint patterns and authentication flows
- Frontend component architecture and state management
- Testing infrastructure and coverage requirements

### **3. Blueprint Creation**
Create a comprehensive PRP file in `PRPs/[feature-name].md` with the following structure:

```markdown
# [Feature Name] - Product Requirements Prompt

## **EXECUTIVE SUMMARY**
[Brief overview of the feature and its business value]

## **COMPLETE CONTEXT**
### Project Architecture
[Current system architecture and how this feature fits]

### Existing Patterns
[Code patterns and conventions to follow from codebase analysis]

### Dependencies
[Required libraries, APIs, and system dependencies]

## **DETAILED REQUIREMENTS**
### Functional Requirements
[Specific functionality with acceptance criteria]

### Technical Requirements
[Performance, security, and integration specifications]

### User Experience Requirements
[Interface design and interaction patterns]

## **IMPLEMENTATION PLAN**
### Phase 1: Foundation
[Database schema, core models, basic API structure]

### Phase 2: Core Logic
[Business logic implementation and validation]

### Phase 3: User Interface
[Frontend components and user interactions]

### Phase 4: Integration
[System integration and end-to-end flows]

### Phase 5: Testing & Validation
[Comprehensive testing and quality assurance]

## **TECHNICAL SPECIFICATIONS**
### Database Schema
[Detailed schema with relationships and indexes]

### API Endpoints
[Complete API specification with request/response examples]

### Component Architecture
[Frontend component structure and props interfaces]

### State Management
[Data flow and state management patterns]

## **VALIDATION CRITERIA**
### Success Metrics
[Measurable outcomes and performance targets]

### Test Requirements
[Specific test cases and coverage requirements]

### Quality Gates
[Build, performance, and security validation]

## **IMPLEMENTATION CHECKLIST**
- [ ] Database schema created and migrated
- [ ] API endpoints implemented and tested
- [ ] Frontend components built and styled
- [ ] Integration testing completed
- [ ] Performance optimization verified
- [ ] Security validation passed
- [ ] Documentation updated
- [ ] User acceptance testing completed

## **RISK MITIGATION**
[Potential issues and mitigation strategies]

## **ROLLBACK PLAN**
[Steps to safely rollback if issues arise]
```

### **4. Quality Validation**
**Completeness Check:**
- Verify all requirements from INITIAL.md are addressed
- Ensure all technical specifications are detailed
- Confirm integration points are clearly defined
- Validate that success criteria are measurable

**Context Verification:**
- Cross-reference with existing codebase patterns
- Ensure consistency with project architecture
- Verify compatibility with current dependencies
- Confirm alignment with project standards

### **5. Confidence Scoring**
Rate the PRP completeness and implementation readiness on a scale of 1-10:

**Scoring Criteria:**
- **10**: Complete context, clear requirements, detailed implementation plan
- **8-9**: Minor gaps in context or implementation details
- **6-7**: Some missing context or unclear requirements
- **4-5**: Significant gaps requiring additional research
- **1-3**: Insufficient context for reliable implementation

**If score < 8:** Identify specific gaps and recommend additional research or clarification needed.

## **Output Format**
1. **Research Summary**: Brief overview of findings and patterns identified
2. **PRP File Created**: Confirmation of `PRPs/[feature-name].md` creation
3. **Confidence Score**: Implementation readiness score with justification
4. **Next Steps**: Recommended actions (execute PRP, gather more context, etc.)

## **Example Output**
```
✅ Research Phase Complete
- Analyzed existing assessment patterns in src/components/assessments/
- Identified reusable chart components in src/components/analytics/
- Found PDF generation utilities in src/lib/pdf/

✅ PRP Generated: PRPs/skill-assessment-system.md
- Complete implementation blueprint with 5-phase plan
- Detailed technical specifications and validation criteria
- Integration points with existing authentication and user systems

🎯 Confidence Score: 9/10
- All requirements clearly defined with implementation details
- Minor gap: Need to verify PDF library compatibility with Next.js 14

🚀 Next Steps:
Run `/execute-prp PRPs/skill-assessment-system.md` to begin implementation
```

## **Error Handling**
- If INITIAL.md file not found, provide template and guidance
- If insufficient context, request specific additional information
- If conflicting requirements, highlight conflicts and request clarification
- If missing dependencies, identify and recommend installation

---

**Remember**: The goal is to create a PRP so comprehensive that implementation becomes systematic and predictable, reducing the chance of missed requirements or architectural inconsistencies.
