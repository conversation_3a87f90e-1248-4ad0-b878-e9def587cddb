{"permissions": {"allowedCommands": ["generate-prp", "execute-prp"], "allowFileAccess": true, "allowNetworkAccess": true, "allowProcessExecution": true}, "context": {"includeGitignore": true, "maxFileSize": "1MB", "excludePatterns": ["node_modules/**", ".next/**", "coverage/**", "*.log", "*.cache", ".env*"]}, "commands": {"generate-prp": {"description": "Generate comprehensive Product Requirements Prompt from INITIAL.md", "requiresConfirmation": false, "timeout": 300}, "execute-prp": {"description": "Execute PRP to implement feature with validation gates", "requiresConfirmation": true, "timeout": 1800}}}