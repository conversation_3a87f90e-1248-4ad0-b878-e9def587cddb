#!/usr/bin/env node

/**
 * Audit Engine Functionality Testing Script
 * 
 * Tests the core audit engine functionality by running actual audits
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

async function testDatabaseOperations() {
  console.log('\n🗄️  Testing Database Operations...');
  
  try {
    // Test 1: Create a test audit run
    const testRun = await prisma.auditRun.create({
      data: {
        status: 'PENDING',
        totalIssues: 0,
        criticalCount: 0,
        highCount: 0,
        mediumCount: 0,
        lowCount: 0,
        triggeredBy: 'test-engine'
      }
    });
    logTest('Can create AuditRun', true, `Created run ${testRun.id.slice(-8)}`);
    
    // Test 2: Create test issues
    const testIssues = await Promise.all([
      prisma.auditIssue.create({
        data: {
          auditRunId: testRun.id,
          severity: 'CRITICAL',
          category: 'SECURITY',
          title: 'SQL Injection Vulnerability',
          description: 'Potential SQL injection in user input handling',
          filePath: '/src/lib/database/queries.ts',
          lineNumber: 42,
          status: 'OPEN'
        }
      }),
      prisma.auditIssue.create({
        data: {
          auditRunId: testRun.id,
          severity: 'HIGH',
          category: 'PERFORMANCE',
          title: 'Inefficient Database Query',
          description: 'N+1 query pattern detected',
          filePath: '/src/components/UserList.tsx',
          lineNumber: 28,
          status: 'OPEN'
        }
      }),
      prisma.auditIssue.create({
        data: {
          auditRunId: testRun.id,
          severity: 'MEDIUM',
          category: 'MAINTAINABILITY',
          title: 'Code Duplication',
          description: 'Duplicate validation logic found',
          filePath: '/src/lib/validation.ts',
          lineNumber: 15,
          status: 'OPEN'
        }
      })
    ]);
    
    logTest('Can create multiple AuditIssues', true, `Created ${testIssues.length} issues`);
    
    // Test 3: Update audit run with counts
    await prisma.auditRun.update({
      where: { id: testRun.id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        totalIssues: testIssues.length,
        criticalCount: 1,
        highCount: 1,
        mediumCount: 1
      }
    });
    logTest('Can update AuditRun with issue counts', true);
    
    // Test 4: Query audit run with issues
    const runWithIssues = await prisma.auditRun.findUnique({
      where: { id: testRun.id },
      include: { 
        issues: {
          orderBy: { severity: 'desc' }
        }
      }
    });
    
    logTest('Can query AuditRun with issues', 
      runWithIssues?.issues.length === 3, 
      `Found ${runWithIssues?.issues.length} issues`);
    
    // Test 5: Test issue filtering
    const criticalIssues = await prisma.auditIssue.findMany({
      where: {
        auditRunId: testRun.id,
        severity: 'CRITICAL'
      }
    });
    
    logTest('Can filter issues by severity', 
      criticalIssues.length === 1, 
      `Found ${criticalIssues.length} critical issues`);
    
    // Test 6: Test issue status updates
    await prisma.auditIssue.update({
      where: { id: testIssues[0].id },
      data: {
        status: 'RESOLVED',
        resolvedAt: new Date()
      }
    });
    
    const resolvedIssue = await prisma.auditIssue.findUnique({
      where: { id: testIssues[0].id }
    });
    
    logTest('Can update issue status', 
      resolvedIssue?.status === 'RESOLVED', 
      `Issue status: ${resolvedIssue?.status}`);
    
    // Test 7: Test comments
    const testUser = await prisma.user.findFirst();
    if (testUser) {
      const comment = await prisma.issueComment.create({
        data: {
          issueId: testIssues[1].id,
          userId: testUser.id,
          comment: 'This issue needs immediate attention due to performance impact.'
        }
      });
      
      logTest('Can create issue comments', true, `Created comment ${comment.id.slice(-8)}`);
      
      // Test comment retrieval
      const issueWithComments = await prisma.auditIssue.findUnique({
        where: { id: testIssues[1].id },
        include: {
          comments: {
            include: { user: true }
          }
        }
      });
      
      logTest('Can retrieve issue with comments', 
        issueWithComments?.comments.length === 1,
        `Found ${issueWithComments?.comments.length} comments`);
    }
    
    // Test 8: Test pagination
    const paginatedIssues = await prisma.auditIssue.findMany({
      where: { auditRunId: testRun.id },
      take: 2,
      skip: 0,
      orderBy: { createdAt: 'desc' }
    });
    
    logTest('Can paginate issues', 
      paginatedIssues.length === 2,
      `Retrieved ${paginatedIssues.length} issues with pagination`);
    
    // Test 9: Test aggregations
    const issueCounts = await prisma.auditIssue.groupBy({
      by: ['severity'],
      where: { auditRunId: testRun.id },
      _count: { severity: true }
    });
    
    logTest('Can aggregate issue counts by severity', 
      issueCounts.length === 3,
      `Found ${issueCounts.length} severity groups`);
    
    // Cleanup test data
    await prisma.issueComment.deleteMany({
      where: { 
        issueId: { in: testIssues.map(i => i.id) }
      }
    });
    
    await prisma.auditIssue.deleteMany({
      where: { auditRunId: testRun.id }
    });
    
    await prisma.auditRun.delete({
      where: { id: testRun.id }
    });
    
    logTest('Test data cleanup completed', true);
    
  } catch (error) {
    logTest('Database operations', false, error.message);
  }
}

async function testAuditServiceIntegration() {
  console.log('\n🔧 Testing Audit Service Integration...');
  
  try {
    // Test 1: Check if we can import the audit service
    let AuditService;
    try {
      const auditModule = require('./src/lib/audit');
      AuditService = auditModule.AuditService;
      logTest('Can import AuditService', true);
    } catch (error) {
      logTest('Can import AuditService', false, error.message);
      return;
    }
    
    // Test 2: Check if we can instantiate the service
    let auditService;
    try {
      auditService = new AuditService();
      logTest('Can instantiate AuditService', true);
    } catch (error) {
      logTest('Can instantiate AuditService', false, error.message);
      return;
    }
    
    // Test 3: Check if service has required methods
    const hasStartAudit = typeof auditService.startAudit === 'function';
    const hasGetAuditRuns = typeof auditService.getAuditRuns === 'function';
    const hasGetIssues = typeof auditService.getIssues === 'function';
    const hasGetIssueById = typeof auditService.getIssueById === 'function';
    
    logTest('AuditService has startAudit method', hasStartAudit);
    logTest('AuditService has getAuditRuns method', hasGetAuditRuns);
    logTest('AuditService has getIssues method', hasGetIssues);
    logTest('AuditService has getIssueById method', hasGetIssueById);
    
    // Test 4: Test getting audit runs (should work even with empty database)
    try {
      const runs = await auditService.getAuditRuns({ page: 1, limit: 10 });
      logTest('Can call getAuditRuns', true, `Retrieved ${runs.runs.length} runs`);
    } catch (error) {
      logTest('Can call getAuditRuns', false, error.message);
    }
    
    // Test 5: Test getting issues (should work even with empty database)
    try {
      const issues = await auditService.getIssues({ page: 1, limit: 10 });
      logTest('Can call getIssues', true, `Retrieved ${issues.issues.length} issues`);
    } catch (error) {
      logTest('Can call getIssues', false, error.message);
    }
    
  } catch (error) {
    logTest('Audit service integration', false, error.message);
  }
}

async function testFileSystemAnalysis() {
  console.log('\n📁 Testing File System Analysis...');
  
  try {
    // Test 1: Check if we can analyze TypeScript files
    const tsFiles = [];
    const findTsFiles = (dir) => {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const fullPath = path.join(dir, file);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          findTsFiles(fullPath);
        } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
          tsFiles.push(fullPath);
        }
      }
    };
    
    findTsFiles('./src');
    logTest('Can discover TypeScript files', tsFiles.length > 0, `Found ${tsFiles.length} TS/TSX files`);
    
    // Test 2: Check if we can read and analyze file content
    if (tsFiles.length > 0) {
      const sampleFile = tsFiles[0];
      const content = fs.readFileSync(sampleFile, 'utf8');
      
      // Basic static analysis checks
      const hasImports = content.includes('import');
      const hasExports = content.includes('export');
      const hasAsyncAwait = content.includes('async') && content.includes('await');
      const hasTryCatch = content.includes('try') && content.includes('catch');
      
      logTest('Can read file content', content.length > 0, `Read ${content.length} characters`);
      logTest('Can detect imports', hasImports);
      logTest('Can detect exports', hasExports);
      logTest('Can detect async/await patterns', hasAsyncAwait);
      logTest('Can detect error handling', hasTryCatch);
    }
    
    // Test 3: Check for common code patterns
    let totalLinesOfCode = 0;
    let filesWithTodos = 0;
    let filesWithConsoleLog = 0;
    
    for (const file of tsFiles.slice(0, 10)) { // Sample first 10 files
      try {
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');
        totalLinesOfCode += lines.length;
        
        if (content.toLowerCase().includes('todo') || content.toLowerCase().includes('fixme')) {
          filesWithTodos++;
        }
        
        if (content.includes('console.log') || content.includes('console.error')) {
          filesWithConsoleLog++;
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    logTest('Can analyze code patterns', true, 
      `Analyzed ${Math.min(10, tsFiles.length)} files, ${totalLinesOfCode} lines total`);
    logTest('Can detect TODO comments', filesWithTodos >= 0, 
      `Found TODOs in ${filesWithTodos} files`);
    logTest('Can detect console statements', filesWithConsoleLog >= 0, 
      `Found console statements in ${filesWithConsoleLog} files`);
    
  } catch (error) {
    logTest('File system analysis', false, error.message);
  }
}

async function runEngineTests() {
  console.log('🚀 Starting Audit Engine Functionality Testing...');
  console.log('=' .repeat(60));
  
  await testDatabaseOperations();
  await testAuditServiceIntegration();
  await testFileSystemAnalysis();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🎉 Engine Testing Complete!');
  
  await prisma.$disconnect();
}

// Run the tests
runEngineTests().catch(console.error);
