# PRP: Comprehensive Codebase Review

## Executive Summary
Systematic review of the entire codebase to identify errors, inconsistencies, code duplication, unused code, and improvement opportunities using Context Engineering methodology with 101% verification standards.

## Implementation Plan

### Phase 1: Infrastructure & Architecture Analysis (20 min)
**Objective**: Understand codebase structure and identify architectural issues

**Tasks**:
1. **Codebase Structure Analysis**
   - Use `codebase-retrieval` to understand overall architecture
   - Examine package.json, dependencies, and configuration files
   - Identify project structure and module organization

2. **Dependency Audit**
   - Review package.json for unused/outdated dependencies
   - Check for security vulnerabilities in dependencies
   - Identify version conflicts or compatibility issues

**Validation Gates**:
- [ ] Complete dependency tree analysis
- [ ] Architecture documentation created
- [ ] No critical dependency vulnerabilities identified

### Phase 2: Critical Systems Review (40 min)
**Objective**: Review production-critical systems first

**Tasks**:
1. **Authentication & Authorization (20 min)**
   - Use `codebase-retrieval` for authentication flow analysis
   - Review session management, JWT handling, permissions
   - Check for security vulnerabilities in auth logic

2. **Database Layer Analysis (20 min)**
   - Examine Prisma schema and migrations
   - Review database queries for performance issues
   - Check for data integrity constraints

**Validation Gates**:
- [ ] Authentication flow documented and verified
- [ ] Database schema validated
- [ ] No critical security vulnerabilities in auth

### Phase 3: API Layer Comprehensive Review (40 min)
**Objective**: Review all API endpoints for errors and inconsistencies

**Tasks**:
1. **API Endpoints Audit (20 min)**
   - Use `codebase-retrieval` to analyze all API routes
   - Check error handling patterns and consistency
   - Identify missing validation or rate limiting

2. **API Integration Testing (20 min)**
   - Review API response formats and error codes
   - Check for proper CORS and security headers
   - Validate request/response schemas

**Validation Gates**:
- [ ] All API endpoints catalogued and reviewed
- [ ] Error handling patterns documented
- [ ] Security headers and validation verified

### Phase 4: React Components & Frontend Review (40 min)
**Objective**: Analyze React components for errors, performance, and consistency

**Tasks**:
1. **Component Architecture Review (20 min)**
   - Use `codebase-retrieval` for component structure analysis
   - Identify code duplication in components
   - Review state management patterns

2. **UI/UX Consistency Audit (20 min)**
   - Check for styling inconsistencies
   - Review responsive design implementation
   - Identify unused CSS or component code

**Validation Gates**:
- [ ] Component architecture documented
- [ ] Code duplication identified and catalogued
- [ ] UI consistency issues documented

### Phase 5: Security & Performance Audit (40 min)
**Objective**: Comprehensive security and performance analysis

**Tasks**:
1. **Security Vulnerability Scan (20 min)**
   - Review input validation across the application
   - Check for XSS, CSRF, and injection vulnerabilities
   - Analyze authentication and authorization flows

2. **Performance Analysis (20 min)**
   - Identify performance bottlenecks in code
   - Review database query optimization
   - Check for memory leaks or inefficient algorithms

**Validation Gates**:
- [ ] Security audit completed with findings documented
- [ ] Performance bottlenecks identified
- [ ] No critical security vulnerabilities remain

### Phase 6: Testing & Documentation Review (20 min)
**Objective**: Evaluate testing coverage and documentation quality

**Tasks**:
1. **Testing Infrastructure Review**
   - Analyze test coverage and quality
   - Identify missing test cases
   - Review testing patterns and consistency

2. **Documentation Audit**
   - Check documentation accuracy and completeness
   - Identify outdated or missing documentation
   - Review code comments and inline documentation

**Validation Gates**:
- [ ] Testing coverage analysis completed
- [ ] Documentation gaps identified
- [ ] Testing infrastructure issues documented

## Comprehensive Test Plan

### Validation Commands
```bash
# Build verification
npm run build

# Test suite execution
npm test

# Linting and code quality
npm run lint

# Type checking
npm run type-check

# Security audit
npm audit
```

### Success Criteria
1. **Issue Categorization**: All issues classified by severity
   - Production-breaking (P0)
   - Data integrity (P1) 
   - Performance/UX (P2)
   - Security vulnerabilities (P1)
   - Technical debt (P3)

2. **Verification Standards**: 101% certainty through file examination
3. **Actionable Output**: Specific recommendations for each issue
4. **Comprehensive Coverage**: All system components reviewed

### Final Deliverables
1. **Issue Inventory**: Categorized list of all identified issues
2. **Priority Matrix**: Issues ranked by severity and impact
3. **Action Plan**: Specific steps to address each category
4. **Verification Report**: Evidence of systematic review completion

## Risk Mitigation
- Use `codebase-retrieval` before analyzing any code section
- Examine actual files rather than making assumptions
- Focus on production-breaking issues first
- Maintain systematic approach throughout review
- Document all findings with specific file references
