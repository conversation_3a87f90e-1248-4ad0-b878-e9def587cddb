/**
 * Skills Analysis Comprehensive Tests
 * 
 * Tests Skills Analysis Comprehensive API endpoints, request/response handling, validation, and error scenarios.
 * 
 * @category unit
 * @requires API mocking, request simulation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '@/app/api/ai/skills-analysis/comprehensive/route';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { geminiService } from '@/lib/services/geminiService';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    skillAssessment: {
      findMany: jest.fn(),
    },
    careerPath: {
      findFirst: jest.fn(),
    },
    skillGapAnalysis: {
      create: jest.fn(),
    },
  },
}));

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeComprehensiveSkillGap: jest.fn(),
  },
}));

jest.mock('@/lib/services/consolidated-cache-service', () => ({
  consolidatedCache: {
    get: jest.fn(),
    set: jest.fn(),
    generateAIKey: jest.fn(),
  },
}));

jest.mock('@/lib/auth', () => ({
  authOptions: {},
}));

jest.mock('@/lib/errorHandler', () => ({
  withErrorHandler: (handler: any) => handler,
}));

jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: (request: any, config: any, handler: any) => handler(),
}));

jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: (request: any, handler: any) => handler(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;
const mockConsolidatedCache = consolidatedCache as jest.Mocked<typeof consolidatedCache>;

describe('Comprehensive Skills Analysis API', () => {
  const mockUserId = 'test-user-id';
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue({
      user: { id: mockUserId },
    } as any);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('POST /api/ai/skills-analysis/comprehensive', () => {
    const validRequestData = {
      currentSkills: [
        {
          skillName: 'JavaScript',
          selfRating: 7,
          confidenceLevel: 8,
          yearsOfExperience: 3,
        },
        {
          skillName: 'React',
          selfRating: 6,
          confidenceLevel: 7,
          yearsOfExperience: 2,
        },
      ],
      targetCareerPath: {
        careerPathName: 'Full Stack Developer',
        targetLevel: 'SENIOR',
      },
      preferences: {
        timeframe: 'SIX_MONTHS',
        hoursPerWeek: 10,
        learningStyle: ['HANDS_ON', 'STRUCTURED'],
        budget: 'MODERATE',
        focusAreas: ['TECHNICAL_SKILLS'],
      },
    };

    it('should successfully analyze skills with valid data', async () => {
      // Arrange
      const mockCareerPathData = {
        id: 'career-path-id',
        name: 'Full Stack Developer',
        requiredSkills: [
          { skillName: 'JavaScript', minimumLevel: 8 },
          { skillName: 'React', minimumLevel: 7 },
          { skillName: 'Node.js', minimumLevel: 6 },
        ],
      };

      const mockAIResponse = {
        success: true,
        data: {
          skillGaps: [
            {
              skillName: 'Node.js',
              currentLevel: 0,
              requiredLevel: 6,
              priority: 'HIGH',
              estimatedTimeToLearn: '3 months',
            },
          ],
          learningPath: [
            {
              phase: 1,
              skills: ['Node.js Fundamentals'],
              estimatedDuration: '4 weeks',
              resources: ['Node.js Official Documentation'],
            },
          ],
          overallAssessment: {
            readinessScore: 75,
            timeToGoal: '6 months',
            confidenceLevel: 'HIGH',
          },
        },
      };

      const mockSkillGapAnalysis = {
        id: 'analysis-id',
        userId: mockUserId,
        targetCareerPathName: 'Full Stack Developer',
        status: 'ACTIVE',
        createdAt: new Date(),
      };

      mockConsolidatedCache.get.mockResolvedValue(null); // No cache
      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
      mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPathData as any);
      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(mockAIResponse);
      mockPrisma.skillGapAnalysis.create.mockResolvedValue(mockSkillGapAnalysis as any);

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);

      // Assert
      expect(response.status).toBe(200);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(true);
      expect(responseData.data.skillGaps).toBeDefined();
      expect(responseData.data.learningPath).toBeDefined();
      expect(responseData.data.overallAssessment).toBeDefined();

      // Verify database interactions
      expect(mockPrisma.careerPath.findFirst).toHaveBeenCalledWith({
        where: { name: 'Full Stack Developer' },
        include: { requiredSkills: true },
      });
      
      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(
        validRequestData.currentSkills,
        validRequestData.targetCareerPath,
        validRequestData.preferences,
        mockCareerPathData,
        mockUserId
      );
    });

    it('should fail with invalid request data', async () => {
      // Arrange
      const invalidRequestData = {
        currentSkills: [], // Empty array - should fail validation
        targetCareerPath: {
          careerPathName: 'A', // Too short
          targetLevel: 'INVALID_LEVEL', // Invalid enum value
        },
        preferences: {
          timeframe: 'INVALID_TIMEFRAME',
          hoursPerWeek: -5, // Negative hours
          learningStyle: [],
          budget: 'INVALID_BUDGET',
          focusAreas: [],
        },
      };

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(invalidRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);

      // Assert
      expect(response.status).toBe(400);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
      expect(responseData.error).toContain('validation');
    });

    it('should handle AI service failures gracefully', async () => {
      // Arrange
      mockConsolidatedCache.get.mockResolvedValue(null);
      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
      mockPrisma.careerPath.findFirst.mockResolvedValue(null);
      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
        success: false,
        error: 'AI service temporarily unavailable',
      });

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);

      // Assert
      expect(response.status).toBe(500);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('AI service temporarily unavailable');
    });

    it('should handle unauthorized access', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null); // No session

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);

      // Assert
      expect(response.status).toBe(401);
      
      const responseData = await response.json();
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('Unauthorized');
    });
  });
});

/**
 * Key Testing Patterns Demonstrated:
 * 
 * 1. Comprehensive Mocking: All external dependencies are properly mocked
 * 2. Test Structure: Clear Arrange-Act-Assert pattern
 * 3. Edge Cases: Tests cover success, validation errors, service failures, and unauthorized access
 * 4. Realistic Data: Uses realistic mock data that matches actual API contracts
 * 5. Proper Cleanup: beforeEach/afterEach ensure test isolation
 * 6. Descriptive Names: Test names clearly describe what is being tested
 * 7. Assertions: Multiple assertions verify different aspects of the response
 * 8. Error Handling: Tests verify proper error responses and status codes
 */
