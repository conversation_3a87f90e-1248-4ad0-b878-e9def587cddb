---
title: "Naming Conventions & Standards"
category: "reference"
subcategory: "development"
tags: ["naming", "conventions", "standards", "code-style", "files"]
last_updated: "2025-07-06"
last_validated: "2025-07-06"
dependencies: []
used_by: ["development-setup", "project-structure"]
maintainer: "development-team"
ai_context: "Comprehensive naming conventions for files, code, and project organization"
---

# 📝 Naming Conventions & Standards

## **📁 File & Directory Naming**

### **General Principles**
- Use **kebab-case** for files and directories
- Use **descriptive, meaningful names**
- Avoid abbreviations unless widely understood
- Include file purpose in the name

### **Directory Structure**
```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries
├── utils/                  # Helper functions
└── types/                  # TypeScript type definitions

docs/
├── reference/              # Technical documentation
├── user-guides/            # End-user documentation
├── workflows/              # Process documentation
├── project-management/     # Project documentation
└── operations/             # Operational procedures
```

### **File Naming Patterns**

#### **React Components**
```typescript
// PascalCase for component files
UserProfile.tsx
SkillAssessment.tsx
ResumeBuilder.tsx
SalaryCalculator.tsx

// Component directories (kebab-case)
user-profile/
skill-assessment/
resume-builder/
salary-calculator/
```

#### **API Routes**
```typescript
// kebab-case for API files
route.ts                    # Standard App Router file
user-profile.ts
skill-assessment.ts
salary-calculation.ts

// API directory structure
api/
├── auth/
├── users/
├── assessments/
└── salary/
```

#### **Utility Files**
```typescript
// kebab-case for utility files
date-utils.ts
validation-helpers.ts
api-client.ts
database-helpers.ts
```

#### **Test Files**
```typescript
// Match source file with .test or .spec suffix
UserProfile.test.tsx
skill-assessment.test.ts
api-client.spec.ts
database-helpers.test.ts
```

## **💻 Code Naming Conventions**

### **Variables & Functions**
```typescript
// camelCase for variables and functions
const userName = 'john_doe';
const skillLevel = 5;
const assessmentResults = [];

function calculateSalary() { }
function validateUserInput() { }
function fetchAssessmentData() { }
```

### **Constants**
```typescript
// SCREAMING_SNAKE_CASE for constants
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_TIMEOUT = 5000;
const SKILL_LEVELS = ['Beginner', 'Intermediate', 'Advanced'];
```

### **Types & Interfaces**
```typescript
// PascalCase for types and interfaces
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

type AssessmentStatus = 'pending' | 'completed' | 'failed';
type SkillLevel = 'beginner' | 'intermediate' | 'advanced';

// Prefix interfaces with 'I' if needed for clarity
interface IApiResponse<T> {
  data: T;
  status: number;
  message: string;
}
```

### **Classes**
```typescript
// PascalCase for classes
class UserService {
  private apiClient: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }
}

class AssessmentEngine {
  calculateScore(): number { }
  generateReport(): Report { }
}
```

### **Enums**
```typescript
// PascalCase for enums and enum values
enum UserRole {
  Admin = 'admin',
  User = 'user',
  Guest = 'guest'
}

enum AssessmentType {
  SkillGap = 'skill_gap',
  CareerPath = 'career_path',
  Interview = 'interview'
}
```

## **🗄️ Database Naming**

### **Table Names**
```sql
-- snake_case for table names (plural)
users
user_profiles
skill_assessments
assessment_results
career_paths
salary_calculations
```

### **Column Names**
```sql
-- snake_case for column names
id
user_id
first_name
last_name
email_address
created_at
updated_at
skill_level
assessment_score
```

### **Relationships**
```sql
-- Foreign key naming
user_id          -- References users.id
assessment_id    -- References assessments.id
career_path_id   -- References career_paths.id

-- Junction table naming
user_skills      -- Many-to-many: users <-> skills
assessment_questions  -- Many-to-many: assessments <-> questions
```

## **🌐 API Naming**

### **Endpoint Patterns**
```typescript
// RESTful API naming
GET    /api/users                    // Get all users
GET    /api/users/:id                // Get specific user
POST   /api/users                    // Create user
PUT    /api/users/:id                // Update user
DELETE /api/users/:id                // Delete user

// Resource-specific endpoints
GET    /api/assessments              // Get assessments
POST   /api/assessments              // Create assessment
GET    /api/assessments/:id/results  // Get assessment results
POST   /api/salary/calculate         // Calculate salary
```

### **Query Parameters**
```typescript
// snake_case for query parameters
?page=1&per_page=20
?skill_level=intermediate
?assessment_type=skill_gap
?sort_by=created_at&sort_order=desc
```

### **Response Fields**
```typescript
// camelCase for JSON response fields
{
  "userId": "123",
  "firstName": "John",
  "lastName": "Doe",
  "skillLevel": "intermediate",
  "assessmentResults": [],
  "createdAt": "2025-07-06T10:00:00Z"
}
```

## **📄 Documentation Naming**

### **Documentation Files**
```
// kebab-case with descriptive names
user-guide.md
api-documentation.md
development-setup.md
deployment-checklist.md
troubleshooting-guide.md
```

### **Section Headers**
```markdown
# Main Title (Title Case)
## Section Header (Title Case)
### Subsection Header (Title Case)

# Examples:
# Getting Started Guide
## User Registration Process
### Email Verification Steps
```

### **Code Examples**
```typescript
// Use descriptive variable names in examples
const exampleUser = {
  id: '123',
  name: 'John Doe',
  email: '<EMAIL>'
};

// Function examples with clear purpose
function calculateUserSkillScore(assessment: Assessment): number {
  // Implementation
}
```

## **🧪 Testing Naming**

### **Test Files**
```typescript
// Match source file with test suffix
UserProfile.test.tsx        // Component tests
user-service.test.ts        // Service tests
api-endpoints.test.ts       // API tests
database-operations.test.ts // Database tests
```

### **Test Descriptions**
```typescript
// Descriptive test names
describe('UserProfile Component', () => {
  it('should render user information correctly', () => {});
  it('should handle missing user data gracefully', () => {});
  it('should update profile when form is submitted', () => {});
});

describe('Salary Calculator', () => {
  it('should calculate salary based on experience level', () => {});
  it('should handle invalid input parameters', () => {});
  it('should return error for negative values', () => {});
});
```

### **Test Data**
```typescript
// Descriptive test data names
const mockUserData = {
  id: 'test-user-123',
  name: 'Test User',
  email: '<EMAIL>'
};

const sampleAssessmentResults = [
  { skill: 'JavaScript', level: 'intermediate' },
  { skill: 'React', level: 'advanced' }
];
```

## **🔧 Configuration Naming**

### **Environment Variables**
```bash
# SCREAMING_SNAKE_CASE for environment variables
DATABASE_URL=
NEXTAUTH_SECRET=
NEXTAUTH_URL=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
RESEND_API_KEY=
```

### **Configuration Files**
```
// kebab-case for config files
next.config.js
tailwind.config.ts
jest.config.js
playwright.config.ts
prisma.schema
```

## **📋 Best Practices**

### **General Guidelines**
1. **Be Consistent**: Follow the same pattern throughout the project
2. **Be Descriptive**: Names should clearly indicate purpose
3. **Avoid Abbreviations**: Use full words unless widely understood
4. **Use Conventions**: Follow established patterns for the technology stack

### **Common Patterns**
```typescript
// Event handlers
onClick, onSubmit, onLoad, onChange

// Boolean variables
isLoading, hasError, canEdit, shouldUpdate

// Arrays and collections
users, assessments, results, skills

// Functions that return booleans
isValid(), hasPermission(), canAccess()

// Async functions
fetchUser(), saveAssessment(), updateProfile()
```

### **Avoid These Patterns**
```typescript
// ❌ Avoid
const u = getUserData();        // Too short
const userData123 = {};         // Numbers in names
const temp = calculateScore();  // Generic names
const data = fetchAPI();        // Vague names

// ✅ Prefer
const currentUser = getUserData();
const userProfileData = {};
const assessmentScore = calculateScore();
const assessmentResults = fetchAssessmentData();
```

---

**Related Documentation:**
- [Project Structure](./project-structure.md)
- [Development Setup](../workflows/development-setup.md)
- [Testing Architecture](./testing-architecture.md)
