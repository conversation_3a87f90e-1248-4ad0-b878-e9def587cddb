---
title: "Developer Tools & Commands"
category: "reference"
subcategory: "development"
tags: ["development", "tools", "testerat", "commands", "ai-testing"]
last_updated: "2025-07-06"
last_validated: "2025-07-06"
dependencies: []
used_by: ["development-setup", "testing-architecture"]
maintainer: "development-team"
ai_context: "Comprehensive guide to development tools including Testerat AI-powered testing system"
---

# 🔧 Developer Tools & Commands

## **🤖 Testerat - AI-Powered Testing Tool**

Testerat is our enhanced AI-powered testing system for comprehensive application validation.

### **Installation & Setup**
```bash
# Navigate to testerat directory
cd testerat_enhanced

# Install dependencies
pip install -r requirements.txt

# Install as package
pip install -e .
```

### **Core Commands**

#### **Comprehensive Testing**
```bash
# Run comprehensive component testing
python comprehensive_component_test.py

# Run comprehensive dashboard testing  
python comprehensive_dashboard_test.py

# Run comprehensive live testing
python comprehensive_live_test.py
```

#### **Specific Feature Testing**
```bash
# Test authentication flows
python test_auth_fix.py

# Test resume builder functionality
python test_resume_builder.py

# Test skill gap analyzer
python test_skill_gap_analyzer.py

# Test interview practice
python test_interview_practice.py

# Test salary calculator
python test_salary_calculator_fix.py
```

#### **Advanced Testing**
```bash
# Edge case stress testing
python edge_case_stress_test.py

# Intelligent full flow testing
python intelligent_full_flow_test.py

# Smart component testing
python smart_component_tester.py
```

### **Configuration**
```bash
# Configuration files location
testerat_enhanced/config/

# Main configuration
testerat_enhanced/config/settings.py

# Test templates
testerat_enhanced/templates/
```

### **Reporting**
```bash
# Test reports location
testerat_enhanced/testerat_reports/

# View latest test results
ls -la testerat_enhanced/testerat_reports/

# Check test logs
tail -f testerat_enhanced/testerat_enhanced.log
```

## **🛠️ Development Commands**

### **Environment Management**
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix
```

### **Database Operations**
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Reset database
npx prisma migrate reset

# View database in browser
npx prisma studio

# Seed database
npx prisma db seed
```

### **Testing Commands**
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Generate test coverage
npm run test:coverage
```

## **🔍 Debugging Tools**

### **Database Debugging**
```bash
# Check local database connection
node check-local-db.js

# Check runtime database connection
node check-runtime-db.js

# Debug user issues
node debug-user-issue.js

# Debug assessment functionality
node debug-assessment.js
```

### **API Testing**
```bash
# Create test user via API
node create-test-user-via-api.js

# Test optimization services
node test-optimization-services.js

# Test race conditions
node test-race-condition.js
bash test-race-condition.sh
```

### **Performance Testing**
```bash
# Test optimization performance
node test-optimization-performance.js

# Test database optimization
node test-phase2-database-optimization.js
```

## **📊 Analysis & Monitoring**

### **Project Intelligence**
```bash
# Run project intelligence analysis
npx ts-node scripts/project-intelligence.ts

# Generate project discovery
bash scripts/ultimate-project-discovery.sh

# Validate project structure
bash scripts/validate-project-structure.sh
```

### **Documentation Tools**
```bash
# Validate documentation structure
bash scripts/validate-docs-structure.sh

# Check link health
python scripts/check-link-health.py

# Generate documentation metrics
python scripts/generate-docs-metrics.py
```

### **Resource Management**
```bash
# Analyze resources
node scripts/analyze-perfect-resources.js

# Validate resources
node scripts/ultra-validate-resources.js

# Improve resources
node scripts/improve-resources.js
```

## **🚀 Deployment Tools**

### **Build & Deployment**
```bash
# Validate before deployment
bash scripts/validate-before-create.sh

# Setup deployment enforcement
bash scripts/setup-docs-enforcement.sh

# Setup ultimate protocol
bash scripts/setup-ultimate-protocol.sh
```

### **Quality Assurance**
```bash
# Final quality assurance
node scripts/final-quality-assurance.js

# TDD protocol
npx ts-node scripts/tdd-protocol.ts

# Generate task complexity report
# Output: scripts/task-complexity-report.json
```

## **🔧 Utility Scripts**

### **File Management**
```bash
# Find files universally
bash scripts/universal-find-file.sh [filename]

# Find specific files
bash scripts/find-file.sh [pattern]

# Setup universal navigation
bash scripts/setup-universal-nav.sh
```

### **Content Management**
```bash
# Check content freshness
python scripts/check-content-freshness.py

# Consolidate scattered docs
python scripts/consolidate-scattered-docs.py

# Migrate legacy documentation
python scripts/migrate-legacy-docs.py
```

## **⚙️ Configuration Files**

### **Jest Configuration**
- `jest.config.js` - Main Jest configuration
- `jest.config.unit.js` - Unit testing configuration
- `jest.config.integration.js` - Integration testing configuration
- `jest.setup.js` - Jest setup and mocks

### **TypeScript Configuration**
- `tsconfig.json` - TypeScript compiler configuration
- `next.config.js` - Next.js configuration
- `tailwind.config.ts` - Tailwind CSS configuration

### **Testing Configuration**
- `playwright.config.ts` - Playwright E2E testing
- `jest.polyfills.js` - Jest polyfills for testing
- `jest.setup.ts` - TypeScript Jest setup

## **📝 Best Practices**

### **Testing Workflow**
1. **Unit Tests First**: Test individual components and functions
2. **Integration Tests**: Test API endpoints and database interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Validate response times and resource usage

### **Development Workflow**
1. **Environment Setup**: Use development-setup.md guide
2. **Feature Development**: Follow Context Engineering principles
3. **Testing**: Use Testerat for comprehensive validation
4. **Quality Gates**: Ensure all tests pass before deployment

### **Debugging Process**
1. **Check Logs**: Review application and test logs
2. **Database State**: Verify database connections and data
3. **API Testing**: Test endpoints individually
4. **Component Testing**: Isolate and test specific components

---

**For more information:**
- [Development Setup](../workflows/development-setup.md)
- [Testing Architecture](./testing-architecture.md)
- [Validation System](./validation-system.md)
