#!/bin/bash

# VS Code Crash Fix Script
# This script cleans up files that can cause VS Code to crash

echo "🔧 Fixing VS Code crashes by cleaning up problematic files..."

# Navigate to project root
cd "$(dirname "$0")/.."

# Clean TypeScript build artifacts
echo "📁 Cleaning TypeScript build artifacts..."
find . -name "tsconfig.tsbuildinfo" -delete
find . -name "*.tsbuildinfo" -delete

# Clean Next.js artifacts
echo "📁 Cleaning Next.js artifacts..."
rm -rf faafo-career-platform/.next
rm -rf .next

# Clean test artifacts
echo "📁 Cleaning test artifacts..."
rm -rf faafo-career-platform/coverage
rm -rf faafo-career-platform/playwright-report
rm -rf faafo-career-platform/test-results
rm -rf coverage
rm -rf playwright-report
rm -rf test-results

# Clean Jest cache
echo "📁 Cleaning Jest cache..."
rm -rf faafo-career-platform/node_modules/.cache
rm -rf node_modules/.cache

# Clean temporary files
echo "📁 Cleaning temporary files..."
find . -name "*.log" -size +10M -delete
find . -name ".DS_Store" -delete

# Restart TypeScript language server
echo "🔄 Instructions to restart TypeScript language server:"
echo "1. Open VS Code"
echo "2. Press Cmd+Shift+P (Mac) or Ctrl+Shift+P (Windows/Linux)"
echo "3. Type 'TypeScript: Restart TS Server'"
echo "4. Press Enter"

echo "✅ Cleanup complete! VS Code should be more stable now."
echo ""
echo "💡 If crashes persist:"
echo "   - Restart VS Code completely"
echo "   - Check VS Code extensions for conflicts"
echo "   - Consider increasing VS Code memory limits"
