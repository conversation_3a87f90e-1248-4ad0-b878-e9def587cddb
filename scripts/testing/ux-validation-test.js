#!/usr/bin/env node

/**
 * UX Validation Test Script for Skill Gap Analyzer
 * Tests user experience improvements, especially error handling
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// Test configuration
const config = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  outputDir: './test-results/ux-validation',
  scenarios: [
    'happy-path',
    'ai-service-timeout',
    'validation-errors',
    'network-issues',
    'fallback-data-usage'
  ]
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

class UXValidationTester {
  constructor() {
    this.browser = null;
    this.context = null;
    this.page = null;
    this.results = {
      scenarios: {},
      metrics: {
        errorMessageClarity: [],
        taskCompletionRate: 0,
        timeToRecovery: [],
        retryAttempts: [],
        userSatisfaction: []
      },
      issues: [],
      recommendations: []
    };
  }

  async setup() {
    console.log('🚀 Setting up UX validation test environment...');
    
    this.browser = await chromium.launch({ 
      headless: false, // Show browser for UX observation
      slowMo: 1000 // Slow down for better observation
    });
    
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      recordVideo: {
        dir: path.join(config.outputDir, 'videos'),
        size: { width: 1280, height: 720 }
      }
    });
    
    this.page = await this.context.newPage();
    
    // Set up error tracking
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.results.issues.push({
          type: 'console_error',
          message: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });
    
    // Track network failures
    this.page.on('response', response => {
      if (!response.ok()) {
        this.results.issues.push({
          type: 'network_error',
          url: response.url(),
          status: response.status(),
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  async authenticate() {
    console.log('🔐 Authenticating test user...');
    
    await this.page.goto(`${config.baseUrl}/auth/signin`);
    await this.page.fill('input[name="email"]', config.testUser.email);
    await this.page.fill('input[name="password"]', config.testUser.password);
    await this.page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await this.page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Authentication successful');
  }

  async testHappyPath() {
    console.log('🎯 Testing happy path scenario...');
    const startTime = Date.now();
    
    try {
      // Navigate to skill gap analyzer
      await this.page.goto(`${config.baseUrl}/skills/gap-analyzer`);
      await this.page.waitForLoadState('networkidle');
      
      // Fill out skill assessment
      await this.page.click('text=Assessment');
      await this.page.fill('input[placeholder*="skill"]', 'React');
      await this.page.click('button:has-text("Add Skill")');
      
      // Rate the skill
      const slider = this.page.locator('input[type="range"]').first();
      await slider.fill('7');
      
      // Submit assessment
      await this.page.click('button:has-text("Submit Assessment")');
      await this.page.waitForSelector('text=Assessment submitted successfully', { timeout: 10000 });
      
      // Perform analysis
      await this.page.click('text=Analysis');
      await this.page.selectOption('select[name="targetCareerPath"]', 'Frontend Developer');
      await this.page.selectOption('select[name="experienceLevel"]', 'intermediate');
      await this.page.click('button:has-text("Analyze Skills")');
      
      // Wait for results
      await this.page.waitForSelector('text=Analysis complete', { timeout: 30000 });
      
      const endTime = Date.now();
      this.results.scenarios['happy-path'] = {
        success: true,
        duration: endTime - startTime,
        userExperience: 'smooth',
        issues: []
      };
      
      console.log('✅ Happy path completed successfully');
      
    } catch (error) {
      this.results.scenarios['happy-path'] = {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        userExperience: 'poor'
      };
      console.log('❌ Happy path failed:', error.message);
    }
  }

  async testAIServiceTimeout() {
    console.log('⏱️ Testing AI service timeout scenario...');
    const startTime = Date.now();
    
    try {
      // Intercept AI service calls to simulate timeout
      await this.page.route('**/api/ai/skills-analysis/**', route => {
        // Delay response to simulate timeout
        setTimeout(() => {
          route.fulfill({
            status: 503,
            contentType: 'application/json',
            body: JSON.stringify({
              success: false,
              error: 'AI service temporarily unavailable',
              errorType: 'AI_SERVICE_ERROR',
              fallbackData: {
                skillGaps: [],
                learningPlan: { phases: [], estimatedDuration: 0 },
                careerReadiness: { currentScore: 0, targetScore: 100 }
              },
              suggestedAlternatives: ['Try again in a few minutes', 'Use basic analysis'],
              retryable: true,
              retryAfter: 300
            })
          });
        }, 5000);
      });
      
      // Navigate and trigger analysis
      await this.page.goto(`${config.baseUrl}/skills/gap-analyzer`);
      await this.page.click('text=Analysis');
      await this.page.selectOption('select[name="targetCareerPath"]', 'Frontend Developer');
      await this.page.click('button:has-text("Analyze Skills")');
      
      // Wait for error message
      await this.page.waitForSelector('text=AI service temporarily unavailable', { timeout: 10000 });
      
      // Check if user-friendly error is displayed
      const errorMessage = await this.page.textContent('[data-testid="error-message"]');
      const hasRetryButton = await this.page.isVisible('button:has-text("Try Again")');
      const hasFallbackOption = await this.page.isVisible('button:has-text("Use Quick Analysis")');
      
      // Test retry functionality
      const retryStartTime = Date.now();
      await this.page.click('button:has-text("Try Again")');
      const retryEndTime = Date.now();
      
      this.results.scenarios['ai-service-timeout'] = {
        success: true,
        errorMessageClarity: this.rateErrorMessage(errorMessage),
        hasRetryButton,
        hasFallbackOption,
        timeToRecovery: retryEndTime - retryStartTime,
        userExperience: hasRetryButton && hasFallbackOption ? 'good' : 'poor'
      };
      
      console.log('✅ AI service timeout test completed');
      
    } catch (error) {
      this.results.scenarios['ai-service-timeout'] = {
        success: false,
        error: error.message,
        userExperience: 'poor'
      };
      console.log('❌ AI service timeout test failed:', error.message);
    }
  }

  async testValidationErrors() {
    console.log('📝 Testing validation error scenario...');
    const startTime = Date.now();
    
    try {
      await this.page.goto(`${config.baseUrl}/skills/gap-analyzer`);
      await this.page.click('text=Assessment');
      
      // Try to submit without filling required fields
      await this.page.click('button:has-text("Submit Assessment")');
      
      // Check for validation error messages
      await this.page.waitForSelector('[data-testid="validation-error"]', { timeout: 5000 });
      
      const errorMessages = await this.page.$$eval('[data-testid="validation-error"]', 
        elements => elements.map(el => el.textContent)
      );
      
      // Check if errors are helpful
      const hasSpecificGuidance = errorMessages.some(msg => 
        msg.includes('at least') || msg.includes('required') || msg.includes('select')
      );
      
      // Test error recovery
      await this.page.fill('input[placeholder*="skill"]', 'JavaScript');
      await this.page.click('button:has-text("Add Skill")');
      
      // Check if errors clear
      const errorsCleared = await this.page.waitForSelector('[data-testid="validation-error"]', 
        { state: 'hidden', timeout: 3000 }
      ).then(() => true).catch(() => false);
      
      this.results.scenarios['validation-errors'] = {
        success: true,
        errorCount: errorMessages.length,
        hasSpecificGuidance,
        errorsCleared,
        errorMessageClarity: this.rateErrorMessages(errorMessages),
        userExperience: hasSpecificGuidance && errorsCleared ? 'good' : 'fair'
      };
      
      console.log('✅ Validation error test completed');
      
    } catch (error) {
      this.results.scenarios['validation-errors'] = {
        success: false,
        error: error.message,
        userExperience: 'poor'
      };
      console.log('❌ Validation error test failed:', error.message);
    }
  }

  async testNetworkIssues() {
    console.log('🌐 Testing network issues scenario...');
    
    try {
      // Simulate network failure
      await this.context.setOffline(true);
      
      await this.page.goto(`${config.baseUrl}/skills/gap-analyzer`);
      
      // Try to perform an action that requires network
      await this.page.click('button:has-text("Analyze Skills")');
      
      // Check for network error handling
      await this.page.waitForSelector('text=Connection Problem', { timeout: 5000 });
      
      const hasOfflineMode = await this.page.isVisible('button:has-text("Work Offline")');
      const hasRetryOption = await this.page.isVisible('button:has-text("Retry Connection")');
      
      // Restore network and test recovery
      await this.context.setOffline(false);
      await this.page.click('button:has-text("Retry Connection")');
      
      this.results.scenarios['network-issues'] = {
        success: true,
        hasOfflineMode,
        hasRetryOption,
        userExperience: hasOfflineMode && hasRetryOption ? 'good' : 'fair'
      };
      
      console.log('✅ Network issues test completed');
      
    } catch (error) {
      this.results.scenarios['network-issues'] = {
        success: false,
        error: error.message,
        userExperience: 'poor'
      };
      console.log('❌ Network issues test failed:', error.message);
    }
  }

  async testFallbackDataUsage() {
    console.log('🔄 Testing fallback data usage scenario...');
    
    try {
      // Mock API to return fallback data
      await this.page.route('**/api/ai/skills-analysis/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              skillGaps: [],
              learningPlan: { phases: [], estimatedDuration: 0 },
              careerReadiness: { currentScore: 50, targetScore: 100 }
            },
            metadata: {
              edgeCaseHandlerData: {
                fallbackDataUsed: true,
                suggestedAlternatives: ['Try full analysis later', 'Use manual assessment']
              }
            }
          })
        });
      });
      
      await this.page.goto(`${config.baseUrl}/skills/gap-analyzer`);
      await this.page.click('text=Analysis');
      await this.page.click('button:has-text("Analyze Skills")');
      
      // Check for fallback data warning
      await this.page.waitForSelector('text=Limited Results', { timeout: 10000 });
      
      const hasWarning = await this.page.isVisible('text=Some services were unavailable');
      const hasAlternatives = await this.page.isVisible('text=Try full analysis later');
      
      this.results.scenarios['fallback-data-usage'] = {
        success: true,
        hasWarning,
        hasAlternatives,
        userExperience: hasWarning && hasAlternatives ? 'good' : 'fair'
      };
      
      console.log('✅ Fallback data usage test completed');
      
    } catch (error) {
      this.results.scenarios['fallback-data-usage'] = {
        success: false,
        error: error.message,
        userExperience: 'poor'
      };
      console.log('❌ Fallback data usage test failed:', error.message);
    }
  }

  rateErrorMessage(message) {
    if (!message) return 1;
    
    let score = 1;
    
    // Check for user-friendly language
    if (!/error|failed|exception/i.test(message)) score += 1;
    
    // Check for specific guidance
    if (/try again|wait|check|select|fill/i.test(message)) score += 1;
    
    // Check for time estimates
    if (/minute|second|hour/i.test(message)) score += 1;
    
    // Check for alternatives
    if (/alternative|instead|or/i.test(message)) score += 1;
    
    return Math.min(score, 5);
  }

  rateErrorMessages(messages) {
    if (!messages || messages.length === 0) return 1;
    
    const scores = messages.map(msg => this.rateErrorMessage(msg));
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  async generateReport() {
    console.log('📊 Generating UX validation report...');
    
    // Calculate overall metrics
    const scenarios = Object.values(this.results.scenarios);
    const successfulScenarios = scenarios.filter(s => s.success);
    
    this.results.metrics.taskCompletionRate = (successfulScenarios.length / scenarios.length) * 100;
    
    // Calculate average error message clarity
    const clarityScores = scenarios
      .filter(s => s.errorMessageClarity)
      .map(s => s.errorMessageClarity);
    
    if (clarityScores.length > 0) {
      this.results.metrics.averageErrorClarity = 
        clarityScores.reduce((sum, score) => sum + score, 0) / clarityScores.length;
    }
    
    // Generate recommendations
    this.generateRecommendations();
    
    // Save report
    const reportPath = path.join(config.outputDir, 'ux-validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    
    // Generate human-readable summary
    const summaryPath = path.join(config.outputDir, 'ux-validation-summary.md');
    const summary = this.generateSummary();
    fs.writeFileSync(summaryPath, summary);
    
    console.log(`📋 Report saved to: ${reportPath}`);
    console.log(`📄 Summary saved to: ${summaryPath}`);
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Check error message quality
    if (this.results.metrics.averageErrorClarity < 4) {
      recommendations.push({
        priority: 'high',
        category: 'error-messages',
        issue: 'Error messages need improvement',
        suggestion: 'Use more user-friendly language and provide specific guidance'
      });
    }
    
    // Check task completion rate
    if (this.results.metrics.taskCompletionRate < 80) {
      recommendations.push({
        priority: 'high',
        category: 'user-flow',
        issue: 'Low task completion rate',
        suggestion: 'Improve error recovery mechanisms and provide better fallback options'
      });
    }
    
    // Check for missing features
    const scenarios = this.results.scenarios;
    if (scenarios['ai-service-timeout'] && !scenarios['ai-service-timeout'].hasFallbackOption) {
      recommendations.push({
        priority: 'medium',
        category: 'fallback-handling',
        issue: 'Missing fallback options for AI service failures',
        suggestion: 'Implement quick analysis or cached results as alternatives'
      });
    }
    
    this.results.recommendations = recommendations;
  }

  generateSummary() {
    const { scenarios, metrics, recommendations } = this.results;
    
    return `# UX Validation Test Summary

## Overall Results
- **Task Completion Rate**: ${metrics.taskCompletionRate.toFixed(1)}%
- **Average Error Message Clarity**: ${(metrics.averageErrorClarity || 0).toFixed(1)}/5.0
- **Total Issues Found**: ${this.results.issues.length}

## Scenario Results
${Object.entries(scenarios).map(([name, result]) => `
### ${name.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
- **Status**: ${result.success ? '✅ Passed' : '❌ Failed'}
- **User Experience**: ${result.userExperience || 'N/A'}
${result.errorMessageClarity ? `- **Error Message Clarity**: ${result.errorMessageClarity}/5.0` : ''}
${result.error ? `- **Error**: ${result.error}` : ''}
`).join('')}

## Recommendations
${recommendations.map(rec => `
### ${rec.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} (${rec.priority.toUpperCase()})
**Issue**: ${rec.issue}
**Suggestion**: ${rec.suggestion}
`).join('')}

## Next Steps
1. Address high-priority recommendations
2. Conduct user testing sessions with real users
3. Implement A/B testing for error message variations
4. Monitor error rates and user feedback in production

Generated on: ${new Date().toISOString()}
`;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async run() {
    try {
      await this.setup();
      await this.authenticate();
      
      // Run all test scenarios
      await this.testHappyPath();
      await this.testAIServiceTimeout();
      await this.testValidationErrors();
      await this.testNetworkIssues();
      await this.testFallbackDataUsage();
      
      await this.generateReport();
      
      console.log('🎉 UX validation testing completed successfully!');
      
    } catch (error) {
      console.error('💥 UX validation testing failed:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test if called directly
if (require.main === module) {
  const tester = new UXValidationTester();
  tester.run();
}

module.exports = UXValidationTester;
