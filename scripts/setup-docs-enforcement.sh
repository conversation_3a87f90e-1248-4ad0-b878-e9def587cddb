#!/bin/bash

# 📚 FAAFO Documentation Enforcement Setup
# This script sets up automatic enforcement of documentation organization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📚 Setting up Documentation Enforcement${NC}"
echo "============================================="

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ Not in a git repository. Please run this from the project root.${NC}"
    exit 1
fi

# Create pre-commit hook
echo -e "${BLUE}🔧 Creating pre-commit hook...${NC}"

PRE_COMMIT_HOOK=".git/hooks/pre-commit"

cat > "$PRE_COMMIT_HOOK" << 'EOF'
#!/bin/bash

# 📚 FAAFO Documentation Organization Pre-commit Hook
# Prevents commits with scattered documentation files

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}📚 Checking documentation organization...${NC}"

# Check for .md files outside docs/ (excluding README.md files and .augment config)
SCATTERED_DOCS=$(git diff --cached --name-only --diff-filter=A | grep '\.md$' | grep -v '^docs/' | grep -v '^README\.md$' | grep -v '^faafo-career-platform/README\.md$' | grep -v '^\.augment/' || true)

if [ -n "$SCATTERED_DOCS" ]; then
    echo -e "${RED}❌ COMMIT BLOCKED: Documentation files found outside docs/ directory!${NC}"
    echo ""
    echo -e "${RED}The following files should be moved to docs/:${NC}"
    echo "$SCATTERED_DOCS" | while read -r file; do
        echo -e "   ${RED}• $file${NC}"
    done
    echo ""
    echo -e "${YELLOW}💡 Please move these files to the appropriate docs/ subdirectory (atomic design):${NC}"
    echo -e "   • Reusable procedures → docs/atoms/"
    echo -e "   • Complete workflows → docs/workflows/"
    echo -e "   • Operations guides → docs/operations/"
    echo -e "   • Auto-generated content → docs/reference/"
    echo -e "   • Testing procedures → docs/testing/"
    echo -e "   • Legacy content → docs/archives/"
    echo ""
    echo -e "${BLUE}📖 For guidance, see: docs/DOCUMENTATION_ORGANIZATION_SYSTEM.md${NC}"
    echo -e "${BLUE}🔧 To validate structure: ./scripts/validate-docs-structure.sh${NC}"
    exit 1
fi

# Check for duplicate docs directories being added
DUPLICATE_DOCS=$(git diff --cached --name-only --diff-filter=A | grep '/docs/' | grep -v '^docs/' || true)

if [ -n "$DUPLICATE_DOCS" ]; then
    echo -e "${RED}❌ COMMIT BLOCKED: Duplicate docs directories detected!${NC}"
    echo ""
    echo -e "${RED}Files being added to duplicate docs directories:${NC}"
    echo "$DUPLICATE_DOCS" | while read -r file; do
        echo -e "   ${RED}• $file${NC}"
    done
    echo ""
    echo -e "${YELLOW}💡 All documentation should go in the main docs/ directory only.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Documentation organization looks good!${NC}"
EOF

# Make the hook executable
chmod +x "$PRE_COMMIT_HOOK"

echo -e "${GREEN}✅ Pre-commit hook created successfully${NC}"

# Create post-merge hook to remind about documentation validation
echo -e "${BLUE}🔧 Creating post-merge hook...${NC}"

POST_MERGE_HOOK=".git/hooks/post-merge"

cat > "$POST_MERGE_HOOK" << 'EOF'
#!/bin/bash

# 📚 FAAFO Documentation Organization Post-merge Hook
# Reminds to validate documentation after merges

# Colors for output
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}📚 Reminder: Run documentation validation after merge${NC}"
echo -e "${BLUE}🔧 Command: ./scripts/validate-docs-structure.sh${NC}"
EOF

chmod +x "$POST_MERGE_HOOK"

echo -e "${GREEN}✅ Post-merge hook created successfully${NC}"

# Create IDE configuration suggestions
echo -e "${BLUE}🔧 Creating IDE configuration suggestions...${NC}"

cat > ".vscode/settings.json.example" << 'EOF'
{
  "files.defaultLanguage": "markdown",
  "markdown.suggest.paths.enabled": true,
  "markdown.suggest.paths.includeWorkspaceHeaderCompletions": "onDoubleHash",
  "files.associations": {
    "*.md": "markdown"
  },
  "explorer.fileNesting.patterns": {
    "*.md": "${capture}.backup.md"
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/coverage": true,
    "**/.next": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/coverage": true,
    "**/.next": true,
    "**/backups": true
  }
}
EOF

echo -e "${GREEN}✅ IDE configuration example created${NC}"

# Create documentation template
echo -e "${BLUE}🔧 Creating documentation template...${NC}"

mkdir -p "docs/templates"

cat > "docs/templates/DOCUMENT_TEMPLATE.md" << 'EOF'
# Document Title

## 📋 Overview
Brief description of what this document covers.

## 🎯 Purpose
Why this document exists and who should read it.

## 📚 Content

### Section 1
Content here...

### Section 2
Content here...

## 🔗 Related Documentation
- [Related Doc 1](../category/related-doc.md)
- [Related Doc 2](../category/related-doc.md)

## 📝 Changelog
- YYYY-MM-DD: Initial version
- YYYY-MM-DD: Updated with new information

---
**Location**: `docs/[category]/[filename].md`  
**Last Updated**: YYYY-MM-DD  
**Maintainer**: [Your Name]
EOF

echo -e "${GREEN}✅ Documentation template created${NC}"

echo -e "\n${GREEN}🎉 Documentation enforcement setup complete!${NC}"
echo ""
echo -e "${BLUE}📋 What was set up:${NC}"
echo "• Pre-commit hook to prevent scattered documentation"
echo "• Post-merge hook to remind about validation"
echo "• IDE configuration suggestions"
echo "• Documentation template"
echo ""
echo -e "${BLUE}🔧 Next steps:${NC}"
echo "1. Run: ./scripts/validate-docs-structure.sh"
echo "2. Copy .vscode/settings.json.example to .vscode/settings.json (if using VS Code)"
echo "3. Use docs/templates/DOCUMENT_TEMPLATE.md for new documentation"
echo ""
echo -e "${YELLOW}💡 The pre-commit hook will now prevent scattered documentation!${NC}"
