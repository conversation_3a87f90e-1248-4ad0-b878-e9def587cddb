# Feature Request Template

## **FEATURE:**
[Describe what you want to build - be specific about functionality, user experience, and technical requirements]

**Example:**
```
Build a comprehensive skill assessment system that allows users to:
- Take adaptive assessments based on their career goals
- Receive detailed skill gap analysis with personalized recommendations
- Track progress over time with visual analytics
- Export results as PDF reports for career planning
```

## **TECHNICAL REQUIREMENTS:**
[Specify technical constraints, performance requirements, and integration needs]

**Example:**
```
- Must integrate with existing user authentication system
- Support real-time progress tracking during assessments
- Generate PDF reports using server-side rendering
- Implement caching for improved performance (<500ms response times)
- Mobile-responsive design with accessibility compliance
```

## **USER EXPERIENCE:**
[Define the user journey, interface requirements, and interaction patterns]

**Example:**
```
- Intuitive multi-step assessment flow with progress indicators
- Immediate feedback after each question with explanations
- Dashboard showing skill levels with visual progress charts
- Ability to retake assessments and compare historical results
- Social sharing capabilities for achievements
```

## **EXAMPLES:**
[Reference specific files in the examples/ folder that demonstrate patterns to follow]

**Example:**
```
- examples/assessment/adaptive-quiz.tsx - Assessment flow pattern
- examples/analytics/progress-charts.tsx - Data visualization approach
- examples/pdf/report-generator.ts - PDF generation implementation
- examples/api/assessment-endpoints.ts - API structure pattern
```

## **DOCUMENTATION:**
[Include links to relevant documentation, APIs, libraries, or external resources]

**Example:**
```
- Next.js App Router: https://nextjs.org/docs/app
- Prisma Database: https://www.prisma.io/docs
- Chart.js for analytics: https://www.chartjs.org/docs
- PDF generation: https://pdfkit.org/docs
- Accessibility guidelines: https://www.w3.org/WAI/WCAG21/quickref/
```

## **DATABASE CONSIDERATIONS:**
[Specify data models, relationships, and migration requirements]

**Example:**
```
- New tables: Assessment, Question, UserResponse, SkillLevel
- Relationships: User -> Assessment (one-to-many), Assessment -> Question (many-to-many)
- Indexes needed for performance on user_id and assessment_id
- Migration strategy for existing user data
```

## **INTEGRATION POINTS:**
[Define how this feature connects with existing systems]

**Example:**
```
- Authentication: Use existing NextAuth.js session management
- User profiles: Extend current user model with skill tracking
- Analytics: Integrate with existing dashboard components
- Notifications: Use current email service for assessment reminders
```

## **TESTING STRATEGY:**
[Define testing approach and acceptance criteria]

**Example:**
```
- Unit tests: All assessment logic and scoring algorithms
- Integration tests: Complete assessment flow from start to finish
- E2E tests: User journey through assessment and results viewing
- Performance tests: Load testing with 100+ concurrent users
- Accessibility tests: Screen reader and keyboard navigation
```

## **SUCCESS CRITERIA:**
[Define measurable outcomes that indicate successful implementation]

**Example:**
```
- Users can complete assessments in under 15 minutes
- 95% of users successfully generate and download PDF reports
- Assessment results load in under 2 seconds
- Zero critical accessibility violations
- 98% uptime during peak usage periods
```

## **OTHER CONSIDERATIONS:**
[Mention any gotchas, edge cases, or specific requirements that AI assistants commonly miss]

**Example:**
```
- Handle assessment interruptions (save progress, resume later)
- Prevent cheating through question randomization and time limits
- Support multiple languages for international users
- Graceful degradation for users with slow internet connections
- GDPR compliance for user data and assessment results
- Rate limiting to prevent assessment spam
```

## **PRIORITY LEVEL:**
[Indicate urgency and business impact]

**Options:** Critical | High | Medium | Low

## **ESTIMATED COMPLEXITY:**
[Help with resource planning]

**Options:** Simple (1-2 days) | Medium (3-5 days) | Complex (1-2 weeks) | Epic (2+ weeks)

---

**Instructions for Use:**
1. Fill out all relevant sections with specific details
2. Reference actual files in the examples/ folder when available
3. Be as specific as possible about requirements and constraints
4. Include edge cases and error scenarios
5. Save as `INITIAL_[feature-name].md` in the project root
6. Run `/generate-prp INITIAL_[feature-name].md` to create implementation blueprint
