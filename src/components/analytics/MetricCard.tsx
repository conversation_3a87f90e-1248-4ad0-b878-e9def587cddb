'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus, LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    period: string;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: LucideIcon;
  iconColor?: string;
  description?: string;
  className?: string;
  loading?: boolean;
  formatValue?: (value: string | number) => string;
}

export function MetricCard({
  title,
  value,
  change,
  icon: Icon,
  iconColor = 'text-blue-600',
  description,
  className,
  loading = false,
  formatValue,
}: MetricCardProps) {
  const formatNumber = (num: string | number) => {
    if (formatValue) {
      return formatValue(num);
    }
    
    if (typeof num === 'number') {
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return num.toLocaleString();
    }
    return num;
  };

  const getTrendIcon = () => {
    if (!change) return null;
    
    switch (change.type) {
      case 'increase':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'decrease':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'neutral':
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = () => {
    if (!change) return '';
    
    switch (change.type) {
      case 'increase':
        return 'text-green-600';
      case 'decrease':
        return 'text-red-600';
      case 'neutral':
      default:
        return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <Card className={cn('p-6', className)}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-2">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
            <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn('p-6 hover:shadow-md transition-shadow', className)}>
      <div className="flex items-center justify-between mb-2">
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
          {title}
        </p>
        {Icon && (
          <Icon className={cn('h-5 w-5', iconColor)} />
        )}
      </div>
      
      <div className="flex items-baseline space-x-2 mb-2">
        <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {formatNumber(value)}
        </p>
        
        {change && (
          <div className={cn('flex items-center space-x-1', getTrendColor())}>
            {getTrendIcon()}
            <span className="text-sm font-medium">
              {Math.abs(change.value)}%
            </span>
          </div>
        )}
      </div>
      
      {(description || change) && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {description && <p>{description}</p>}
          {change && (
            <p>
              {change.type === 'increase' ? '+' : change.type === 'decrease' ? '-' : ''}
              {Math.abs(change.value)}% from {change.period}
            </p>
          )}
        </div>
      )}
    </Card>
  );
}

// Specialized metric cards for common use cases
export function UserMetricCard({ 
  totalUsers, 
  activeUsers, 
  newUsers, 
  loading = false 
}: {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  loading?: boolean;
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <MetricCard
        title="Total Users"
        value={totalUsers}
        icon={TrendingUp}
        iconColor="text-blue-600"
        loading={loading}
      />
      <MetricCard
        title="Active Users"
        value={activeUsers}
        icon={TrendingUp}
        iconColor="text-green-600"
        loading={loading}
      />
      <MetricCard
        title="New Users"
        value={newUsers}
        icon={TrendingUp}
        iconColor="text-purple-600"
        loading={loading}
      />
    </div>
  );
}

export function LearningMetricCard({ 
  totalResources, 
  completedResources, 
  completionRate, 
  loading = false 
}: {
  totalResources: number;
  completedResources: number;
  completionRate: number;
  loading?: boolean;
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <MetricCard
        title="Total Resources"
        value={totalResources}
        icon={TrendingUp}
        iconColor="text-blue-600"
        loading={loading}
      />
      <MetricCard
        title="Completed"
        value={completedResources}
        icon={TrendingUp}
        iconColor="text-green-600"
        loading={loading}
      />
      <MetricCard
        title="Completion Rate"
        value={`${completionRate.toFixed(1)}%`}
        icon={TrendingUp}
        iconColor="text-purple-600"
        loading={loading}
      />
    </div>
  );
}
