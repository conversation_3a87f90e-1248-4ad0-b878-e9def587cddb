'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';

interface PieChartProps {
  data: Array<{
    name: string;
    value: number;
    color?: string;
  }>;
  title?: string;
  height?: number;
  showLegend?: boolean;
  showLabels?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  formatTooltip?: (value: any, name: string) => [string, string];
  colors?: string[];
}

const DEFAULT_COLORS = [
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7300',
  '#00ff00',
  '#ff00ff',
  '#00ffff',
  '#ff0000',
  '#0000ff',
  '#ffff00',
];

export function PieChart({
  data,
  title,
  height = 300,
  showLegend = true,
  showLabels = true,
  innerRadius = 0,
  outerRadius = 80,
  formatTooltip,
  colors = DEFAULT_COLORS,
}: PieChartProps) {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadRecharts = async () => {
      try {
        const recharts = await import('recharts');
        setRechartsComponents({
          PieChart: recharts.PieChart,
          Pie: recharts.Pie,
          Cell: recharts.Cell,
          ResponsiveContainer: recharts.ResponsiveContainer,
          Tooltip: recharts.Tooltip,
          Legend: recharts.Legend,
        });
      } catch (error) {
        console.error('Failed to load Recharts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecharts();
  }, []);

  if (isLoading) {
    return (
      <Card className="p-6">
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div
          className="flex items-center justify-center bg-gray-100 rounded animate-pulse"
          style={{ height }}
        >
          <div className="text-gray-500">Loading chart...</div>
        </div>
      </Card>
    );
  }

  if (!RechartsComponents) {
    return (
      <Card className="p-6">
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div
          className="flex items-center justify-center bg-red-50 rounded border border-red-200"
          style={{ height }}
        >
          <div className="text-red-600">Failed to load chart</div>
        </div>
      </Card>
    );
  }

  const {
    PieChart: RechartsPieChart,
    Pie,
    Cell,
    ResponsiveContainer,
    Tooltip,
    Legend,
  } = RechartsComponents;
  const defaultTooltipFormatter = (value: any, name: string) => [
    typeof value === 'number' ? value.toLocaleString() : value,
    name,
  ];

  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
    name,
  }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices smaller than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Assign colors to data items
  const dataWithColors = data.map((item, index) => ({
    ...item,
    color: item.color || colors[index % colors.length],
  }));

  return (
    <Card className="p-6">
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        </div>
      )}
      
      <div style={{ width: '100%', height }}>
        <ResponsiveContainer>
          <RechartsPieChart>
            <Pie
              data={dataWithColors}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={showLabels ? renderCustomizedLabel : false}
              outerRadius={outerRadius}
              innerRadius={innerRadius}
              fill="#8884d8"
              dataKey="value"
            >
              {dataWithColors.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            
            <Tooltip
              formatter={formatTooltip || defaultTooltipFormatter}
              contentStyle={{
                backgroundColor: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '6px',
                color: 'var(--foreground)',
              }}
            />
            
            {showLegend && (
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(value: any, entry: any) => (
                  <span style={{ color: entry.color }}>{value}</span>
                )}
              />
            )}
          </RechartsPieChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}

// Donut Chart variant
export function DonutChart(props: PieChartProps) {
  return <PieChart {...props} innerRadius={40} outerRadius={80} />;
}
