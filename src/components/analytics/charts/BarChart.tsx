'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';

interface BarChartProps {
  data: Array<Record<string, any>>;
  xAxisKey: string;
  bars: Array<{
    key: string;
    name: string;
    color: string;
  }>;
  title?: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  orientation?: 'horizontal' | 'vertical';
  formatTooltip?: (value: any, name: string) => [string, string];
  formatXAxis?: (value: any) => string;
  formatYAxis?: (value: any) => string;
}

export function BarChart({
  data,
  xAxisKey,
  bars,
  title,
  height = 300,
  showGrid = true,
  showLegend = true,
  orientation = 'vertical',
  formatTooltip,
  formatXAxis,
  formatYAxis,
}: BarChartProps) {
  const [RechartsComponents, setRechartsComponents] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadRecharts = async () => {
      try {
        const recharts = await import('recharts');
        setRechartsComponents({
          BarChart: recharts.BarChart,
          Bar: recharts.Bar,
          XAxis: recharts.XAxis,
          YAxis: recharts.YAxis,
          CartesianGrid: recharts.CartesianGrid,
          Tooltip: recharts.Tooltip,
          ResponsiveContainer: recharts.ResponsiveContainer,
          Legend: recharts.Legend,
        });
      } catch (error) {
        console.error('Failed to load Recharts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecharts();
  }, []);

  if (isLoading) {
    return (
      <Card className="p-6">
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div
          className="flex items-center justify-center bg-gray-100 rounded animate-pulse"
          style={{ height }}
        >
          <div className="text-gray-500">Loading chart...</div>
        </div>
      </Card>
    );
  }

  if (!RechartsComponents) {
    return (
      <Card className="p-6">
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div
          className="flex items-center justify-center bg-red-50 rounded border border-red-200"
          style={{ height }}
        >
          <div className="text-red-600">Failed to load chart</div>
        </div>
      </Card>
    );
  }

  const {
    BarChart: RechartsBarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Legend,
  } = RechartsComponents;
  const defaultTooltipFormatter = (value: any, name: string) => [
    typeof value === 'number' ? value.toLocaleString() : value,
    name,
  ];

  const defaultAxisFormatter = (value: any) => {
    if (typeof value === 'number') {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toString();
    }
    return value;
  };

  const defaultLabelFormatter = (value: any) => {
    if (typeof value === 'string' && value.length > 15) {
      return value.substring(0, 12) + '...';
    }
    return value;
  };

  return (
    <Card className="p-6">
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        </div>
      )}
      
      <div style={{ width: '100%', height }}>
        <ResponsiveContainer>
          <RechartsBarChart
            data={data}
            layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-gray-200 dark:stroke-gray-700"
              />
            )}
            
            <XAxis
              type={orientation === 'horizontal' ? 'number' : 'category'}
              dataKey={orientation === 'horizontal' ? undefined : xAxisKey}
              tickFormatter={orientation === 'horizontal' 
                ? (formatXAxis || defaultAxisFormatter)
                : (formatXAxis || defaultLabelFormatter)
              }
              className="text-gray-600 dark:text-gray-400"
              fontSize={12}
            />
            
            <YAxis
              type={orientation === 'horizontal' ? 'category' : 'number'}
              dataKey={orientation === 'horizontal' ? xAxisKey : undefined}
              tickFormatter={orientation === 'horizontal' 
                ? (formatYAxis || defaultLabelFormatter)
                : (formatYAxis || defaultAxisFormatter)
              }
              className="text-gray-600 dark:text-gray-400"
              fontSize={12}
            />
            
            <Tooltip
              formatter={formatTooltip || defaultTooltipFormatter}
              contentStyle={{
                backgroundColor: 'var(--background)',
                border: '1px solid var(--border)',
                borderRadius: '6px',
                color: 'var(--foreground)',
              }}
            />
            
            {showLegend && <Legend />}
            
            {bars.map((bar) => (
              <Bar
                key={bar.key}
                dataKey={bar.key}
                name={bar.name}
                fill={bar.color}
                radius={[2, 2, 0, 0]}
              />
            ))}
          </RechartsBarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}
