#!/usr/bin/env node

/**
 * Simple Audit System Test
 * 
 * Basic validation that all files exist and have correct structure
 */

const fs = require('fs');
const path = require('path');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

function testFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  logTest(`${description} exists`, exists, filePath);
  return exists;
}

function testFileContent(filePath, description, checks) {
  if (!fs.existsSync(filePath)) {
    logTest(`${description} content`, false, 'File does not exist');
    return false;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let allPassed = true;
    
    for (const [checkName, checkFn] of Object.entries(checks)) {
      const passed = checkFn(content);
      if (!passed) allPassed = false;
      logTest(`${description} ${checkName}`, passed);
    }
    
    return allPassed;
  } catch (error) {
    logTest(`${description} content`, false, error.message);
    return false;
  }
}

async function runSimpleTests() {
  console.log('🚀 Starting Simple Audit System Validation...');
  console.log('=' .repeat(60));
  
  console.log('\n📁 Testing Core Files...');
  
  // Test core audit files
  testFileExists('src/lib/audit/index.ts', 'Audit index');
  testFileExists('src/lib/audit/types.ts', 'Audit types');
  testFileExists('src/lib/audit/core-audit-engine.ts', 'Core audit engine');
  testFileExists('src/lib/audit/audit-service.ts', 'Audit service');
  testFileExists('src/lib/audit/storage/audit-storage.ts', 'Audit storage');
  
  console.log('\n🔍 Testing Analyzers...');
  
  // Test analyzer files
  const analyzers = [
    'typescript-analyzer.ts',
    'eslint-analyzer.ts', 
    'security-analyzer.ts',
    'performance-analyzer.ts',
    'architecture-analyzer.ts',
    'prisma-analyzer.ts',
    'test-coverage-analyzer.ts',
    'api-analyzer.ts',
    'component-analyzer.ts',
    'dependency-analyzer.ts'
  ];
  
  for (const analyzer of analyzers) {
    testFileExists(`src/lib/audit/analyzers/${analyzer}`, analyzer);
  }
  
  console.log('\n🌐 Testing API Routes...');
  
  // Test API routes
  testFileExists('src/app/api/audit/runs/route.ts', 'Runs API route');
  testFileExists('src/app/api/audit/runs/[id]/route.ts', 'Run detail API route');
  testFileExists('src/app/api/audit/issues/route.ts', 'Issues API route');
  testFileExists('src/app/api/audit/issues/[id]/route.ts', 'Issue detail API route');
  testFileExists('src/app/api/audit/issues/[id]/comments/route.ts', 'Issue comments API route');
  
  console.log('\n🎨 Testing UI Components...');
  
  // Test UI components
  testFileExists('src/app/audit/page.tsx', 'Main audit page');
  testFileExists('src/app/audit/runs/page.tsx', 'Audit runs page');
  testFileExists('src/app/audit/issues/page.tsx', 'Audit issues page');
  testFileExists('src/app/audit/issues/[id]/page.tsx', 'Issue detail page');
  
  testFileExists('src/components/audit/AuditDashboard.tsx', 'Audit dashboard component');
  testFileExists('src/components/audit/AuditMetrics.tsx', 'Audit metrics component');
  testFileExists('src/components/audit/RecentIssues.tsx', 'Recent issues component');
  testFileExists('src/components/audit/AuditRunsList.tsx', 'Audit runs list component');
  testFileExists('src/components/audit/AuditIssuesPage.tsx', 'Audit issues page component');
  testFileExists('src/components/audit/AuditIssueDetailPage.tsx', 'Audit issue detail component');
  testFileExists('src/components/audit/AuditRunsPage.tsx', 'Audit runs page component');
  
  console.log('\n🗄️ Testing Database Schema...');
  
  // Test database schema
  testFileContent('prisma/schema.prisma', 'Prisma schema', {
    'has AuditRun model': (content) => content.includes('model AuditRun'),
    'has AuditIssue model': (content) => content.includes('model AuditIssue'),
    'has IssueComment model': (content) => content.includes('model IssueComment'),
    'has IssueSeverity enum': (content) => content.includes('enum IssueSeverity'),
    'has IssueCategory enum': (content) => content.includes('enum IssueCategory'),
    'has IssueStatus enum': (content) => content.includes('enum IssueStatus'),
    'has AuditStatus enum': (content) => content.includes('enum AuditStatus')
  });
  
  console.log('\n🧭 Testing Navigation Integration...');
  
  // Test navigation integration
  testFileContent('src/components/layout/NavigationBar.tsx', 'Navigation bar', {
    'has audit link': (content) => content.includes('/audit'),
    'has Bug icon import': (content) => content.includes('Bug'),
    'has admin check': (content) => content.includes('isAdmin')
  });
  
  testFileContent('middleware.ts', 'Middleware', {
    'protects audit routes': (content) => content.includes('/audit'),
    'has admin routes': (content) => content.includes('adminRoutes'),
    'protects audit API': (content) => content.includes('/api/audit')
  });
  
  console.log('\n🔒 Testing Security...');
  
  // Test security implementation
  testFileContent('src/lib/auth-utils.ts', 'Auth utils', {
    'has requireAdmin function': (content) => content.includes('requireAdmin'),
    'has isUserAdmin function': (content) => content.includes('isUserAdmin'),
    'has admin check': (content) => content.includes('isAdminUser')
  });
  
  console.log('\n🧪 Testing Test Files...');
  
  // Test test files
  testFileExists('src/components/audit/__tests__/audit-dashboard.test.tsx', 'Audit dashboard test');
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  
  if (testResults.passed + testResults.failed > 0) {
    console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  }
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🎉 Simple Validation Complete!');
  
  // Summary assessment
  const successRate = Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100);
  
  if (successRate >= 95) {
    console.log('\n🌟 EXCELLENT: Audit system is fully implemented and ready!');
  } else if (successRate >= 85) {
    console.log('\n✨ GOOD: Audit system is mostly complete with minor issues.');
  } else if (successRate >= 70) {
    console.log('\n⚠️  FAIR: Audit system has significant gaps that need attention.');
  } else {
    console.log('\n🚨 POOR: Audit system needs major work before it can be used.');
  }
}

// Run the tests
runSimpleTests().catch(console.error);
