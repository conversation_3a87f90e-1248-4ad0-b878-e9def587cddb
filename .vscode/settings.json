{"files.watcherExclude": {"**/node_modules/**": true, "**/.git/**": true, "**/.next/**": true, "**/dist/**": true, "**/build/**": true, "**/coverage/**": true, "**/.nyc_output/**": true, "**/tmp/**": true, "**/temp/**": true, "**/*.log": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.next": true, "**/dist": true, "**/build": true, "**/coverage": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.nyc_output": true, "**/tmp": true, "**/temp": true}, "git.ignoreLimitWarning": true, "typescript.preferences.includePackageJsonAutoImports": "off"}