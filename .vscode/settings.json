{"files.watcherExclude": {"**/node_modules/**": true, "**/.git/**": true, "**/.next/**": true, "**/dist/**": true, "**/build/**": true, "**/coverage/**": true, "**/.nyc_output/**": true, "**/tmp/**": true, "**/temp/**": true, "**/*.log": true, "**/tsconfig.tsbuildinfo": true, "**/playwright-report/**": true, "**/test-results/**": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.next": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/tsconfig.tsbuildinfo": true, "**/playwright-report": true, "**/test-results": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.nyc_output": true, "**/tmp": true, "**/temp": true, "**/tsconfig.tsbuildinfo": true, "**/playwright-report": true, "**/test-results": true}, "git.ignoreLimitWarning": true, "typescript.preferences.includePackageJsonAutoImports": "off", "typescript.tsc.autoDetect": "off", "typescript.disableAutomaticTypeAcquisition": true, "typescript.suggest.autoImports": false, "typescript.preferences.includeCompletionsForModuleExports": false, "typescript.maxTsServerMemory": 8192, "typescript.updateImportsOnFileMove.enabled": "never", "typescript.preferences.includeCompletionsWithSnippetText": false, "typescript.suggest.includeCompletionsForImportStatements": false, "files.watcherExcludePatterns": ["**/node_modules/**", "**/.git/**", "**/coverage/**", "**/tsconfig.tsbuildinfo"], "extensions.autoUpdate": false, "extensions.autoCheckUpdates": false, "workbench.settings.enableNaturalLanguageSearch": false, "search.followSymlinks": false, "files.hotExit": "off"}