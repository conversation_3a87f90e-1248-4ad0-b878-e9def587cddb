# [Feature Name] - Product Requirements Prompt

## **EXECUTIVE SUMMARY**
[Brief overview of the feature, its business value, and impact on users]

**Key Objectives:**
- [Primary objective 1]
- [Primary objective 2]
- [Primary objective 3]

**Success Metrics:**
- [Measurable outcome 1]
- [Measurable outcome 2]
- [Measurable outcome 3]

## **COMPLETE CONTEXT**

### **Project Architecture**
**Current System Overview:**
- Next.js 14.2.5 with App Router
- TypeScript for type safety
- Prisma ORM with PostgreSQL (prod) / SQLite (dev)
- NextAuth.js for authentication
- Tailwind CSS for styling
- Jest + React Testing Library for testing

**Integration Points:**
- [How this feature connects to existing systems]
- [Dependencies on current services]
- [Impact on existing user flows]

### **Existing Patterns**
**Code Conventions:**
- [Specific patterns from codebase analysis]
- [Component architecture to follow]
- [API endpoint patterns]
- [Database interaction patterns]

**UI/UX Patterns:**
- [Design system components to use]
- [Navigation patterns]
- [Form handling approaches]
- [Error handling UI patterns]

### **Dependencies**
**Required Libraries:**
- [New dependencies needed]
- [Version compatibility requirements]
- [Integration with existing packages]

**External Services:**
- [APIs or services to integrate]
- [Authentication requirements]
- [Rate limiting considerations]

## **DETAILED REQUIREMENTS**

### **Functional Requirements**
**Core Functionality:**
1. [Requirement 1 with acceptance criteria]
2. [Requirement 2 with acceptance criteria]
3. [Requirement 3 with acceptance criteria]

**User Interactions:**
- [Specific user actions and expected responses]
- [Input validation and error handling]
- [Success and failure scenarios]

### **Technical Requirements**
**Performance:**
- API response times: <500ms
- Page load times: <2s initial load
- Database query optimization
- Caching strategy implementation

**Security:**
- Input validation with Zod schemas
- Authentication and authorization
- Rate limiting implementation
- Data sanitization

**Scalability:**
- Database indexing strategy
- Caching implementation
- Resource optimization
- Load testing requirements

### **User Experience Requirements**
**Interface Design:**
- [Specific UI components and layouts]
- [Responsive design requirements]
- [Accessibility compliance (WCAG 2.1)]
- [Loading states and error handling]

**User Journey:**
- [Step-by-step user flow]
- [Decision points and branching]
- [Error recovery paths]
- [Success confirmation patterns]

## **IMPLEMENTATION PLAN**

### **Phase 1: Foundation (Day 1)**
**Database Schema:**
```sql
-- [Detailed schema with relationships]
-- [Indexes for performance]
-- [Migration strategy]
```

**Core Models:**
```typescript
// [TypeScript interfaces]
// [Zod validation schemas]
// [Prisma model definitions]
```

**Validation Criteria:**
- [ ] Database migration successful
- [ ] Schema validation passes
- [ ] Core types compile without errors

### **Phase 2: API Implementation (Day 2)**
**Backend Services:**
```typescript
// [API route implementations]
// [Service layer functions]
// [Error handling patterns]
```

**Authentication Integration:**
```typescript
// [NextAuth.js integration]
// [Permission validation]
// [Session management]
```

**Validation Criteria:**
- [ ] All API endpoints respond correctly
- [ ] Authentication/authorization working
- [ ] >95% test coverage achieved
- [ ] Performance targets met (<500ms)

### **Phase 3: Frontend Components (Day 3)**
**UI Components:**
```typescript
// [React component implementations]
// [TypeScript prop interfaces]
// [Styling with Tailwind CSS]
```

**State Management:**
```typescript
// [Data fetching with SWR/React Query]
// [Form handling with react-hook-form]
// [Loading and error states]
```

**Validation Criteria:**
- [ ] Components render without errors
- [ ] Responsive design verified
- [ ] Accessibility standards met
- [ ] User interactions working

### **Phase 4: Integration & Testing (Day 4)**
**End-to-End Flows:**
```typescript
// [Complete user journey implementation]
// [Integration testing]
// [Data consistency validation]
```

**Performance Optimization:**
```typescript
// [Database query optimization]
// [Caching implementation]
// [Bundle size optimization]
```

**Validation Criteria:**
- [ ] Complete user flows working
- [ ] Performance benchmarks met
- [ ] No console errors or warnings
- [ ] Cross-browser compatibility verified

### **Phase 5: Quality Assurance (Day 5)**
**Testing Suite:**
```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Build verification
npm run build
```

**Final Validation:**
- [ ] All tests passing (>95% coverage)
- [ ] Build successful without warnings
- [ ] Security audit passed
- [ ] Performance targets achieved
- [ ] Documentation updated

## **TECHNICAL SPECIFICATIONS**

### **Database Schema**
```prisma
// [Complete Prisma schema]
// [Relationships and constraints]
// [Indexes for performance]
```

### **API Endpoints**
```typescript
// GET /api/[endpoint]
// POST /api/[endpoint]
// PUT /api/[endpoint]
// DELETE /api/[endpoint]

// [Request/response examples]
// [Error response formats]
// [Authentication requirements]
```

### **Component Architecture**
```typescript
// [Component hierarchy]
// [Props interfaces]
// [State management patterns]
// [Event handling]
```

### **State Management**
```typescript
// [Data flow patterns]
// [Caching strategies]
// [Optimistic updates]
// [Error boundary implementation]
```

## **VALIDATION CRITERIA**

### **Success Metrics**
- [Quantifiable performance targets]
- [User experience benchmarks]
- [Business value indicators]
- [Technical quality measures]

### **Test Requirements**
**Unit Tests:**
- [Specific test cases for core logic]
- [Edge case scenarios]
- [Error handling validation]

**Integration Tests:**
- [API endpoint testing]
- [Database interaction testing]
- [Authentication flow testing]

**E2E Tests:**
- [Complete user journey testing]
- [Cross-browser validation]
- [Mobile responsiveness testing]

### **Quality Gates**
- Build successful without warnings
- All tests passing with >95% coverage
- Performance targets met
- Security validation passed
- Accessibility compliance verified
- Documentation complete

## **RISK MITIGATION**

### **Technical Risks**
- [Potential technical challenges]
- [Mitigation strategies]
- [Fallback approaches]

### **Performance Risks**
- [Scalability concerns]
- [Optimization strategies]
- [Monitoring implementation]

### **Security Risks**
- [Potential vulnerabilities]
- [Security measures]
- [Validation strategies]

## **ROLLBACK PLAN**

### **Database Rollback**
```bash
# [Specific rollback commands]
# [Data preservation strategies]
# [Recovery procedures]
```

### **Code Rollback**
```bash
# [Git revert procedures]
# [Dependency restoration]
# [Configuration reset]
```

### **Monitoring & Alerts**
- [Error detection strategies]
- [Performance monitoring]
- [User impact assessment]

---

**Implementation Confidence:** [Score 1-10 with justification]

**Next Steps:** [Specific actions to begin implementation]
