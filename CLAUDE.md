# FAAFO Career Platform - AI Assistant Rules

## **🎯 PROJECT OVERVIEW**

You are working on the **FAAFO Career Platform** - a comprehensive career development application built with Next.js, TypeScript, Prisma, and PostgreSQL. This platform helps users with skill assessments, career planning, resume building, interview practice, and salary analysis.

## **📋 CORE RESPONSIBILITIES**

### **1. Project Awareness**
- **ALWAYS** read `.project_context.md` before starting any task
- **ALWAYS** run `./scripts/core/ultimate-project-discovery.sh` for current state analysis
- Check existing task lists and documentation before creating new work
- Understand the full scope before making changes

### **2. Code Quality Standards**
- **File Size Limit**: Maximum 150 lines per edit operation
- **Modular Architecture**: Break large components into smaller, focused modules
- **TypeScript First**: All new code must use proper TypeScript types
- **Error Handling**: Implement comprehensive error boundaries and validation
- **Performance**: Optimize for Core Web Vitals and user experience

### **3. Testing Requirements**
- **Test-Driven Development**: Write tests BEFORE implementation
- **Coverage Target**: >95% test coverage for all new features
- **Testing Order**: Database schema → Jest components → API endpoints → Integration flows
- **Real Testing**: Use actual API calls and database connections, not mocks
- **Validation**: Every feature must pass end-to-end testing before completion

### **4. Development Workflow**
- **Context Engineering**: Use `/generate-prp` and `/execute-prp` commands for complex features
- **Systematic Approach**: Break complex problems into 20-minute focused tasks
- **Verification**: Examine actual files/systems to validate completion, not just task lists
- **Documentation**: Update documentation throughout development process
- **Build Testing**: Run `npm run build` after significant changes

## **🏗️ ARCHITECTURE PATTERNS**

### **Frontend (Next.js App Router)**
```typescript
// Component Structure
src/
├── app/                 # App Router pages and layouts
├── components/          # Reusable UI components
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries and configurations
└── utils/              # Helper functions
```

### **Backend (API Routes)**
```typescript
// API Pattern
export const POST = withUnifiedErrorHandling(async (request: Request) => {
  // Implementation with proper error handling
});
```

### **Database (Prisma)**
```typescript
// Database Operations
const result = await prisma.model.operation({
  // Proper error handling and validation
});
```

## **🔒 SECURITY STANDARDS**

### **Authentication & Authorization**
- Use NextAuth.js for authentication
- Implement proper session management
- Validate user permissions for all operations
- Sanitize all user inputs

### **Data Protection**
- Validate all API inputs with Zod schemas
- Use parameterized queries (Prisma handles this)
- Implement rate limiting on sensitive endpoints
- Follow OWASP security guidelines

## **📊 PERFORMANCE REQUIREMENTS**

### **Response Times**
- API endpoints: <500ms response time
- Page loads: <2s initial load
- Database queries: Optimized with proper indexing
- Image optimization: Use Next.js Image component

### **Monitoring**
- Sentry for error tracking
- Performance monitoring for Core Web Vitals
- Database query optimization
- Memory leak prevention

## **🧪 TESTING PROTOCOLS**

### **Unit Testing (Jest)**
```typescript
// Test Pattern
describe('Component/Function', () => {
  it('should handle specific scenario', () => {
    // Arrange, Act, Assert
  });
});
```

### **Integration Testing**
- Test complete user flows
- Use test credentials: `<EMAIL> / TestPassword123!`
- Validate database state changes
- Test API endpoint interactions

### **End-to-End Testing**
- Use localhost:3000 for development testing
- Test critical user journeys
- Validate UI interactions and state management
- Ensure responsive design across devices

## **📝 DOCUMENTATION STANDARDS**

### **Code Documentation**
```typescript
/**
 * Brief description of function/component
 * @param param1 - Description of parameter
 * @returns Description of return value
 */
```

### **API Documentation**
- Document all endpoints with request/response examples
- Include error scenarios and status codes
- Provide authentication requirements
- Update OpenAPI specs when available

## **🚀 DEPLOYMENT STANDARDS**

### **Environment Configuration**
- Development: SQLite database
- Production: Neon PostgreSQL
- Environment variables properly configured
- Secrets management through Vercel

### **Build Process**
- TypeScript compilation without errors
- All tests passing
- No console errors or warnings
- Performance budgets met

## **🔄 CONTEXT ENGINEERING WORKFLOW**

### **Feature Development Process**
1. **Brief**: Create detailed `INITIAL.md` with requirements
2. **Plan**: Run `/generate-prp INITIAL.md` to create comprehensive blueprint
3. **Execute**: Run `/execute-prp PRPs/feature-name.md` to implement
4. **Validate**: Comprehensive testing and verification

### **Quality Gates**
- All tests passing (>95% coverage)
- Build successful without warnings
- Performance metrics within targets
- Security validation complete
- Documentation updated

## **⚠️ CRITICAL CONSTRAINTS**

### **Version Requirements**
- Next.js: ^14.2.5 (NOT ^15.x due to build issues)
- Node.js: Compatible with Vercel deployment
- Dependencies: Keep updated but test thoroughly

### **Database Considerations**
- Prisma migrations: Always test in development first
- Data integrity: Validate all schema changes
- Performance: Monitor query performance
- Backups: Ensure proper backup procedures

### **Production Readiness**
- Zero critical vulnerabilities
- Comprehensive error handling
- Proper logging and monitoring
- Performance optimization complete
- Security audit passed

## **🎯 SUCCESS CRITERIA**

Every task completion must meet:
- ✅ All tests passing with >95% coverage
- ✅ Build successful without errors
- ✅ Performance targets met
- ✅ Security standards followed
- ✅ Documentation updated
- ✅ User experience validated
- ✅ Code review standards met

---

**Remember**: Context Engineering is about providing complete, comprehensive context for successful implementation. Use the `/generate-prp` and `/execute-prp` workflow for complex features to ensure systematic, high-quality development.
