#!/usr/bin/env node

/**
 * Audit API Endpoints Testing Script
 * 
 * Tests all audit API endpoints to ensure they work correctly
 */

const fs = require('fs');
const path = require('path');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

async function testAPIRouteFiles() {
  console.log('\n🌐 Testing API Route Files...');
  
  const apiFiles = [
    'src/app/api/audit/runs/route.ts',
    'src/app/api/audit/runs/[id]/route.ts',
    'src/app/api/audit/issues/route.ts',
    'src/app/api/audit/issues/[id]/route.ts',
    'src/app/api/audit/issues/[id]/comments/route.ts'
  ];
  
  for (const file of apiFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`API route exists: ${file}`, exists);
    
    if (exists) {
      try {
        const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
        
        // Check for required exports
        const hasGETExport = content.includes('export const GET');
        const hasPOSTExport = content.includes('export const POST');
        const hasPATCHExport = content.includes('export const PATCH');
        
        // Check for security features
        const hasErrorHandling = content.includes('withUnifiedErrorHandling');
        const hasRateLimit = content.includes('withRateLimit');
        const hasAuth = content.includes('getServerSession');
        const hasAdminCheck = content.includes('isAdminUser') || content.includes('requireAdmin');
        
        if (file.includes('/runs/route.ts')) {
          logTest(`${file} has GET endpoint`, hasGETExport);
          logTest(`${file} has POST endpoint`, hasPOSTExport);
        } else if (file.includes('/issues/route.ts')) {
          logTest(`${file} has GET endpoint`, hasGETExport);
        } else if (file.includes('/issues/[id]/route.ts')) {
          logTest(`${file} has GET endpoint`, hasGETExport);
          logTest(`${file} has PATCH endpoint`, hasPATCHExport);
        } else if (file.includes('/comments/route.ts')) {
          logTest(`${file} has POST endpoint`, hasPOSTExport);
        }
        
        logTest(`${file} has error handling`, hasErrorHandling);
        logTest(`${file} has rate limiting`, hasRateLimit);
        logTest(`${file} has authentication`, hasAuth);
        logTest(`${file} has admin check`, hasAdminCheck);
        
      } catch (error) {
        logTest(`${file} content validation`, false, error.message);
      }
    }
  }
}

async function testCoreAuditFiles() {
  console.log('\n🔧 Testing Core Audit Files...');
  
  const coreFiles = [
    'src/lib/audit/core-audit-engine.ts',
    'src/lib/audit/audit-service.ts',
    'src/lib/audit/types.ts',
    'src/lib/audit/index.ts',
    'src/lib/audit/storage/audit-storage.ts'
  ];
  
  for (const file of coreFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`Core file exists: ${file}`, exists);
    
    if (exists) {
      try {
        const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
        
        if (file.includes('types.ts')) {
          const hasAuditEngine = content.includes('interface AuditEngine');
          const hasIssueSeverity = content.includes('IssueSeverity');
          const hasIssueCategory = content.includes('IssueCategory');
          logTest(`${file} has AuditEngine interface`, hasAuditEngine);
          logTest(`${file} has IssueSeverity enum`, hasIssueSeverity);
          logTest(`${file} has IssueCategory enum`, hasIssueCategory);
        }
        
        if (file.includes('audit-service.ts')) {
          const hasAuditService = content.includes('class AuditService') || content.includes('export class AuditService');
          const hasStartAudit = content.includes('startAudit');
          const hasGetIssues = content.includes('getIssues');
          logTest(`${file} has AuditService class`, hasAuditService);
          logTest(`${file} has startAudit method`, hasStartAudit);
          logTest(`${file} has getIssues method`, hasGetIssues);
        }
        
        if (file.includes('core-audit-engine.ts')) {
          const hasCoreAuditEngine = content.includes('class CoreAuditEngine') || content.includes('export class CoreAuditEngine');
          const hasRunAudit = content.includes('runAudit');
          logTest(`${file} has CoreAuditEngine class`, hasCoreAuditEngine);
          logTest(`${file} has runAudit method`, hasRunAudit);
        }
        
      } catch (error) {
        logTest(`${file} content validation`, false, error.message);
      }
    }
  }
}

async function testAnalyzerFiles() {
  console.log('\n🔍 Testing Analyzer Files...');
  
  const analyzerFiles = [
    'src/lib/audit/analyzers/typescript-analyzer.ts',
    'src/lib/audit/analyzers/eslint-analyzer.ts',
    'src/lib/audit/analyzers/security-analyzer.ts',
    'src/lib/audit/analyzers/performance-analyzer.ts',
    'src/lib/audit/analyzers/architecture-analyzer.ts',
    'src/lib/audit/analyzers/prisma-analyzer.ts',
    'src/lib/audit/analyzers/test-coverage-analyzer.ts',
    'src/lib/audit/analyzers/api-analyzer.ts',
    'src/lib/audit/analyzers/component-analyzer.ts',
    'src/lib/audit/analyzers/dependency-analyzer.ts'
  ];
  
  for (const file of analyzerFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`Analyzer exists: ${path.basename(file)}`, exists);
    
    if (exists) {
      try {
        const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
        const hasAnalyzeMethod = content.includes('analyze(') || content.includes('async analyze');
        const hasExport = content.includes('export class') || content.includes('export default');
        logTest(`${path.basename(file)} has analyze method`, hasAnalyzeMethod);
        logTest(`${path.basename(file)} has proper export`, hasExport);
      } catch (error) {
        logTest(`${path.basename(file)} content validation`, false, error.message);
      }
    }
  }
}

async function testUIComponents() {
  console.log('\n🎨 Testing UI Components...');
  
  const uiFiles = [
    'src/app/audit/page.tsx',
    'src/app/audit/runs/page.tsx',
    'src/app/audit/issues/page.tsx',
    'src/app/audit/issues/[id]/page.tsx',
    'src/components/audit/AuditDashboard.tsx',
    'src/components/audit/AuditMetrics.tsx',
    'src/components/audit/RecentIssues.tsx',
    'src/components/audit/AuditRunsList.tsx',
    'src/components/audit/AuditIssuesPage.tsx',
    'src/components/audit/AuditIssueDetailPage.tsx',
    'src/components/audit/AuditRunsPage.tsx'
  ];
  
  for (const file of uiFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`UI component exists: ${path.basename(file)}`, exists);
    
    if (exists) {
      try {
        const content = fs.readFileSync(path.join(process.cwd(), file), 'utf8');
        
        if (file.includes('/page.tsx')) {
          const hasServerSession = content.includes('getServerSession');
          const hasRequireAdmin = content.includes('requireAdmin');
          const hasRedirect = content.includes('redirect');
          logTest(`${path.basename(file)} has server session check`, hasServerSession);
          logTest(`${path.basename(file)} requires admin access`, hasRequireAdmin);
          logTest(`${path.basename(file)} has redirect logic`, hasRedirect);
        } else {
          const hasUseState = content.includes('useState');
          const hasUseEffect = content.includes('useEffect');
          const hasFetchLogic = content.includes('fetch(');
          logTest(`${path.basename(file)} has state management`, hasUseState);
          logTest(`${path.basename(file)} has lifecycle hooks`, hasUseEffect);
          logTest(`${path.basename(file)} has API integration`, hasFetchLogic);
        }
        
      } catch (error) {
        logTest(`${path.basename(file)} content validation`, false, error.message);
      }
    }
  }
}

async function testNavigationIntegration() {
  console.log('\n🧭 Testing Navigation Integration...');
  
  try {
    // Test navigation bar
    const navContent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/layout/NavigationBar.tsx'), 
      'utf8'
    );
    
    const hasAuditLink = navContent.includes('/audit');
    const hasBugIcon = navContent.includes('Bug');
    const hasAdminCheck = navContent.includes('isAdmin');
    
    logTest('Navigation has audit link', hasAuditLink);
    logTest('Navigation has audit icon', hasBugIcon);
    logTest('Navigation has admin check', hasAdminCheck);
    
    // Test middleware
    const middlewareContent = fs.readFileSync(
      path.join(process.cwd(), 'middleware.ts'), 
      'utf8'
    );
    
    const hasAuditRoute = middlewareContent.includes('/audit');
    const hasAdminRoutes = middlewareContent.includes('adminRoutes');
    const hasAuditAPI = middlewareContent.includes('/api/audit');
    
    logTest('Middleware protects audit routes', hasAuditRoute);
    logTest('Middleware has admin route protection', hasAdminRoutes);
    logTest('Middleware protects audit API', hasAuditAPI);
    
    // Test navigation state
    const navStateContent = fs.readFileSync(
      path.join(process.cwd(), 'src/hooks/useNavigationState.ts'), 
      'utf8'
    );
    
    const hasAuditTitles = navStateContent.includes('/audit');
    logTest('Navigation state includes audit pages', hasAuditTitles);
    
  } catch (error) {
    logTest('Navigation integration validation', false, error.message);
  }
}

async function testDatabaseSchema() {
  console.log('\n🗄️ Testing Database Schema...');
  
  try {
    const schemaContent = fs.readFileSync(
      path.join(process.cwd(), 'prisma/schema.prisma'), 
      'utf8'
    );
    
    const hasAuditRun = schemaContent.includes('model AuditRun');
    const hasAuditIssue = schemaContent.includes('model AuditIssue');
    const hasIssueComment = schemaContent.includes('model IssueComment');
    const hasIssueSeverity = schemaContent.includes('enum IssueSeverity');
    const hasIssueCategory = schemaContent.includes('enum IssueCategory');
    const hasIssueStatus = schemaContent.includes('enum IssueStatus');
    const hasAuditStatus = schemaContent.includes('enum AuditStatus');
    
    logTest('Schema has AuditRun model', hasAuditRun);
    logTest('Schema has AuditIssue model', hasAuditIssue);
    logTest('Schema has IssueComment model', hasIssueComment);
    logTest('Schema has IssueSeverity enum', hasIssueSeverity);
    logTest('Schema has IssueCategory enum', hasIssueCategory);
    logTest('Schema has IssueStatus enum', hasIssueStatus);
    logTest('Schema has AuditStatus enum', hasAuditStatus);
    
    // Check relationships
    const hasRunIssueRelation = schemaContent.includes('issues        AuditIssue[]');
    const hasIssueRunRelation = schemaContent.includes('auditRun       AuditRun');
    const hasIssueCommentRelation = schemaContent.includes('comments       IssueComment[]');
    
    logTest('Schema has run-issue relationship', hasRunIssueRelation);
    logTest('Schema has issue-run relationship', hasIssueRunRelation);
    logTest('Schema has issue-comment relationship', hasIssueCommentRelation);
    
  } catch (error) {
    logTest('Database schema validation', false, error.message);
  }
}

async function runAPITests() {
  console.log('🚀 Starting Comprehensive Audit API Testing...');
  console.log('=' .repeat(60));
  
  await testAPIRouteFiles();
  await testCoreAuditFiles();
  await testAnalyzerFiles();
  await testUIComponents();
  await testNavigationIntegration();
  await testDatabaseSchema();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🎉 API Testing Complete!');
}

// Run the tests
runAPITests().catch(console.error);
