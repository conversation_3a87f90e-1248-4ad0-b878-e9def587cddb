#!/usr/bin/env node

/**
 * Comprehensive Audit System Testing Script
 * 
 * This script thoroughly tests all aspects of the audit system:
 * 1. Database schema validation
 * 2. API endpoints testing
 * 3. Core audit engine functionality
 * 4. UI component integration
 * 5. End-to-end workflows
 * 6. Security and access controls
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

async function testDatabaseSchema() {
  console.log('\n🗄️  Testing Database Schema...');
  
  try {
    // Test 1: Check if AuditRun table exists and has correct structure
    const auditRunCount = await prisma.auditRun.count();
    logTest('AuditRun table exists', true, `Found ${auditRunCount} records`);
    
    // Test 2: Check if AuditIssue table exists and has correct structure
    const auditIssueCount = await prisma.auditIssue.count();
    logTest('AuditIssue table exists', true, `Found ${auditIssueCount} records`);
    
    // Test 3: Check if IssueComment table exists
    const commentCount = await prisma.issueComment.count();
    logTest('IssueComment table exists', true, `Found ${commentCount} records`);
    
    // Test 4: Test creating a sample audit run
    const testRun = await prisma.auditRun.create({
      data: {
        status: 'PENDING',
        totalIssues: 0,
        criticalCount: 0,
        highCount: 0,
        mediumCount: 0,
        lowCount: 0,
        triggeredBy: 'test-system'
      }
    });
    logTest('Can create AuditRun', true, `Created run ${testRun.id}`);
    
    // Test 5: Test creating a sample issue
    const testIssue = await prisma.auditIssue.create({
      data: {
        auditRunId: testRun.id,
        severity: 'MEDIUM',
        category: 'TESTING',
        title: 'Test Issue for System Validation',
        description: 'This is a test issue created during system validation',
        filePath: '/test/validation.ts',
        lineNumber: 42,
        status: 'OPEN'
      }
    });
    logTest('Can create AuditIssue', true, `Created issue ${testIssue.id}`);
    
    // Test 6: Test relationships work
    const runWithIssues = await prisma.auditRun.findUnique({
      where: { id: testRun.id },
      include: { issues: true }
    });
    logTest('AuditRun-AuditIssue relationship works', runWithIssues?.issues.length === 1, 
      `Found ${runWithIssues?.issues.length} issues for run`);
    
    // Test 7: Test updating audit run with issue counts
    await prisma.auditRun.update({
      where: { id: testRun.id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        totalIssues: 1,
        mediumCount: 1
      }
    });
    logTest('Can update AuditRun status and counts', true);
    
    // Test 8: Test creating a comment
    const testUser = await prisma.user.findFirst();
    if (testUser) {
      const testComment = await prisma.issueComment.create({
        data: {
          issueId: testIssue.id,
          userId: testUser.id,
          comment: 'This is a test comment for system validation'
        }
      });
      logTest('Can create IssueComment', true, `Created comment ${testComment.id}`);
    } else {
      logTest('Can create IssueComment', false, 'No test user found');
    }
    
    // Cleanup test data
    await prisma.issueComment.deleteMany({
      where: { issueId: testIssue.id }
    });
    await prisma.auditIssue.delete({
      where: { id: testIssue.id }
    });
    await prisma.auditRun.delete({
      where: { id: testRun.id }
    });
    logTest('Test data cleanup', true);
    
  } catch (error) {
    logTest('Database schema validation', false, error.message);
  }
}

async function testCoreAuditEngine() {
  console.log('\n🔧 Testing Core Audit Engine...');
  
  try {
    // Test 1: Check if audit engine files exist
    const auditEngineFiles = [
      'src/lib/audit/core-audit-engine.ts',
      'src/lib/audit/audit-service.ts',
      'src/lib/audit/types.ts',
      'src/lib/audit/index.ts'
    ];
    
    for (const file of auditEngineFiles) {
      const exists = fs.existsSync(path.join(process.cwd(), file));
      logTest(`Core file exists: ${file}`, exists);
    }
    
    // Test 2: Check if analyzer files exist
    const analyzerFiles = [
      'src/lib/audit/analyzers/typescript-analyzer.ts',
      'src/lib/audit/analyzers/eslint-analyzer.ts',
      'src/lib/audit/analyzers/security-analyzer.ts',
      'src/lib/audit/analyzers/performance-analyzer.ts'
    ];
    
    for (const file of analyzerFiles) {
      const exists = fs.existsSync(path.join(process.cwd(), file));
      logTest(`Analyzer exists: ${path.basename(file)}`, exists);
    }
    
    // Test 3: Check if storage files exist
    const storageFiles = [
      'src/lib/audit/storage/audit-storage.ts'
    ];
    
    for (const file of storageFiles) {
      const exists = fs.existsSync(path.join(process.cwd(), file));
      logTest(`Storage file exists: ${file}`, exists);
    }
    
    // Test 4: Try to import and instantiate core components
    try {
      const { AuditService } = require('./src/lib/audit');
      const auditService = new AuditService();
      logTest('Can instantiate AuditService', true);
    } catch (error) {
      logTest('Can instantiate AuditService', false, error.message);
    }
    
  } catch (error) {
    logTest('Core audit engine validation', false, error.message);
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API Endpoints...');
  
  // Test 1: Check if API route files exist
  const apiFiles = [
    'src/app/api/audit/runs/route.ts',
    'src/app/api/audit/runs/[id]/route.ts',
    'src/app/api/audit/issues/route.ts',
    'src/app/api/audit/issues/[id]/route.ts',
    'src/app/api/audit/issues/[id]/comments/route.ts'
  ];
  
  for (const file of apiFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`API route exists: ${file}`, exists);
  }
  
  // Test 2: Check API route structure
  try {
    const runsRouteContent = fs.readFileSync(
      path.join(process.cwd(), 'src/app/api/audit/runs/route.ts'), 
      'utf8'
    );
    
    const hasGETExport = runsRouteContent.includes('export const GET');
    const hasPOSTExport = runsRouteContent.includes('export const POST');
    const hasErrorHandling = runsRouteContent.includes('withUnifiedErrorHandling');
    const hasRateLimit = runsRouteContent.includes('withRateLimit');
    const hasAuth = runsRouteContent.includes('getServerSession');
    
    logTest('Runs API has GET endpoint', hasGETExport);
    logTest('Runs API has POST endpoint', hasPOSTExport);
    logTest('Runs API has error handling', hasErrorHandling);
    logTest('Runs API has rate limiting', hasRateLimit);
    logTest('Runs API has authentication', hasAuth);
    
  } catch (error) {
    logTest('API route structure validation', false, error.message);
  }
}

async function testUIComponents() {
  console.log('\n🎨 Testing UI Components...');
  
  // Test 1: Check if UI component files exist
  const uiFiles = [
    'src/app/audit/page.tsx',
    'src/app/audit/runs/page.tsx',
    'src/app/audit/issues/page.tsx',
    'src/app/audit/issues/[id]/page.tsx',
    'src/components/audit/AuditDashboard.tsx',
    'src/components/audit/AuditMetrics.tsx',
    'src/components/audit/RecentIssues.tsx',
    'src/components/audit/AuditRunsList.tsx',
    'src/components/audit/AuditIssuesPage.tsx',
    'src/components/audit/AuditIssueDetailPage.tsx'
  ];
  
  for (const file of uiFiles) {
    const exists = fs.existsSync(path.join(process.cwd(), file));
    logTest(`UI component exists: ${path.basename(file)}`, exists);
  }
  
  // Test 2: Check component structure
  try {
    const dashboardContent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/audit/AuditDashboard.tsx'), 
      'utf8'
    );
    
    const hasUseState = dashboardContent.includes('useState');
    const hasUseEffect = dashboardContent.includes('useEffect');
    const hasFetchLogic = dashboardContent.includes('fetch(');
    const hasErrorHandling = dashboardContent.includes('catch');
    const hasLoadingState = dashboardContent.includes('loading');
    
    logTest('Dashboard has state management', hasUseState);
    logTest('Dashboard has lifecycle hooks', hasUseEffect);
    logTest('Dashboard has API integration', hasFetchLogic);
    logTest('Dashboard has error handling', hasErrorHandling);
    logTest('Dashboard has loading states', hasLoadingState);
    
  } catch (error) {
    logTest('UI component structure validation', false, error.message);
  }
}

async function testNavigationIntegration() {
  console.log('\n🧭 Testing Navigation Integration...');
  
  try {
    // Test 1: Check if navigation is updated
    const navContent = fs.readFileSync(
      path.join(process.cwd(), 'src/components/layout/NavigationBar.tsx'), 
      'utf8'
    );
    
    const hasAuditLink = navContent.includes('/audit');
    const hasBugIcon = navContent.includes('Bug');
    const hasAdminCheck = navContent.includes('isAdmin');
    
    logTest('Navigation has audit link', hasAuditLink);
    logTest('Navigation has audit icon', hasBugIcon);
    logTest('Navigation has admin check', hasAdminCheck);
    
    // Test 2: Check middleware protection
    const middlewareContent = fs.readFileSync(
      path.join(process.cwd(), 'middleware.ts'), 
      'utf8'
    );
    
    const hasAuditRoute = middlewareContent.includes('/audit');
    const hasAdminRoutes = middlewareContent.includes('adminRoutes');
    
    logTest('Middleware protects audit routes', hasAuditRoute);
    logTest('Middleware has admin route protection', hasAdminRoutes);
    
  } catch (error) {
    logTest('Navigation integration validation', false, error.message);
  }
}

async function testSecurityAndAccess() {
  console.log('\n🔒 Testing Security and Access Control...');
  
  try {
    // Test 1: Check API route security
    const apiContent = fs.readFileSync(
      path.join(process.cwd(), 'src/app/api/audit/runs/route.ts'), 
      'utf8'
    );
    
    const hasSessionCheck = apiContent.includes('getServerSession');
    const hasAdminCheck = apiContent.includes('isAdminUser') || apiContent.includes('requireAdmin');
    const hasUnauthorizedResponse = apiContent.includes('403') || apiContent.includes('Unauthorized');
    const hasCSRFProtection = apiContent.includes('withCSRFProtection');
    
    logTest('API has session validation', hasSessionCheck);
    logTest('API has admin validation', hasAdminCheck);
    logTest('API returns unauthorized responses', hasUnauthorizedResponse);
    logTest('API has CSRF protection', hasCSRFProtection);
    
    // Test 2: Check page-level security
    const pageContent = fs.readFileSync(
      path.join(process.cwd(), 'src/app/audit/page.tsx'), 
      'utf8'
    );
    
    const hasServerSession = pageContent.includes('getServerSession');
    const hasRedirect = pageContent.includes('redirect');
    const hasRequireAdmin = pageContent.includes('requireAdmin');
    
    logTest('Page has server session check', hasServerSession);
    logTest('Page has redirect logic', hasRedirect);
    logTest('Page requires admin access', hasRequireAdmin);
    
  } catch (error) {
    logTest('Security validation', false, error.message);
  }
}

async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Audit System Testing...');
  console.log('=' .repeat(60));
  
  await testDatabaseSchema();
  await testCoreAuditEngine();
  await testAPIEndpoints();
  await testUIComponents();
  await testNavigationIntegration();
  await testSecurityAndAccess();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🎉 Comprehensive Testing Complete!');
  
  await prisma.$disconnect();
}

// Run the tests
runComprehensiveTests().catch(console.error);
