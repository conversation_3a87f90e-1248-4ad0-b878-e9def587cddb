#!/usr/bin/env node

/**
 * Live Audit Endpoints Testing Script
 * 
 * Tests the audit API endpoints against a running development server
 */

const https = require('https');
const http = require('http');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.request(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Audit-Test-Script/1.0',
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

async function testServerAvailability() {
  console.log('\n🌐 Testing Server Availability...');
  
  try {
    const response = await makeRequest('http://localhost:3000/');
    logTest('Development server is running', 
      response.statusCode === 200 || response.statusCode === 404,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('Development server is running', false, error.message);
    return false;
  }
  
  return true;
}

async function testAuditAPIEndpoints() {
  console.log('\n🔧 Testing Audit API Endpoints...');
  
  const baseUrl = 'http://localhost:3000';
  
  // Test 1: GET /api/audit/runs (should require auth)
  try {
    const response = await makeRequest(`${baseUrl}/api/audit/runs`);
    logTest('GET /api/audit/runs responds', 
      response.statusCode === 401 || response.statusCode === 403 || response.statusCode === 200,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('GET /api/audit/runs responds', false, error.message);
  }
  
  // Test 2: GET /api/audit/issues (should require auth)
  try {
    const response = await makeRequest(`${baseUrl}/api/audit/issues`);
    logTest('GET /api/audit/issues responds', 
      response.statusCode === 401 || response.statusCode === 403 || response.statusCode === 200,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('GET /api/audit/issues responds', false, error.message);
  }
  
  // Test 3: POST /api/audit/runs (should require auth and admin)
  try {
    const response = await makeRequest(`${baseUrl}/api/audit/runs`, {
      method: 'POST',
      body: {
        enabledAnalyzers: ['typescript', 'eslint'],
        categories: ['SECURITY', 'PERFORMANCE']
      }
    });
    logTest('POST /api/audit/runs requires auth', 
      response.statusCode === 401 || response.statusCode === 403,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('POST /api/audit/runs requires auth', false, error.message);
  }
  
  // Test 4: Test invalid endpoints
  try {
    const response = await makeRequest(`${baseUrl}/api/audit/invalid`);
    logTest('Invalid audit endpoint returns 404', 
      response.statusCode === 404,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('Invalid audit endpoint returns 404', false, error.message);
  }
}

async function testAuditUIPages() {
  console.log('\n🎨 Testing Audit UI Pages...');
  
  const baseUrl = 'http://localhost:3000';
  
  // Test 1: GET /audit (should redirect to login or require admin)
  try {
    const response = await makeRequest(`${baseUrl}/audit`);
    logTest('GET /audit page responds', 
      response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 401,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('GET /audit page responds', false, error.message);
  }
  
  // Test 2: GET /audit/runs
  try {
    const response = await makeRequest(`${baseUrl}/audit/runs`);
    logTest('GET /audit/runs page responds', 
      response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 401,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('GET /audit/runs page responds', false, error.message);
  }
  
  // Test 3: GET /audit/issues
  try {
    const response = await makeRequest(`${baseUrl}/audit/issues`);
    logTest('GET /audit/issues page responds', 
      response.statusCode === 200 || response.statusCode === 302 || response.statusCode === 401,
      `Status: ${response.statusCode}`);
  } catch (error) {
    logTest('GET /audit/issues page responds', false, error.message);
  }
}

async function testSecurityHeaders() {
  console.log('\n🔒 Testing Security Headers...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    const response = await makeRequest(`${baseUrl}/audit`);
    
    const hasCSP = response.headers['content-security-policy'];
    const hasXFrame = response.headers['x-frame-options'];
    const hasXContent = response.headers['x-content-type-options'];
    
    logTest('Has Content Security Policy', !!hasCSP);
    logTest('Has X-Frame-Options', !!hasXFrame);
    logTest('Has X-Content-Type-Options', !!hasXContent);
    
  } catch (error) {
    logTest('Security headers test', false, error.message);
  }
}

async function testRateLimiting() {
  console.log('\n⚡ Testing Rate Limiting...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Make multiple rapid requests to test rate limiting
    const requests = [];
    for (let i = 0; i < 5; i++) {
      requests.push(makeRequest(`${baseUrl}/api/audit/runs`));
    }
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.some(r => r.statusCode === 429);
    
    logTest('Rate limiting is active', 
      rateLimited || responses.every(r => r.statusCode === 401 || r.statusCode === 403),
      `Got status codes: ${responses.map(r => r.statusCode).join(', ')}`);
    
  } catch (error) {
    logTest('Rate limiting test', false, error.message);
  }
}

async function runLiveTests() {
  console.log('🚀 Starting Live Audit System Testing...');
  console.log('=' .repeat(60));
  
  const serverRunning = await testServerAvailability();
  
  if (serverRunning) {
    await testAuditAPIEndpoints();
    await testAuditUIPages();
    await testSecurityHeaders();
    await testRateLimiting();
  } else {
    console.log('⚠️  Development server not running. Skipping live tests.');
    console.log('   Start the server with: npm run dev');
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  
  if (testResults.passed + testResults.failed > 0) {
    console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  }
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Failed Tests:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n🎉 Live Testing Complete!');
}

// Run the tests
runLiveTests().catch(console.error);
